import { MentionItem } from "../api/contact";

export function contactCard(item: MentionItem): HTMLElement {
  const div = document.createElement("div");
  div.className = "contact-card";

  // 头像
  const avatarBlock = document.createElement("div");
  avatarBlock.className = "contact-card-avatar";
  if (item.avatars) {
    const img = document.createElement("img");
    img.src = item.avatars;
    img.alt = item.value || "";
    img.className = "contact-card-avatar-img";
    avatarBlock.appendChild(img);
  } else {
    const avatarText = document.createElement("span");
    avatarText.textContent = (item.value || "").slice(0, 2);
    avatarText.className = "contact-card-avatar-text";
    avatarBlock.appendChild(avatarText);
  }

  // 信息
  const info = document.createElement("div");
  info.className = "contact-card-info";
  const nameDiv = document.createElement("div");
  nameDiv.textContent = item.value || "";
  nameDiv.className = "contact-card-name";
  const deptDiv = document.createElement("div");
  deptDiv.textContent = item.dept || "";
  deptDiv.className = "contact-card-dept";
  info.appendChild(nameDiv);
  info.appendChild(deptDiv);

  div.appendChild(avatarBlock);
  div.appendChild(info);
  return div;
}
