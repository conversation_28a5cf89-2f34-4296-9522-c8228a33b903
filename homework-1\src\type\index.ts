export interface FileOperationRecord {
  app_type: string; // 应用类型，例如 "otl"
  b64fname: string; // Base64编码的文件名
  collection_time: number; // 收集时间戳
  corpid: number; // 企业ID
  ctime: number; // 创建时间戳
  current_device_id: string; // 当前设备ID
  current_device_name: string; // 当前设备名称
  current_device_type: string; // 当前设备类型
  deleted: number; // 删除标记 (0/1)
  external: string; // 外部信息
  file_ctime: number; // 文件创建时间戳
  file_src: string; // 文件来源描述
  file_src_type: string; // 文件来源类型
  file_type: string; // 文件类型
  fileid: string; // 文件ID
  group_type: string; // 群组类型
  groupid: number; // 群组ID
  is_tmp: number; // 是否临时文件 (0/1)
  isshare: boolean; // 是否共享
  mtime: number; // 修改时间戳
  name: string; // 文件名
  operation: string; // 操作类型
  opv: number; // 操作版本
  original_device_id: string; // 原始设备ID
  original_device_name: string; // 原始设备名称
  original_device_type: string; // 原始设备类型
  path: string; // 文件路径
  roamingid: string; // 漫游ID
  size: number; // 文件大小（字节）
  status: string; // 状态
  summary: string; // 摘要
  thumbnail: string; // 缩略图路径
  userid: string; // 用户ID
}

export interface FileMentionValue {
  name: string;
  fileId: string;
}

export interface FetchFilePermissionParams {
  fileid: string;
  chatid: string | number;
  cid: string | number;
}

export interface fileResponse {
  successes: {
    corpid: number;
    deleted: number;
    drive_id: string;
    encipherer: number;
    ext_perm_list: null;
    file_creator: number;
    file_name: string;
    file_size: number;
    file_source: number;
    file_status: number;
    fileid: number;
    fsha: string;
    group_permission: string;
    groupid: number;
    link_creator: number;
    link_type: string;
    link_url: string;
    linkid: string;
    mtime: number;
    permission: string;
    range: string;
    receiver_permission: string;
    securityFlag: boolean;
    sender_permission: string;
    share_permission: number;
    short_link_url: string;
    status: string;
    user_ext_perm: null;
    user_permission: string;
  };
}

//联系人接口请求参数
export interface ChatSearchParams {
  keyword?: string;
  page_token?: string;
  count?: number;
  search_type?: number;
  search_scopes?: number[];
  relation_version?: number;
}

export interface ContactResponse {
  contacts: {
    next_page_token: string;
    list: Contact[];
  };
}

export interface Contact {
  corpid: number;
  id: number;
  chatid: number;
  avatars: string[];
  highlight: Record<string, unknown>;
  dept: string;
  department_show: string;
  dept_id_path: string;
  abs_dept_id_path: string;
  abs_dept: string;
  name: string;
  remark_name: string;
  avatar: {
    list: string[];
  };
  chat_type: number;
  relation: {
    ext_contact_id: number;
    dept_path: string;
    relation_type: "corp" | "partners" | string;
  };
  email: string;
  typ: string;
  status: "active" | string;
  is_external: boolean;
  department_id: string;
  is_all_staff: boolean;
  group_owner_user_id: number;
  group_owner_user_name: string;
  group_owner_user_email: string;
  group_class: number;
  member_num: number;
  update_time: number;
  disable_msg_status: number;
  data_source: number;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  fields: any[];
}

//尝试设置不同的renderlist渲染，但是没生效---查资料说是不支持，需要进一步再看看，一定要加的话是用假数据，设为第一个数据项（pass掉这个方式）
//下拉项
export interface ContactMentionItem {
  id: number | string;
  value: string; // 姓名
  avatars?: string; // 头像
  dept?: string; // 部门
  pinyin?: string; // 拼音首字母
  denotationChar: "@";
}

export interface FileMentionItem {
  id: number | string;
  value?: string; // 文件名
  name?: string; // 文件名（有的接口用name）
  fileid?: string | number;
  file_ctime?: number;
  file_src?: string;
  path?: string;
  denotationChar: "#";
}

export type MentionDropdownItem = ContactMentionItem | FileMentionItem;
