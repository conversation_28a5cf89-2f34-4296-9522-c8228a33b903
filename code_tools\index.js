// IP + Host伪装域名请求
const fs = require("fs");
const path = require("path");
const axios = require("axios");
const https = require("https");
const net = require("net");
const BASE_IP = "**************";
const HOST_HEADER = "kfpxy-fe.wps.cn";

//husky line-stage测试
const a = "test";
const BASE_URL = `https://${BASE_IP}`;

// 判断字符串是否是 IP 地址
function isIPAddress(str) {
  return net.isIP(str) !== 0;
}

// 自动配置 httpsAgent,避免每次写死servername
// 根据是否是 IP 地址智能设置servername
function createHttpsAgent(baseURL, headersHost) {
  const urlObj = new URL(baseURL);
  const hostname = urlObj.hostname;

  if (isIPAddress(hostname) && headersHost) {
    return new https.Agent({
      servername: headersHost // 设置 SNI 为 Host
    });
  } else {
    return new https.Agent(); // 默认行为即可
  }
}

// 创建忽略证书校验的 HTTPS Agent,去除也可以访问成功，但是查资料说在使用ip伪装访问时，这个agent最好设置
// const agent = new https.Agent({
//   rejectUnauthorized: false, //不要在生产环境使用
//   servername: HOST_HEADER //明确指定 SNI
// });

// 去除字符串中的所有数字
function removeDigits(str) {
  return str.replace(/\d+/g, "");
}

async function checkRoute() {
  try {
    const names = fs
      .readFileSync(path.resolve(__dirname, "name.txt"), "utf-8")
      .split(/\r?\n/)
      .map((line) => line.trim())
      .filter(Boolean);

    for (const name of names) {
      const routesToCheck = [
        // `/${name}/`,
        `/${removeDigits(name)}/`
      ];

      const agent = createHttpsAgent(BASE_URL, HOST_HEADER);

      for (const route of routesToCheck) {
        const url = `${BASE_URL}${route}`;
        console.log(`🚀 正在请求: ${url} (Host: ${HOST_HEADER})`);

        try {
          const response = await axios.get(url, {
            validateStatus: () => true,
            httpsAgent: agent,
            headers: {
              Host: HOST_HEADER,
              "User-Agent":
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 Chrome/114.0 Safari/537.36"
            }
          });

          if (response.status === 200) {
            console.log(`✅ 路由有效: ${route} → HTTP ${response.status}`);
          } else {
            console.warn(`❌ 路由无效: ${route} → HTTP ${response.status}`);
          }
        } catch (err) {
          console.error(`❌ 请求失败: ${route}, 错误: ${err.message}`);
        }
      }
    }
  } catch (error) {
    console.error("❌ 脚本运行失败:", error.message);
    process.exit(1);
  }
}

checkRoute();
