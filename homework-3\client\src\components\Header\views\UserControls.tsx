import { Popover } from "radix-ui"
import { AvatarHoverCard } from "../../common/AvatarCard"
import { ShareIcon } from "../../icons/ShareIcon"
import "../../common/css/header.css"

export const UserControls = () => {
  return (
    <div className="user-controls">
      <Popover.Root>
        <Popover.Trigger asChild>
          <button className="share-btn">
            <ShareIcon />
            <span>分享</span>
          </button>
        </Popover.Trigger>
        <Popover.Portal>
          <Popover.Content sideOffset={5}>
            <div className="share-panel">分享块</div>
          </Popover.Content>
        </Popover.Portal>
      </Popover.Root>
      <AvatarHoverCard
        src="https://images.unsplash.com/photo-1511485977113-f34c92461ad9?ixlib=rb-1.2.1&w=128&h=128&dpr=2&q=80"
        fallbackText="AB"
        name="<PERSON>"
        email="<EMAIL>"
        onProfileClick={() => alert("查看资料")}
      />
    </div>
  )
}
