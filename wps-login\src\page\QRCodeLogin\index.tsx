import wxLogo from "../../assets/wxlogo.png";
import Common from "../../components/Common";
import IconButtonGroup from "../../components/IconButtonGroup";
import { loginMethodConfigs } from "../../components/IconButtonGroup/loginIcons";
import { type LoginMethod } from "../../constants";
import "./index.css";

interface QRCodeLoginProps {
  onCurrentBtn: (value: LoginMethod) => void;
}

function QRCodeLogin({ onCurrentBtn }: QRCodeLoginProps) {
  const handleCurrentBtn = (value: LoginMethod) => {
    console.log("handleCurrentBtn被调用", value);
    onCurrentBtn(value);
  };

  return (
    <Common
      isShowBack={false}
      title="微信扫码登录"
      subTitle="使用金山办公在线服务账号登录"
      fchildren={
        <div className="login_options">
          <div className="checkbox-group">
            <div className="checkbox-div">
              <label className="checkbox-item">
                <input type="checkbox" />
                <span>自动登录</span>
              </label>
            </div>
            <div className="checkbox-div">
              <label className="checkbox-item">
                <input type="checkbox" />
                <span>
                  已阅读并同意
                  <a href="#">隐私保护政策</a>和<a href="#">在线服务协议</a>
                </span>
              </label>
            </div>
          </div>

          <IconButtonGroup
            dividerText=""
            buttons={[
              {
                ...loginMethodConfigs.qq,
                onClick: (id) => handleCurrentBtn(id as LoginMethod)
              },
              {
                ...loginMethodConfigs.phone,
                onClick: (id) => handleCurrentBtn(id as LoginMethod)
              },
              {
                ...loginMethodConfigs.sso,
                onClick: (id) => handleCurrentBtn(id as LoginMethod)
              },
              {
                ...loginMethodConfigs.more,
                onClick: (id) => handleCurrentBtn(id as LoginMethod)
              }
            ]}
            mobileColumns={4}
            desktopColumns={4}
          />
        </div>
      }
    >
      <div className="qr_code_section">
        <div className="qr_code_container">
          <img src={wxLogo} />
        </div>
      </div>
    </Common>
  );
}

export default QRCodeLogin;
