先写测试用例（用中文），然后根据测试用例再写单测代码

## 组件测试用例

## TodoApp 测试

### **1.基础渲染测试**

- 渲染基本结构（标题、输入框、统计信息）
- 渲染过滤器按钮
- 默认选中'全部'过滤器

### 2. **交互功能测试**

- 点击过滤器按钮切换过滤条件
- 处理添加待办事项
- 处理切换待办事项状态
- 处理删除待办事项
- 处理编辑待办事项（console.log）
- 处理清除已完成项
- 处理取消编辑

### 3. **状态管理测试**

- 默认显示日期
- 传递 showDate 属性给 TodoItem
- 渲染待办事项列表

### 4. **条件渲染测试**

- 显示空状态消息
- 显示不同过滤器的空状态消息

### 5. **工具函数调用测试**

- 过滤器正确调用 filterTodos
- 正确调用 getTodoStats
- 正确调用 sortTodos

### 6. **副作用测试**

- 是否自动聚焦输入框

### 7. **成功回调测试**

- 添加待办事项后的处理
- 删除待办事项后的处理
- 功清除已完成项后的处理

## TodoInput 组件测试用例

### 1. **渲染测试**

- 应该渲染一个输入框和一个按钮。

### 2. **输入与按钮状态**

- 输入有效内容时，按钮应可点击。
- 输入无效内容（如全空格或空字符串）时，按钮应禁用。

### 3. **提交行为**

- 输入有效内容并点击按钮，应调用 onSubmit 回调，并清空输入框。
- 输入有效内容并按 Enter 键，应调用 onSubmit 回调，并清空输入框。

### 4. **取消行为**

- 输入内容后按 ESC 键，应调用 onCancel 回调，并清空输入框。

### 5. **自定义属性**

- 可以自定义按钮文本和输入框 placeholder。

### 6. **最大长度限制**

- 输入内容超过 maxLength 时，输入框内容应被截断。

### 7. **ref 方法测试**

- 通过 ref 调用 focus 方法应使输入框聚焦 focus() 被调用。
- 通过 ref 调用 clear 方法应清空输入框 clear() 执行后，输入框内容为空。

### 8. onCancel 可选参数测试\*\*

- 当 onCancel 未提供时，按 ESC 应只清空输入框；输入 "任务" 后按 ESC，输入框值变为空。

## TodoItem 组件测试用例

- 应该正常渲染内容
- 勾选切换完成状态，触发 onToggle
- 点击删除按钮触发 onDelete
- 点击编辑按钮触发 onEdit
- showDate 为 true 时展示日期
- 已完成时有 completed 样式
- 删除/编辑按钮阻止事件冒泡

## TodoStats 组件测试用例

- 正常渲染统计信息
- stats 为 null 时显示默认值
- 完成率仅在 total>0 时显示
- 清除已完成按钮可用/禁用状态
- 点击清除已完成按钮触发 onClearCompleted

## utils 工具函数测试用例【todoUtils.js 测试】

### **1. alidateTodoText 函数**

- 空字符串和非字符串类型
- 只包含空格的字符串
- 正常文本验证
- 长度限制（超过 200 字符和等于 200 字符）

### **2. generateTodoId 函数**

- 生成唯一数字 ID

### **3. formatTodoDate 函数**

- 无参数和 null 参数
- 格式化 Date 对象
- 处理数字类型时间戳
- 处理无效日期

### **4. filterTodos 函数**

- 默认返回全部
- 过滤活跃项目
- 过滤已完成项目

### **5. sortTodos 函数**

- 默认按创建时间降序
- 按文本排序
- 按完成状态排序
- 处理缺失 createdAt 的情况
- 处理 null/undefined 的 createdAt

### **6. getTodoStats 函数**

- 统计总数、已完成、未完成和百分比
- 空数组的边界情况

### 边界情况和错误处理

### **数据类型处理**

- 数字时间戳转换为 Date 对象
- 无效日期的错误处理
- null/undefined 值的安全处理

### **边界值测试**

- 最大长度限制（200 字符）
- 空数组和空值处理
- 无效输入的容错处理

## hooks 测试用例【useTodoStore.js】

### **1. 初始化状态**

- 初始 todos 应为空，todos 等于 []。

### **2. 添加任务**

- addTodo 可以添加新任务，并去除首尾空格,todos 长度为 1，新任务文本为 "测试"。
- addTodo 传入空字符串（仅空格）时无效，todos 长度仍为 0。

### **3. 切换完成状态**

- toggleTodo 可以切换任务完成状态，第一次切换后 completed 为 true，再次切换为 false。
- toggleTodo 对不存在的 ID 无效果，todos 保持不变。

### **4. 删除任务**

- deleteTodo 可以删除指定 ID 的任务，删除后 todos 长度为 0。
- deleteTodo 对不存在的 ID 无效果，todos 长度不变。

### **5. 清除已完成任务**

- clearCompleted 可以清除所有完成的任务，剩下未完成的任务，数量正确。
- ：clearCompleted 在没有完成任务时无效果，todos 长度不变。

### **6. 更新任务**

- updateTodo 可以更新任务文本和完成状态，任务文本变为 "new"，completed 为 true。
- updateTodo 对不存在的 ID 无效果，原任务保持不变。

### **7. 统计功能**

- uncompletedCount、completedCount、totalCount 统计正确，未完成数量、已完成数量、总数符合添加和切换后的状态。

### **8. 筛选功能**

- filteredTodos 可以按条件返回任务列表
  - "all" 返回所有任务
  - "active" 返回未完成任务
  - "completed" 返回已完成任务。
- filteredTodos 对未知条件返回全部任务，返回所有任务；未传条件时也返回所有任务。
