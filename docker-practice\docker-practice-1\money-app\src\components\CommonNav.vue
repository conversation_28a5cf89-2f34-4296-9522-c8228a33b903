<script setup lang="ts">
import { RouterLink } from 'vue-router'
import { useMoney } from '@/hooks/useMoney'

const { activateTag, category } = useMoney()

const handleClick = () => {
  activateTag.value = 1
  category.value = 'collect'
}
</script>

<template>
  <nav class="wrapper">
    <RouterLink to="/note">
      <div class="icon-col">
        <svg
          t="1724636365194"
          class="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="4147"
          width="32"
          height="32"
        >
          <path
            d="M607.4 726.7c0 15.6 12.7 28.3 28.3 28.3h295.6c15.6 0 28.3-12.6 28.3-28.3s-12.7-28.3-28.3-28.3H635.7c-15.6 0.1-28.3 12.7-28.3 28.3zM931.2 885.9h-454c-15.6 0-28.3 12.6-28.3 28.3 0 15.6 12.7 28.3 28.3 28.3h454.1c15.6 0 28.3-12.6 28.3-28.3-0.1-15.7-12.8-28.3-28.4-28.3zM804.9 214.2L665.2 97c-12.3-10.3-27.3-15.4-42.2-15.4-18.7 0-37.4 8-50.4 23.5L69 705.3c-3 3.6-4.6 8.2-4.4 12.9L72 926.3c0.3 9.1 7.8 16 16.6 16 0.8 0 1.5-0.1 2.3-0.2l206.5-28.6c4.7-0.6 9-3 12-6.6L813 306.7c23.3-27.8 19.7-69.2-8.1-92.5zM275.3 859.6l-148.5 20.6-5.3-149.6 392.2-467.5L668.5 391 275.3 859.6z m494.4-589.2l-64.9 77.3-154.7-127.8 65.9-78.5c2.4-2.9 5.5-3.3 7.1-3.3 2.2 0 4.2 0.7 5.9 2.1l139.7 117.2c3.7 3.3 4.2 9.1 1 13z"
            p-id="4148"
            fill="#707070"
          ></path></svg
        ><span>记账</span>
      </div>
    </RouterLink>
    <RouterLink to="/show" @click="handleClick">
      <div class="icon-col">
        <svg
          t="1724636543175"
          class="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="5495"
          width="32"
          height="32"
        >
          <path
            d="M128 128h320v320H128zM544 128h352v96H544zM544 352h352v96H544zM128 576h320v320H128zM544 576h352v96H544zM544 800h352v96H544z"
            p-id="5496"
            fill="#707070"
          ></path></svg
        ><span>明细</span>
      </div></RouterLink
    >
    <RouterLink to="/collect" @click="handleClick">
      <div class="icon-col">
        <svg
          t="1724636588228"
          class="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="6888"
          width="32"
          height="32"
        >
          <path
            d="M691.2 928.2V543.1c0-32.7 26.5-59.3 59.2-59.3h118.5c32.7 0 59.3 26.5 59.3 59.2V928.2h-237z m192.6-385.1c0-8.2-6.6-14.8-14.8-14.8H750.5c-8.2 0-14.8 6.6-14.9 14.7v340.8h148.2V543.1zM395 157.8c-0.1-32.6 26.3-59.2 58.9-59.3h118.8c32.6 0 59.1 26.5 59.1 59.1v770.6H395V157.8z m44.4 725.9h148V157.9c0-8.1-6.5-14.7-14.7-14.8H454.1c-8.1 0.1-14.7 6.7-14.7 14.8v725.8zM98.6 394.9c0-32.7 26.5-59.2 59.2-59.3h118.5c32.7-0.1 59.3 26.4 59.3 59.1v533.5h-237V394.9z m44.5 488.8h148.2V394.9c0-8.2-6.7-14.8-14.8-14.8H158c-8.2 0-14.8 6.6-14.9 14.7v488.9z"
            p-id="6889"
            fill="#707070"
          ></path></svg
        ><span>统计</span>
      </div>
    </RouterLink>
  </nav>
</template>

<style scoped>
.wrapper {
  display: flex;
  align-items: center;
  justify-content: space-around;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100vw;
  background: #e0e0e080;
  padding: 6px;
}
.icon-col svg {
  width: 20px;
  height: 20px;
}
nav a:hover {
  background-color: #e0e0e0;
}

nav a {
  color: black;
  padding: 0 1rem;
  font-size: 14px;
  text-decoration: none;
}

nav .icon-col {
  display: flex;
  flex-direction: column;
  align-items: center;
}
</style>
