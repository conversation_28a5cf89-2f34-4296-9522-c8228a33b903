body {
  font-size: 16px;
  font-family:
    -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial,
    sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  max-width: 800px;
  margin: 0 auto;
}

.ql-editor {
  border: 1px solid #a3a3a3;
  border-radius: 6px;
}

.ql-editor-disabled {
  border-radius: 6px;
  background-color: rgba(124, 0, 0, 0.2);
  transition-duration: 0.5s;
}

.ql-editor:focus {
  border: 1px solid #025fae;
}

.ql-mention-list-container {
  max-height: 200px;
  overflow-y: auto;
  background: white;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.ql-mention-list-container ul,
.ql-mention-list-container li {
  list-style: none !important;
  padding-left: 0;
  margin: 0;
}

.ql-file-mention {
  display: inline-flex;
  align-items: center;
  font-weight: bold;
  width: auto;
  font-size: 16px;
  height: 20px;
  color: #2563eb;
}

.ql-file-mention::before {
  content: "\1F4C4";
}

.ql-file-mention-link {
  color: #2563eb;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  height: 1.5em;
  line-height: 1.5em;
  align-items: center;
  white-space: nowrap;
}

.ql-file-mention-icon {
  font-size: 18px;
  margin-right: 4px;
}

.ql-file-mention-text {
  font-size: 18px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ql-file-mention a {
  color: inherit;
  text-decoration: inherit;
}

.ql-mention {
  background: #f3f6fb;
  color: black;
  border-radius: 4px;
  padding: 2px 6px;
  margin: 0 2px;
  font-weight: bold;
  display: inline-block;
}

.custom-mention-item {
  cursor: pointer;
}

.custom-mention-item:hover {
  background-color: #f0f0f0;
}

.my-mention-item {
  padding: 8px 12px;
  font-size: 14px;
  cursor: pointer;
}

.my-mention-item:hover {
  background: #f0f0f0;
}

.my-mention-item a {
  color: #28a745;
  text-decoration: underline;
}

.my-mention-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  cursor: pointer;
}

.my-mention-card:hover {
  background: #f5f6fa !important;
}

.ql-contact-mention {
  display: inline-flex;
  font-size: 16px;
  border-radius: 3px;
  background-color: #f6faff;
  padding-left: 20px;
  background: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4NCjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNOCAxQzYuMzQzMTUgMSA1IDIuMzQzMTUgNSA0QzUgNS42NTY4NSA2LjM0MzE1IDcgOCA3QzkuNjU2ODUgNyAxMSA1LjY1Njg1IDExIDRDMTEgMi4zNDMxNSA5LjY1Njg1IDEgOCAxWk02IDRDNiAyLjg5NTQzIDYuODk1NDMgMiA4IDJDOS4xMDQ1NyAyIDEwIDIuODk1NDMgMTAgNEMxMCA1LjEwNDU3IDkuMTA0NTcgNiA4IDZDNi44OTU0MyA2IDYgNS4xMDQ1NyA2IDRaTTYuNSA4QzQuMDgyNjIgOCAyIDkuNzE3ODEgMiAxMS45NzgzVjEzLjIzOTFDMiAxMy43MjI3IDIuNDI2MDcgMTQgMi44IDE0SDEzLjJDMTMuNTczOSAxNCAxNCAxMy43MjI3IDE0IDEzLjIzOTFWMTEuOTc4M0MxNCA5LjcxNzgxIDExLjkxNzQgOCA5LjUgOEg2LjVaTTMgMTEuOTc4M0MzIDEwLjM5NjggNC40OTkxIDkgNi41IDlIOS41QzExLjUwMDkgOSAxMyAxMC4zOTY4IDEzIDExLjk3ODNWMTNIM1YxMS45NzgzWiIgZmlsbD0iIzM1NjRGMCIvPg0KPC9zdmc+DQo=")
    0 50% no-repeat;
  background-size: 16px 16px;
  color: #2563eb;
  font-weight: bold;
  vertical-align: middle;
}

/* 联系人卡片样式 */
.contact-card {
  display: flex;
  align-items: center;
  background: #f6f6f6;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 6px;
  width: 320px;
}

.contact-card-avatar {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  flex-shrink: 0;
  overflow: hidden;
}

.contact-card-avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.contact-card-avatar-text {
  color: #fff;
  font-weight: bold;
  font-size: 18px;
}

.contact-card-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.contact-card-name {
  font-weight: bold;
  font-size: 17px;
  color: #222;
}

.contact-card-dept {
  font-size: 15px;
  color: #b0b0b0;
  margin-top: 4px;
}

/* 文件卡片样式 */
.file-card {
  display: flex;
  align-items: center;
  text-decoration: none;
  padding: 10px 12px;
  border-radius: 8px;
  margin-bottom: 6px;
  background: #fff;
  transition: background 0.2s;
}

.file-card:hover {
  background: #f5f6fa;
}

.file-card-icon {
  display: inline-flex;
  margin-right: 12px;
  flex: none;
}

.file-card-content {
  flex: 1;
}

.file-card-title {
  font-weight: bold;
  font-size: 15px;
  color: #222;
}

.file-card-meta {
  font-size: 12px;
  color: #888;
  margin-top: 4px;
}
