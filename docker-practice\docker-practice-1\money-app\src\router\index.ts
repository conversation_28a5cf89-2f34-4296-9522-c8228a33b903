import { createRouter, createWebHashHistory } from 'vue-router'

const router = createRouter({
  history: createWebHashHistory('/zhouxinyi/'),
  routes: [
    {
      path: '/',
      redirect: '/note'
    },
    {
      path: '/note',
      name: 'note',
      component: () => import('@/views/Note/NoteView.vue')
    },
    {
      path: '/show',
      name: 'show',
      component: () => import('@/views/Show/ShowView.vue')
    },
    {
      path: '/collect',
      name: 'collect',
      component: () => import('@/views/Collect/CollectView.vue')
    },
  ]
})

export default router
