import { createSlice, type PayloadAction } from "@reduxjs/toolkit"
import type { EditorState, NodeStyle } from "../types"
import { getDefaultNodeStyle } from "../utils/nodeUtils"

const initialState: EditorState = {
  isEditingNode: null,
  editingText: "",
  showStylePanel: null,
  editingStyle: getDefaultNodeStyle()
}

const editorSlice = createSlice({
  name: "editor",
  initialState,
  reducers: {
    startEditNode: (
      state,
      action: PayloadAction<{ nodeId: string; text: string; style?: NodeStyle }>
    ) => {
      const { nodeId, text, style } = action.payload
      state.isEditingNode = nodeId
      state.editingText = text
      state.showStylePanel = nodeId
      state.editingStyle = style || getDefaultNodeStyle()
    },

    setEditingText: (state, action: PayloadAction<string>) => {
      state.editingText = action.payload
    },

    setEditingStyle: (state, action: PayloadAction<NodeStyle>) => {
      state.editingStyle = action.payload
    },

    setShowStylePanel: (state, action: PayloadAction<string | null>) => {
      state.showStylePanel = action.payload
    },

    saveNodeEdit: (state) => {
      state.isEditingNode = null
      state.editingText = ""
      // 不要自动隐藏样式面板，让用户手动点击其他地方关闭
    },

    cancelNodeEdit: (state) => {
      state.isEditingNode = null
      state.editingText = ""
      // 不要自动隐藏样式面板
    }
  }
})

export const {
  startEditNode,
  setEditingText,
  setEditingStyle,
  setShowStylePanel,
  saveNodeEdit,
  cancelNodeEdit
} = editorSlice.actions

export default editorSlice.reducer
