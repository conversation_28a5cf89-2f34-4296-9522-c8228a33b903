/// <reference types="vite/client" />

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module '*.jsx' {
  import type { ComponentType } from 'react'
  const component: ComponentType<any>
  export default component
}

declare module '*.tsx' {
  import type { ComponentType } from 'react'
  const component: ComponentType<any>
  export default component
} 