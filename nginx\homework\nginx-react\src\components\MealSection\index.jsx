import "./MealSection.css"
export const MealSection = ({ section, index }) => {
  return (
    <div key={index} className="section">
      <h3 className="title">{section.category}</h3>
      <ul style={{ listStyleType: "none", padding: 0 }}>
        {section.dishes.map((dish) => (
          <li key={dish.name} style={{ marginBottom: "10px" }}>
            <span style={{ fontWeight: "bold" }}>{dish.name}</span> - ¥
            {dish.price.toFixed(2)}
            {dish.intro && <span> ({dish.intro})</span>}
          </li>
        ))}
      </ul>
    </div>
  )
}
