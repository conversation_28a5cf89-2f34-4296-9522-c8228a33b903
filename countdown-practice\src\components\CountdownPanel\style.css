.countdown-panel {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  min-width: 420px;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
  padding: 28px 32px 32px 32px;
  z-index: 9999;
}

.countdown-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}

.countdown-panel-title {
  font-size: 22px;
  font-weight: 500;
  color: #222;
}

.countdown-panel-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.countdown-panel-action-btn {
  cursor: pointer;
  font-size: 22px;
}

.countdown-panel-action-btn.close {
  margin-left: 4px;
}

.countdown-panel-ended {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 120px;
}

.countdown-panel-clock {
  font-size: 38px;
  color: #fa7d1a;
  margin-bottom: 8px;
}

.countdown-panel-clock {
  animation: bell-shake 0.5s infinite alternate;
}


@keyframes countdown-panel-clock {
  0% {
    transform: rotate(-15deg);
  }

  100% {
    transform: rotate(15deg);
  }
}

.countdown-panel-ended-text {
  font-size: 28px;
  font-weight: 700;
  color: #222;
  display: flex;
  align-items: center;
}

.countdown-panel-digits {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 32px;
}

.countdown-panel-digit {
  background: #f5f5f5;
  border-radius: 12px;
  width: 100px;
  height: 90px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 44px;
  font-weight: 700;
  color: #222;
}

.countdown-panel-colon {
  font-size: 40px;
  color: #888;
}

.bell-shake {
  animation: bell-shake 0.5s infinite alternate;
}

@keyframes bell-shake {
  0% {
    transform: rotate(-15deg);
  }

  100% {
    transform: rotate(15deg);
  }
}

.ok-time {
  color: '#fa7d1a';
  font-size: 32;
  margin-right: 8;
}