import Quill from "quill";
import "quill-mention/autoregister";
import { getArticleList } from "./api/article";
import {
  getContactList,
  mapContactsToMentionList,
  MentionItem
} from "./api/contact";
import { fetchFilePermission } from "./api/router";
import { ContactMentionBlot } from "./blot/ContactMentionBlot";
import { FileMentionBlot } from "./blot/FileMentionBlot";
import { contactCard } from "./render/contactCard";
import { fileCard } from "./render/fileCard";
import "./styles.css";
import {
  ContactMentionItem,
  FileMentionItem,
  fileResponse,
  MentionDropdownItem
} from "./type";
import { showContactPopup } from "./utils/contactPopup";
import { removeFileExtension } from "./utils/utils";

// 注册自定义 Blot
Quill.register(ContactMentionBlot, true);
Quill.register(FileMentionBlot, true);

//作为联系人数据的缓存
let allContacts: MentionItem[] = [];

const quill = new Quill("#editor", {
  modules: {
    mention: {
      allowedChars: /^[A-Za-z\sÅÄÖåäö]*$/,
      mentionDenotationChars: ["@", "#"],
      //想要在onselect里获取到数据属性data-，一定要添加在这里
      dataAttributes: [
        "id",
        "name",
        "value",
        "denotationChar",
        "path",
        "file_ctime",
        "file_src",
        "fileid"
      ],
      source: async function (
        searchTerm: string,
        // eslint-disable-next-line no-unused-vars
        renderList: (data: MentionDropdownItem[], searchTerm: string) => void,
        mentionChar: string
      ) {
        let values: MentionDropdownItem[] = [];
        if (mentionChar === "#") {
          values = (await getArticleList()).map((item) => ({
            id: item.fileid,
            value: item.name,
            name: item.name,
            fileid: item.fileid,
            file_ctime: item.file_ctime,
            file_src: item.file_src,
            path: item.path,
            denotationChar: "#"
          }));
          renderList(values, searchTerm);
        } else if (mentionChar === "@") {
          if (!allContacts.length) {
            const data = await getContactList();
            allContacts = mapContactsToMentionList(data);
          }
          values = allContacts.map((item) => ({
            ...item,
            denotationChar: "@"
          }));

          if (searchTerm && searchTerm.length > 0) {
            const search = searchTerm.toLowerCase();
            const matches = values.filter(
              (item) =>
                (item.value && item.value.toLowerCase().includes(search)) ||
                ("pinyin" in item &&
                  (item as ContactMentionItem).pinyin &&
                  (item as ContactMentionItem).pinyin!.includes(search))
            );
            renderList(matches, searchTerm);
          } else {
            renderList(values, searchTerm);
          }
        }
      },
      renderItem: function (item: MentionDropdownItem) {
        if (item.denotationChar === "@") {
          return contactCard(item as MentionItem);
        } else {
          return fileCard(item as FileMentionItem);
        }
      },
      onSelect: function (item: MentionDropdownItem) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const quillInstance = (this as any).quill;
        const selection = quillInstance.getSelection(true);
        let index = selection.index;
        if (item.denotationChar === "@") {
          const textBefore = quillInstance.getText(Math.max(0, index - 1), 1);
          if (textBefore === "@") {
            quillInstance.deleteText(index - 1, 1, "user");
            index = index - 1;
          }
          quillInstance.insertEmbed(
            index,
            "contactMention",
            {
              id: (item as ContactMentionItem).id,
              name: (item as ContactMentionItem).value
            },
            "user"
          );
          quillInstance.setSelection(quillInstance.getLength(), 0);
        } else {
          const fileId =
            (item as FileMentionItem).fileid || (item as FileMentionItem).id;
          const textBefore = quillInstance.getText(Math.max(0, index - 1), 1);
          if (textBefore === "#") {
            quillInstance.deleteText(index - 1, 1, "user");
            index = index - 1;
          }
          quillInstance.insertEmbed(
            index,
            "fileMention",
            {
              name: removeFileExtension(
                (item as FileMentionItem).name ||
                  (item as FileMentionItem).value ||
                  "未命名文件"
              ),
              fileId
            },
            "user"
          );
          quillInstance.setSelection(quillInstance.getLength(), 0);
        }
      }
    }
  }
});

document.addEventListener(
  "click",
  (e) => {
    const popup = document.getElementById("contact-popup");
    if (popup && !popup.contains(e.target as Node)) {
      popup.remove();
    }
  },
  true
);

//监听点击事件---用quill.on当光标在最后时，点击光标尾也会触发事件，改为监听quill.root
//通过判断 e.target 是否在 mention元素上，只在真正点击 mention才弹窗
quill.root.addEventListener("click", async (e: MouseEvent) => {
  const target = e.target as HTMLElement;
  // 1. 先判断是否点击了联系人（@）
  const contactMention = target.closest(".ql-contact-mention") as HTMLElement;
  if (contactMention) {
    // 处理联系人弹窗
    const contactId = contactMention.getAttribute("data-id");
    const contact = allContacts.find((c) => String(c.id) === contactId);
    if (contact) {
      showContactPopup(contact, contactMention);
    }
    return;
  }

  // 2. 再判断是否点击了文件（#）
  const fileMention = target.closest(".ql-file-mention") as HTMLElement;
  if (fileMention) {
    const fileId = fileMention.getAttribute("data-fileid");
    if (!fileId) {
      console.error("未获取到文件ID");
      return;
    }
    try {
      const chatid = 47995027;
      const cid = 47995027;
      const result: fileResponse = await fetchFilePermission({
        fileid: fileId,
        chatid,
        cid
      });
      if (result && result.successes.link_url) {
        window.open(result.successes.link_url, "_blank");
      } else {
        console.log("未获取到文件详情地址");
      }
    } catch (err) {
      console.log("获取文件详情失败", err);
    }
    return;
  }
});

quill.on("text-change", function () {
  quill.root.querySelectorAll("p:empty").forEach((node) => node.remove());
});

/*
quill.getLeaf(range.index) 获取的是光标当前位置的叶子节点。
当光标在 mention 元素后面时，leaf.parent.domNode 可能还是 mention 的 DOM 节点，但实际上你并没有点击 mention，只是光标在它后面。
*/
// quill.on("editor-change", async (eventName: string, ...args: any[]) => {
//   if (eventName !== "selection-change") {
//     return;
//   }
//   const [range, oldRange, source] = args;
//   console.log("range, oldRange, source", range, oldRange, source)
//   // 如果没有选择范围或者不是用户操作，则返回
//   if (!range || source !== "user") {
//     return;
//   }
//   // 获取当前选中的内容
//   const [leaf, offset] = quill.getLeaf(range.index);
//   if (
//     leaf &&
//     leaf.domNode &&
//     leaf.parent &&
//     leaf.parent.domNode &&
//     leaf.parent.domNode.classList.contains("ql-contact-mention")
//   ) {
//     const mentionElement = leaf.parent.domNode;
//     console.log("联系人进入逻辑里了，", mentionElement)
//     // 处理联系人弹窗
//     const contactId = mentionElement.getAttribute("data-id");
//     const contact = allContacts.find((c) => String(c.id) === contactId);
//     if (contact) {
//       showContactPopup(contact, mentionElement);
//     }
//     return;
//   }
//   // 检查是否点击了文件提及
//   if (leaf &&
//     leaf.domNode &&
//     leaf.parent &&
//     leaf.parent.domNode &&
//     leaf.parent.domNode.classList.contains("ql-file-mention")) {
//     const fileMention = leaf.parent.domNode;
//     const fileId = fileMention.getAttribute("data-fileid");
//     if (!fileId) {
//       console.error("未获取到文件ID");
//       return;
//     }
//     try {
//       const chatid = 4799527;
//       const cid = 47995027;
//       const result: fileResponse = await fetchFilePermission({
//         fileid: fileId,
//         chatid,
//         cid
//       });
//       if (result && result.successes.link_url) {
//         window.open(result.successes.link_url, "_blank");
//       } else {
//         console.log("未获取到文件详情地址");
//       }
//     } catch (err) {
//       console.log("获取文件详情失败", err);
//     }
//     return;
//   }
// });
