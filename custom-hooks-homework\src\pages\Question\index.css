.question h1 {
  text-align: center;
}

.question-container {
  display: flex;
  flex-direction: column;
  padding: 25px;
  justify-content: center;
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  margin: 20px;
  gap: 20px;
}

.submit-button {
  display: block;
  width: 50%;
  padding: 10px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  border-radius: 10px;
  background-color: #007bff;
  color: white;
  transition: background-color 0.2s;
  margin: 10px auto;
}


.progress-container {
  width: 100%;
  height: 20px;  
  background-color: #f3f3f3;
  margin-bottom: 40px;
  position: relative;
  border-radius: 5px;
}

.progress-bar {
  height: 100%;

  background-color: #4caf50;
  width: 0%;
  transition: width 0.5s ease-in-out;
}

.submit-button {
  margin-top: 20px;
  padding: 10px 20px;
  background-color: #4caf50;
  color: white;
  border: none;
  cursor: pointer;
}

.submit-button:hover {
  background-color: #45a049;
}