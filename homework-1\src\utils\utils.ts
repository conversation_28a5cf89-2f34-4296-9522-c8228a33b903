export function formatTimestamp(ts: number): string {
  if (ts < 1e12) ts = ts * 1000;
  const date = new Date(ts);
  const y = date.getFullYear();
  const m = (date.getMonth() + 1).toString().padStart(2, "0");
  const d = date.getDate().toString().padStart(2, "0");
  const h = date.getHours().toString().padStart(2, "0");
  const min = date.getMinutes().toString().padStart(2, "0");
  return `${y}-${m}-${d} ${h}:${min}`;
}

export function removeFileExtension(filename: string): string {
  return filename.replace(/\.[^/.]+$/, "");
}
