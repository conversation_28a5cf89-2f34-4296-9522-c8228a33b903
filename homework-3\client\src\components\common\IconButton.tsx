import * as Tooltip from "@radix-ui/react-tooltip"
import React from "react"

interface IconButtonProps {
  icon: React.ComponentType<{ className?: string }>
  label: string
  onClick?: () => void
  disabled?: boolean
  className?: string
  size?: "sm" | "md" | "lg"
}

export const IconButton: React.FC<IconButtonProps> = ({
  icon: Icon,
  label,
  onClick,
  disabled = false,
  className = "",
  size = "md"
}) => {
  return (
    <Tooltip.Root>
      <Tooltip.Trigger asChild>
        <button
          className={`icon-btn icon-btn-${size} ${className}`}
          onClick={onClick}
          disabled={disabled}
          aria-label={label}
          type="button"
        >
          <Icon />
        </button>
      </Tooltip.Trigger>
      <Tooltip.Portal>
        <Tooltip.Content
          className="tooltip-content"
          side="bottom"
          align="center"
          sideOffset={5}
        >
          {label}
          <Tooltip.Arrow className="tooltip-arrow" />
        </Tooltip.Content>
      </Tooltip.Portal>
    </Tooltip.Root>
  )
}
