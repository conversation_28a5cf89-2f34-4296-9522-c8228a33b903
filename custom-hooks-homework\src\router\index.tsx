// src/router/index.jsx
import { createBrowserRouter } from "react-router-dom"
import App from "../App"
import Answer from "../pages/Answer"
import Home from "../pages/Home"
const router = createBrowserRouter([
  {
    path: "/",
    element: <App />,
    children: [
      {
        index: true, // 默认子路由
        element: <Home />,
      },
      {
        path: "answer",
        element: <Answer />,
      },
    ],
  },
])

export default router
