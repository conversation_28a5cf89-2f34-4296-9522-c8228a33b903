<script setup lang="ts">
import { ref } from 'vue'
import categoryList from './CategoryList.vue'
import { useMoney } from '@/hooks/useMoney'
import CollectData from '@/data/collect.json'
import PayData from '@/data/pay.json'
const { category, tag, activateTag } = useMoney()

const tabs = [
  { id: 1, name: '收入', label: 'collect' },
  { id: 2, name: '支出', label: 'pay' }
]
const activeTab = ref<string>('collect')

const handelChange = (val: string) => {
  activeTab.value = val
  category.value = val
  if (val === 'collect') {
    tag.value = CollectData[0]['name']
  } else {
    tag.value = PayData[0]['name']
  }
  activateTag.value = 1
}
</script>

<template>
  <div class="header">
    <div class="tabs">
      <button
        v-for="tab in tabs"
        :key="tab.id"
        :class="[
          { colactive: activeTab === tab.label && category === 'collect' },
          { payactive: activeTab === tab.label && category === 'pay' }
        ]"
        @click="handelChange(tab.label)"
      >
        {{ tab.name }}
      </button>
    </div>
    <div class="tab-content">
      <categoryList :activeTab="activeTab" />
    </div>
  </div>
</template>

<style scoped>
.header {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
}
.tabs {
  display: flex;
  border-bottom: 1px solid #dcdcdc;
}

.tabs button {
  flex: 1;
  padding: 10px;
  border: none;
  background: white;
  cursor: pointer;
}

.tabs button.colactive {
  background: rgba(0, 122, 204, 0.4);
}
.tabs button.payactive {
  background-color: rgba(255, 0, 0, 0.4);
}
.tab-content {
  padding: 20px;
}
</style>
