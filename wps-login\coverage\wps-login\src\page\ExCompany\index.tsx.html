
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for wps-login/src/page/ExCompany/index.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../index.html">All files</a> / <a href="index.html">wps-login/src/page/ExCompany</a> index.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/56</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/56</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" >import ButtonItem from "../../components/ButtonItem";<span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" ></span></span></span>
<span class="cstat-no" title="statement not covered" >import Common from "../../components/Common";</span>
<span class="cstat-no" title="statement not covered" >import { useMobile } from "../../hooks/useMobile";</span>
<span class="cstat-no" title="statement not covered" >import "./index.css";</span>
interface ExCompanyProps {
  onBack: () =&gt; void;
}
&nbsp;
<span class="cstat-no" title="statement not covered" >const company = [</span>
<span class="cstat-no" title="statement not covered" >  {</span>
<span class="cstat-no" title="statement not covered" >    icon: '&lt;svg t="1753153861902" className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2321" width="32" height="32"&gt;&lt;path d="M799.**********.000994h-23.999963c-4.399993 0-7.999988-3.599994-7.999987-7.999988V258.001189C767.**********.**********.399782-0.**********.000002 0.00159 370.10022 0.**********.**********.**********.**********.001192v119.999814c0 4.399993-3.599994 7.999988-7.999987 7.999988h-23.999963c-52.999918 0-95.999851 42.999933-95.999851 95.999851v447.999304c0 52.999918 42.999933 95.999851 95.999851 95.999851h575.999106c52.999918 0 95.999851-42.999933 95.999851-95.999851V640.000845c0-52.999918-42.999933-95.999851-95.999851-95.999851zM569.69991 748.700427c-15.999975 12.099981-25.69996 30.699952-25.69996 50.799922V864.000248c0 17.499973-14.199978 31.799951-31.599951 31.999951-17.799972 0.2-32.39995-14.799977-32.399949-32.699949V800.000348c0-20.199969-9.599985-39.099939-25.79996-51.299921C431.000126 731.200455 416.000149 703.300498 416.000149 672.000547c0-52.199919 42.399934-95.299852 94.599853-95.999851 53.699917-0.799999 97.399849 42.499934 97.399849 95.999851 0 31.299951-14.999977 59.199908-38.299941 76.69988zM703.999702 376.001006c0 4.399993-3.599994 7.999988-7.999988 7.999988H328.000286c-4.399993 0-7.999988-3.599994-7.999988-7.999988V256.001192c0-51.29992 19.999969-99.499846 56.199913-135.799789C412.500154 84.00146 460.70008 64.001491 512 64.001491s99.499846 19.999969 135.799789 56.199912C683.999733 156.501347 703.999702 204.701272 703.999702 256.001192v119.999814z" p-id="2322" fill="#8a8a8a"&gt;&lt;/path&gt;&lt;path d="M512 672.000547m-31.99995 0a31.99995 31.99995 0 1 0 63.9999 0 31.99995 31.99995 0 1 0-63.9999 0Z" p-id="2323" fill="#8a8a8a"&gt;&lt;/path&gt;&lt;/svg&gt;',</span>
<span class="cstat-no" title="statement not covered" >    title: "账号密码"</span>
<span class="cstat-no" title="statement not covered" >  },</span>
<span class="cstat-no" title="statement not covered" >  {</span>
<span class="cstat-no" title="statement not covered" >    icon: '&lt;svg t="1753153957637" className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6179" width="32" height="32"&gt;&lt;path d="M146.432 336.896h-81.92V106.496l40.96-40.96h231.424v81.92H146.432zM336.896 958.464H105.472l-40.96-40.96V687.104h81.92v189.44h190.464zM956.416 336.896h-81.92V147.456H684.032v-81.92h231.424l40.96 40.96zM915.456 958.464H613.376v-81.92h261.12V659.456h81.92v258.048z" fill="#437DFF" p-id="6180"&gt;&lt;/path&gt;&lt;path d="M326.656 334.848h61.44v98.304h-61.44zM415.744 575.488h61.44v133.12h-61.44zM265.216 575.488h61.44v114.688h-61.44zM566.272 575.488h61.44v98.304h-61.44zM706.56 575.488h61.44v154.624h-61.44zM477.184 297.984h61.44v135.168h-61.44zM627.712 329.728h61.44v103.424h-61.44z" fill="#63F7DE" p-id="6181"&gt;&lt;/path&gt;&lt;path d="M10.24 473.088h1003.52v61.44H10.24z" fill="#437DFF" p-id="6182"&gt;&lt;/path&gt;&lt;/svg&gt;',</span>
<span class="cstat-no" title="statement not covered" >    title: "WPS扫码"</span>
<span class="cstat-no" title="statement not covered" >  },</span>
<span class="cstat-no" title="statement not covered" >  {</span>
<span class="cstat-no" title="statement not covered" >    icon: '&lt;svg t="1753170944868" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2617" width="32" height="32"&gt;&lt;path d="M412.08 753.87Q297 855.06 220.14 855.13t-117.27-95.07l309.21-6.24z m202.16 0l309.21 6.19Q883 855.13 806.18 855.13T614.24 753.82zM15.91 489.1q178 95 228.49 125.34t171.89 108.88q-193.1 12-252.75 0.32c-45.49-8.89-80.9-28.29-111.22-58.66Q-8.22 604.44 15.91 489.1z m994.5 0Q1034.56 604.41 974 665c-30.31 30.37-65.72 49.77-111.22 58.66q-59.64 11.7-252.7-0.32 121.27-78.55 171.85-108.88t228.48-125.36zM161.48 228.27q93.08 123.36 127.41 175.94t155 283.38Q205.88 580.55 113 475c-44.49-50.54-44.49-133.46 6-204.23q11-15.4 42.47-42.47z m703.31 0q31.51 27.1 42.47 42.47c50.54 70.77 50.54 153.65 6.06 204.26q-92.88 105.6-331 212.62 120.66-230.8 155-283.43t127.47-175.92zM440.6 84.72q46.51 137.52 52.56 194.1T483.07 659Q280.81 367.83 280.81 236.39T440.6 84.72z m145.21 0Q745.55 105 745.55 236.39T543.34 659q-16.17-323.55-10.09-380.16t52.56-194.1z" fill="#FE0000" p-id="2618"&gt;&lt;/path&gt;&lt;/svg&gt;',</span>
<span class="cstat-no" title="statement not covered" >    title: "华为账号"</span>
<span class="cstat-no" title="statement not covered" >  },</span>
<span class="cstat-no" title="statement not covered" >  {</span>
<span class="cstat-no" title="statement not covered" >    icon: '&lt;svg t="1753170965553" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3601" width="32" height="32"&gt;&lt;path d="M709.2224 678.656h114.176c-71.0144 99.2768-139.3152 194.7136-207.616 290.1504-1.6384-0.5632-3.2768-1.1776-4.9152-1.7408 14.8992-62.6688 29.7984-125.3888 45.312-190.72h-89.2416c10.1376-46.2848 19.6608-89.7536 30.0544-137.2672-25.3952 7.6288-47.5136 12.0832-67.7376 20.8896-43.7248 19.0464-83.0976 10.9056-118.5792-17.2544-23.5008-18.6368-46.4384-38.9632-65.5872-61.9008-19.968-23.9616-13.7216-37.632 16.6912-42.752 64.3072-10.8544 128.8192-20.3776 193.4336-32.3584-13.5168 0-27.0336 0.1024-40.5504 0-55.6032-0.5632-111.2064-1.4336-166.8608-1.6384-34.1504-0.1024-58.1632-17.8176-78.7968-42.5472-26.624-31.9488-44.1856-68.4032-54.1184-108.544-6.2976-25.4464 0.6656-32.4608 26.9312-26.368 66.4576 15.4624 132.7104 31.4368 199.168 47.0016 33.9456 7.936 67.9936 15.3088 102.9632 19.712-40.2944-13.312-80.9984-25.4976-120.7808-40.2432-60.6208-22.4768-120.5248-46.9504-180.9408-70.0416-18.3296-7.0144-30.464-19.2-38.144-36.8128-23.6032-53.8624-41.216-109.2096-41.6768-168.6528-0.1536-21.6576 7.0656-26.9824 25.7024-17.9712C377.7536 151.2448 573.44 228.4544 769.8944 303.872c18.8416 7.2192 36.7616 17.8688 53.7088 29.0816 32.6144 21.7088 43.264 49.1008 26.368 83.456-34.7136 70.5536-72.8576 139.4688-109.8752 208.896-9.1648 17.152-19.456 33.6896-30.8736 53.3504z" fill="#2595E8" p-id="3602"&gt;&lt;/path&gt;&lt;path d="M763.5968 301.4144C569.2416 226.816 375.7568 150.2208 188.16 59.5456c-18.5856-9.0112-25.856-3.6864-25.7024 17.9712 0.4608 59.4432 18.0736 114.7904 41.6768 168.6528 7.7312 17.6128 19.8144 29.7984 38.144 36.8128 60.416 23.0912 120.32 47.5648 180.9408 70.0416 39.7824 14.7456 80.4864 26.9312 120.7808 40.2432-34.9696-4.4032-69.0176-11.776-102.9632-19.712-66.4064-15.5136-132.7104-31.5392-199.168-47.0016-26.2656-6.0928-33.2288 0.9216-26.9312 26.368 9.9328 40.192 27.4944 76.5952 54.1184 108.544 20.5824 24.7296 44.6464 42.3936 78.7968 42.5472 55.6032 0.2048 111.2064 1.1264 166.8608 1.6384 13.5168 0.1536 27.0336 0 40.5504 0-64.6144 11.9808-129.1264 21.5552-193.4336 32.3584-30.4128 5.12-36.608 18.7904-16.6912 42.752 19.0976 22.9376 42.0864 43.264 65.5872 61.9008 35.4816 28.16 74.8032 36.3008 118.5792 17.2544 20.224-8.8064 42.3936-13.2096 67.7376-20.8896-5.1712 23.6032-10.1376 46.2336-15.0528 68.7104 109.3632-101.632 178.6368-245.8112 181.6064-406.3232z" fill="#3A9CED" p-id="3603"&gt;&lt;/path&gt;&lt;path d="M242.2784 282.9312c60.416 23.0912 120.32 47.5648 180.9408 70.0416 33.1264 12.288 66.9184 22.8352 100.5568 33.6896 28.928-47.0016 51.2512-98.4576 65.5872-153.1904-135.4752-53.8624-269.824-110.3872-401.2544-173.9776-18.5856-9.0112-25.856-3.6864-25.7024 17.9712 0.4608 59.4432 18.0736 114.7904 41.6768 168.6528 7.7824 17.6128 19.9168 29.8496 38.1952 36.8128zM441.0368 373.504c-66.4064-15.5136-132.7104-31.5392-199.168-47.0016-26.2656-6.0928-33.2288 0.9216-26.9312 26.368 9.9328 40.192 27.4944 76.5952 54.1184 108.544 20.5824 24.7296 44.6464 42.3936 78.7968 42.5472 26.4192 0.1024 52.8896 0.3584 79.3088 0.6656 36.2496-33.8944 68.096-72.3968 94.5152-114.688-27.1872-4.352-53.9648-10.1888-80.64-16.4352zM361.7792 538.0608c-26.624 4.5056-34.6624 15.5136-22.8864 34.304 19.5072-12.1344 38.1952-25.4464 56.064-39.7824-11.0592 1.792-22.1184 3.584-33.1776 5.4784z" fill="#59ADF8" p-id="3604"&gt;&lt;/path&gt;&lt;path d="M242.2784 282.9312c14.8992 5.6832 29.7984 11.4688 44.6464 17.3056 44.3904-42.0352 82.0224-91.0848 111.0528-145.5104-70.6048-30.2592-140.6464-61.7472-209.8688-95.232-18.5856-9.0112-25.856-3.6864-25.7024 17.9712 0.4608 59.4432 18.0736 114.7904 41.6768 168.6528 7.7824 17.6128 19.9168 29.8496 38.1952 36.8128zM241.8688 326.5536c-26.2656-6.0928-33.2288 0.9216-26.9312 26.368 0.3584 1.536 0.8704 3.0208 1.28 4.5568 12.9536-8.8576 25.4976-18.2272 37.6832-28.1088-4.0448-0.9216-8.0384-1.8944-12.032-2.816z" fill="#6BC2FC" p-id="3605"&gt;&lt;/path&gt;&lt;/svg&gt;',</span>
<span class="cstat-no" title="statement not covered" >    title: "钉钉账号"</span>
<span class="cstat-no" title="statement not covered" >  },</span>
<span class="cstat-no" title="statement not covered" >  {</span>
<span class="cstat-no" title="statement not covered" >    icon: '&lt;svg t="1753170985115" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4623" width="32" height="32"&gt;&lt;path d="M723.2 537.6c0-96 80-144 83.2-147.2-44.8-67.2-115.2-73.6-140.8-76.8-60.8-6.4-115.2 35.2-147.2 35.2-28.8 0-76.8-35.2-128-32-64 0-124.8 38.4-160 96C163.2 528 214.4 704 281.6 800c32 48 70.4 99.2 121.6 96 48-3.2 67.2-32 124.8-32 57.6 0 76.8 32 128 32s86.4-48 118.4-92.8c38.4-54.4 51.2-105.6 54.4-108.8-6.4-3.2-105.6-41.6-105.6-156.8m-99.2-288c25.6-32 44.8-76.8 38.4-121.6-38.4 0-86.4 25.6-112 57.6-25.6 28.8-48 73.6-41.6 118.4 44.8 3.2 89.6-22.4 115.2-54.4" fill="#040000" p-id="4624"&gt;&lt;/path&gt;&lt;/svg&gt;',</span>
<span class="cstat-no" title="statement not covered" >    title: "Apple账号"</span>
<span class="cstat-no" title="statement not covered" >  },</span>
<span class="cstat-no" title="statement not covered" >  {</span>
<span class="cstat-no" title="statement not covered" >    icon: '&lt;svg t="1753171006005" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5757" width="32" height="32"&gt;&lt;path d="M706.975647 794.391579V389.422244c0-92.01467-74.570369-166.618808-166.576407-166.618809H72.149227c-3.610224 0-6.535857 2.90926-6.535857 6.482645v565.101405c0 3.573385 2.924609 6.469342 6.535857 6.469342h125.074623c3.611248 0 6.531763-2.911306 6.531764-6.505157V350.813924c0-3.601015 2.924609-6.522554 6.535856-6.522554h268.962732c50.507324 0 91.466178 40.939411 91.466178 91.452875v358.594122c0 3.597945 2.920516 6.502088 6.513344 6.502087H700.467419c3.584642 0 6.513344-2.902096 6.513344-6.469342l-0.005116 0.020467z m-252.57653-0.044003a6.492878 6.492878 0 0 1-6.50925 6.509251H322.844919c-3.611248 0-6.535857-2.911306-6.535857-6.509251V454.467679c0-3.597945 2.924609-6.51846 6.535857-6.518461h125.044948a6.503111 6.503111 0 0 1 6.50925 6.518461V794.364973v-0.017397m500.045737 0c0 3.601015-2.928702 6.509251-6.538926 6.509251H822.889633c-3.620457 0-6.564509-2.911306-6.564509-6.509251V229.346455c0-3.614317 2.944052-6.531763 6.564509-6.531763h125.016295a6.528694 6.528694 0 0 1 6.538926 6.531763v565.001121" fill="#fa6719" p-id="5758"&gt;&lt;/path&gt;&lt;/svg&gt;',</span>
<span class="cstat-no" title="statement not covered" >    title: "小米账号"</span>
<span class="cstat-no" title="statement not covered" >  },</span>
<span class="cstat-no" title="statement not covered" >  {</span>
<span class="cstat-no" title="statement not covered" >    icon: '&lt;svg t="1753171033400" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6787" width="32" height="32"&gt;&lt;path d="M851.4 590.193c-22.196-66.233-90.385-90.422-105.912-91.863-15.523-1.442-29.593-9.94-19.295-27.505 10.302-17.566 29.304-68.684-7.248-104.681-36.564-36.14-116.512-22.462-173.094 0.866-56.434 23.327-53.39 7.055-51.65-8.925 1.89-16.848 32.355-111.02-60.791-122.395C311.395 220.86 154.85 370.754 99.572 457.15 16 587.607 29.208 675.873 29.208 675.873h0.58c10.009 121.819 190.787 218.869 412.328 218.869 190.5 0 350.961-71.853 398.402-169.478 0 0 0.143-0.433 0.575-1.156 4.938-10.506 8.71-21.168 11.035-32.254 6.668-26.205 11.755-64.215-0.728-101.66z m-436.7 251.27c-157.71 0-285.674-84.095-285.674-187.768 0-103.671 127.82-187.76 285.674-187.76 157.705 0 285.673 84.089 285.673 187.76 0 103.815-127.968 187.768-285.673 187.768z" fill="#E71F19" p-id="6788"&gt;&lt;/path&gt;&lt;path d="M803.096 425.327c2.896 1.298 5.945 1.869 8.994 1.869 8.993 0 17.7-5.328 21.323-14.112 5.95-13.964 8.993-28.793 8.993-44.205 0-62.488-51.208-113.321-114.181-113.321-15.379 0-30.32 3.022-44.396 8.926-11.755 4.896-17.263 18.432-12.335 30.24 4.933 11.662 18.572 17.134 30.465 12.238 8.419-3.46 17.268-5.33 26.41-5.33 37.431 0 67.752 30.241 67.752 67.247 0 9.068-1.735 17.857-5.369 26.202a22.832 22.832 0 0 0 12.335 30.236l0.01 0.01z" fill="#F5AA15" p-id="6789"&gt;&lt;/path&gt;&lt;path d="M726.922 114.157c-25.969 0-51.65 3.744-76.315 10.942-18.423 5.472-28.868 24.622-23.5 42.91 5.509 18.29 24.804 28.657 43.237 23.329a201.888 201.888 0 0 1 56.578-8.064c109.253 0 198.189 88.271 198.189 196.696 0 19.436-2.905 38.729-8.419 57.16-5.508 18.289 4.79 37.588 23.212 43.053 3.342 1.014 6.817 1.442 10.159 1.442 14.943 0 28.725-9.648 33.37-24.48 7.547-24.906 11.462-50.826 11.462-77.175-0.143-146.588-120.278-265.813-267.973-265.813z" fill="#F5AA15" p-id="6790"&gt;&lt;/path&gt;&lt;path d="M388.294 534.47c-84.151 0-152.34 59.178-152.34 132.334 0 73.141 68.189 132.328 152.34 132.328 84.148 0 152.337-59.182 152.337-132.328 0-73.15-68.19-132.334-152.337-132.334zM338.53 752.763c-29.454 0-53.39-23.755-53.39-52.987 0-29.228 23.941-52.989 53.39-52.989 29.453 0 53.39 23.76 53.39 52.989 0 29.227-23.937 52.987-53.39 52.987z m99.82-95.465c-6.382 11.086-19.296 15.696-28.726 10.219-9.43-5.323-11.75-18.717-5.37-29.803 6.386-11.09 19.297-15.7 28.725-10.224 9.43 5.472 11.755 18.864 5.37 29.808z" fill="#040000" p-id="6791"&gt;&lt;/path&gt;&lt;/svg&gt;',</span>
<span class="cstat-no" title="statement not covered" >    title: "微博账号"</span>
<span class="cstat-no" title="statement not covered" >  },</span>
<span class="cstat-no" title="statement not covered" >  {</span>
<span class="cstat-no" title="statement not covered" >    icon: '&lt;svg t="1753171058401" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7849" width="32" height="32"&gt;&lt;path d="M943.077086 414.549303c-42.267772-45.338508-99.166696-74.239548-160.220145-81.64544-29.623567-48.409243-70.988181-89.051332-119.758687-117.591109-52.383136-30.707356-112.533427-46.78356-173.586876-46.78356-83.090492 0-163.29088 29.98483-225.789381 84.716176-52.744399 46.061034-90.857647 108.559534-107.656377 176.296348-40.280826 14.269889-75.323337 40.100194-101.153643 74.42018-29.081672 38.293879-44.254719 84.174281-44.254718 132.222261 0 108.740166 80.019757 203.029811 186.231081 219.106016l1.625684 0.361263H751.427059c144.685835 0 262.276945-117.771741 262.276945-262.276945v-0.180632c0-66.472394-25.107779-130.054683-70.626918-178.644558z m-613.605221-85.980596c44.254719-38.655142 101.153643-59.96966 159.858881-59.96966 67.19492 0 131.499735 27.817252 177.380138 76.587758-50.757453 17.159993-94.109014 48.951138-126.080791 91.760805-7.947786 10.83789-11.379785 24.02399-9.392838 37.21009 1.986947 13.1861 9.031575 24.927148 19.869466 32.874934 22.037044 16.437467 53.466925 11.741048 70.085024-10.295996 31.068619-41.725878 78.394073-65.569236 130.235314-65.569236 89.412595 0 162.207091 72.794496 162.207092 162.207091s-72.794496 162.207091-162.207092 162.207091l-523.289469-0.180631c-64.84671-0.903158-117.410478-54.370083-117.410478-119.036162 0-57.440818 41.**********.753219 97.**********.229847l35.223143-6.502734 5.238314-35.584406c8.128418-57.440818 36.**********.185218 80.**********.479097z" fill="#5482F8" p-id="7850"&gt;&lt;/path&gt;&lt;/svg&gt;',</span>
<span class="cstat-no" title="statement not covered" >    title: "教育云账号"</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >function ExCompany({ onBack }: ExCompanyProps) {</span>
  // 使用自定义hook检测是否是移动端
<span class="cstat-no" title="statement not covered" >  const isMobile = useMobile();</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  return (</span>
<span class="cstat-no" title="statement not covered" >    &lt;Common</span>
<span class="cstat-no" title="statement not covered" >      isShowBack={true}</span>
<span class="cstat-no" title="statement not covered" >      className="login_back_y"</span>
<span class="cstat-no" title="statement not covered" >      headerClassName="login_header_y"</span>
<span class="cstat-no" title="statement not covered" >      onBack={onBack}</span>
<span class="cstat-no" title="statement not covered" >      title={isMobile ? "使用金山办公在线服务账号登录" : "更多登录方式"}</span>
<span class="cstat-no" title="statement not covered" >      subTitle={isMobile ? "" : "使用金山办公在线服务账号登录"}</span>
    &gt;
<span class="cstat-no" title="statement not covered" >      &lt;div className="ex-company-list"&gt;</span>
<span class="cstat-no" title="statement not covered" >        {company.map((item, idx) =&gt; (</span>
<span class="cstat-no" title="statement not covered" >          &lt;ButtonItem key={idx} icon={item.icon} title={item.title} /&gt;</span>
<span class="cstat-no" title="statement not covered" >        ))}</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/Common&gt;</span>
  );
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export default ExCompany;</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-24T00:50:30.664Z
            </div>
        <script src="../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../sorter.js"></script>
        <script src="../../../../block-navigation.js"></script>
    </body>
</html>
    