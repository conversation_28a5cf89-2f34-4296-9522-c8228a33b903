<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1752204852621" clover="3.2.0">
  <project timestamp="1752204852621" name="All files">
    <metrics statements="9" coveredstatements="5" conditionals="6" coveredconditionals="3" methods="4" coveredmethods="2" elements="19" coveredelements="10" complexity="0" loc="9" ncloc="9" packages="1" files="2" classes="2"/>
    <file name="index.ts" path="D:\code\func-npm\src\index.ts">
      <metrics statements="1" coveredstatements="1" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="1"/>
      <line num="1" count="2" type="stmt"/>
    </file>
    <file name="utils.ts" path="D:\code\func-npm\src\utils.ts">
      <metrics statements="8" coveredstatements="4" conditionals="6" coveredconditionals="3" methods="2" coveredmethods="1"/>
      <line num="3" count="1" type="stmt"/>
      <line num="4" count="1" type="cond" truecount="3" falsecount="1"/>
      <line num="5" count="0" type="stmt"/>
      <line num="7" count="1" type="stmt"/>
      <line num="11" count="1" type="stmt"/>
      <line num="12" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="13" count="0" type="stmt"/>
      <line num="15" count="0" type="stmt"/>
    </file>
  </project>
</coverage>
