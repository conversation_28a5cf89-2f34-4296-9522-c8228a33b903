import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { beforeEach, describe, expect, it, vi } from "vitest";
import type { User } from "../../../type";
import { createMockUser } from "../../PhoneLogin/__tests__/test-utils";
import UsersList from "../index";

// Mock子组件
vi.mock("../../../components/Common", () => ({
  default: ({
    title,
    isShowBack,
    headerClassName,
    children,
    fchildren
  }: {
    title: string;
    isShowBack: boolean;
    headerClassName: string;
    children: React.ReactNode;
    fchildren: React.ReactNode;
  }) => (
    <div data-testid="common-component">
      <div data-testid="title">{title}</div>
      <div data-testid="is-show-back">{isShowBack.toString()}</div>
      <div data-testid="header-class">{headerClassName}</div>
      <div data-testid="children">{children}</div>
      <div data-testid="fchildren">{fchildren}</div>
    </div>
  )
}));

vi.mock("../../../components/UserItem", () => ({
  default: ({
    avatarUrl,
    nickname,
    companyName,
    isCompanyAccount,
    isCurrent,
    isLogin,
    onSelect
  }: {
    avatarUrl: string;
    nickname: string;
    companyName: string;
    isCompanyAccount: boolean;
    isCurrent: boolean;
    isLogin: boolean;
    onSelect?: (isSelected: boolean) => void;
  }) => (
    <div data-testid={`user-item-${nickname}`}>
      <div data-testid="avatar-url">{avatarUrl}</div>
      <div data-testid="nickname">{nickname}</div>
      <div data-testid="company-name">{companyName}</div>
      <div data-testid="is-company-account">{isCompanyAccount.toString()}</div>
      <div data-testid="is-current">{isCurrent.toString()}</div>
      <div data-testid="is-login">{isLogin.toString()}</div>
      {onSelect && (
        <button
          data-testid={`select-${nickname}`}
          onClick={() => onSelect(true)}
        >
          选择用户
        </button>
      )}
      {onSelect && (
        <button
          data-testid={`unselect-${nickname}`}
          onClick={() => onSelect(false)}
        >
          取消选择
        </button>
      )}
    </div>
  )
}));

// Mock alert
Object.defineProperty(window, "alert", {
  value: vi.fn(),
  writable: true
});

describe("UsersList组件", () => {
  const mockOnBack = vi.fn();
  const mockUsers: User[] = [
    createMockUser({
      userid: 1,
      nickname: "用户1",
      company_name: "公司1",
      is_company_account: true,
      is_current: true,
      is_login: false,
      avatar_url: "avatar1.jpg"
    }),
    createMockUser({
      userid: 2,
      nickname: "用户2",
      company_name: "公司2",
      is_company_account: false,
      is_current: false,
      is_login: true,
      avatar_url: "avatar2.jpg"
    })
  ];

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("正确渲染组件基本结构", () => {
    render(<UsersList users={mockUsers} onBack={mockOnBack} />);

    expect(screen.getByTestId("common-component")).toBeInTheDocument();
    expect(screen.getByTestId("title")).toHaveTextContent(
      "当前手机号绑定了以下账号，请选择账号登录"
    );
    expect(screen.getByTestId("is-show-back")).toHaveTextContent("false");
    expect(screen.getByTestId("header-class")).toHaveTextContent(
      "user_login_title"
    );
  });

  it("正确渲染用户列表", () => {
    render(<UsersList users={mockUsers} onBack={mockOnBack} />);

    expect(screen.getByTestId("user-item-用户1")).toBeInTheDocument();
    expect(screen.getByTestId("user-item-用户2")).toBeInTheDocument();

    // 检查用户1的信息
    const user1Container = screen.getByTestId("user-item-用户1");
    expect(user1Container).toHaveTextContent("用户1");
    expect(user1Container).toHaveTextContent("公司1");
    expect(user1Container).toHaveTextContent("true"); // is_company_account

    // 检查用户2的信息
    const user2Container = screen.getByTestId("user-item-用户2");
    expect(user2Container).toHaveTextContent("用户2");
    expect(user2Container).toHaveTextContent("公司2");
    expect(user2Container).toHaveTextContent("false"); // is_company_account
  });

  it("空用户列表时显示暂无账号", () => {
    render(<UsersList users={[]} onBack={mockOnBack} />);

    expect(screen.getByText("暂无账号")).toBeInTheDocument();
    expect(screen.queryByTestId("user-item-用户1")).not.toBeInTheDocument();
  });

  it("渲染确认登录按钮", () => {
    render(<UsersList users={mockUsers} onBack={mockOnBack} />);

    expect(screen.getByText("确认登录")).toBeInTheDocument();
  });

  it("选择用户后显示选择状态", async () => {
    const user = userEvent.setup();
    render(<UsersList users={mockUsers} onBack={mockOnBack} />);

    // 选择用户1
    const selectButton = screen.getByTestId("select-用户1");
    await user.click(selectButton);

    // 验证选择状态区域存在并包含用户信息
    const selectionStatus = document.querySelector(".selection-status");
    expect(selectionStatus).toBeInTheDocument();
    expect(selectionStatus).toHaveTextContent("用户1");
  });

  it("选择多个用户后显示正确的选择状态", async () => {
    const user = userEvent.setup();
    render(<UsersList users={mockUsers} onBack={mockOnBack} />);

    // 选择用户1
    await user.click(screen.getByTestId("select-用户1"));
    // 选择用户2
    await user.click(screen.getByTestId("select-用户2"));

    // 应该显示多选状态
    expect(
      screen.getByText("已选2个账号，登录后可以切换访问2个账号数据")
    ).toBeInTheDocument();
  });

  it("取消选择用户后更新选择状态", async () => {
    const user = userEvent.setup();
    render(<UsersList users={mockUsers} onBack={mockOnBack} />);

    // 先选择用户1
    await user.click(screen.getByTestId("select-用户1"));
    expect(document.querySelector(".selection-status")).toBeInTheDocument();

    // 取消选择用户1
    await user.click(screen.getByTestId("unselect-用户1"));
    expect(document.querySelector(".selection-status")).not.toBeInTheDocument();
  });

  it("未选择用户时点击确认登录显示提示", async () => {
    const user = userEvent.setup();
    render(<UsersList users={mockUsers} onBack={mockOnBack} />);

    const loginButton = screen.getByText("确认登录");
    await user.click(loginButton);

    expect(window.alert).toHaveBeenCalledWith("请至少选择一个账号");
  });

  it("选择用户后点击确认登录显示用户信息", async () => {
    const user = userEvent.setup();
    render(<UsersList users={mockUsers} onBack={mockOnBack} />);

    // 选择用户1
    await user.click(screen.getByTestId("select-用户1"));

    // 点击确认登录
    const loginButton = screen.getByText("确认登录");
    await user.click(loginButton);

    expect(window.alert).toHaveBeenCalledWith(
      "选中的用户信息:\n\n昵称: 用户1\n公司: 公司1\n企业账号: 是"
    );
  });

  it("选择多个用户后点击确认登录显示所有用户信息", async () => {
    const user = userEvent.setup();
    render(<UsersList users={mockUsers} onBack={mockOnBack} />);

    // 选择两个用户
    await user.click(screen.getByTestId("select-用户1"));
    await user.click(screen.getByTestId("select-用户2"));

    // 点击确认登录
    const loginButton = screen.getByText("确认登录");
    await user.click(loginButton);

    expect(window.alert).toHaveBeenCalledWith(
      "选中的用户信息:\n\n昵称: 用户1\n公司: 公司1\n企业账号: 是\n\n昵称: 用户2\n公司: 公司2\n企业账号: 否"
    );
  });

  it("正确处理用户选择和取消选择的逻辑", async () => {
    const user = userEvent.setup();
    render(<UsersList users={mockUsers} onBack={mockOnBack} />);

    // 选择用户1和用户2
    await user.click(screen.getByTestId("select-用户1"));
    await user.click(screen.getByTestId("select-用户2"));
    expect(
      screen.getByText("已选2个账号，登录后可以切换访问2个账号数据")
    ).toBeInTheDocument();

    // 取消选择用户1
    await user.click(screen.getByTestId("unselect-用户1"));
    const selectionStatus = document.querySelector(".selection-status");
    expect(selectionStatus).toBeInTheDocument();
    expect(selectionStatus).toHaveTextContent("用户2");

    // 取消选择用户2
    await user.click(screen.getByTestId("unselect-用户2"));
    expect(document.querySelector(".selection-status")).not.toBeInTheDocument();
  });

  it("使用用户昵称作为唯一标识符进行过滤", async () => {
    const usersWithSameId = [
      createMockUser({
        userid: 1,
        nickname: "用户A",
        company_name: "公司A"
      }),
      createMockUser({
        userid: 1, // 相同的userid
        nickname: "用户B", // 不同的nickname
        company_name: "公司B"
      })
    ];

    const user = userEvent.setup();
    render(<UsersList users={usersWithSameId} onBack={mockOnBack} />);

    // 选择两个用户
    await user.click(screen.getByTestId("select-用户A"));
    await user.click(screen.getByTestId("select-用户B"));

    // 取消选择用户A
    await user.click(screen.getByTestId("unselect-用户A"));

    // 应该只剩下用户B被选中
    const selectionStatus = document.querySelector(".selection-status");
    expect(selectionStatus).toBeInTheDocument();
    expect(selectionStatus).toHaveTextContent("用户B");
    expect(selectionStatus).not.toHaveTextContent("用户A");
  });
});
