
//字符串首字母大写工具函数
export function capitalizeFirstLetter(str: string): string {
  if (typeof str !== 'string' || str.length === 0) {
    return str; // 如果不是字符串或空字符串，直接返回
  }
  return str.charAt(0).toUpperCase() + str.slice(1);
}

// 数组去重工具函数
export function uniqueArray<T>(arr: T[]): T[] {
  if (!Array.isArray(arr)) {
    throw new TypeError('Expected an array');
  }
  return Array.from(new Set(arr));
}

