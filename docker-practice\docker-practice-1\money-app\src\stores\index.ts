import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import CollectData from '@/data/collect.json'

export interface moneyItem {
  id: number
  name: string
  money: string
  category: string
  date: Date;
}

export const useMoneyStore = defineStore('money', () => {
  const activateTag = ref<number>(1)
  const category = ref<string>('collect')
  const tag = ref<string>(CollectData[0]['name'])
  const money = ref<string>('0')
  const moneyList = ref<moneyItem[]>([])


  // 用于获取不同类别的列表--charts数据类型[{value:,name:}]
  //对同一类别的数据求和

  const collect_list = computed(() => {
    const filteredList = moneyList.value.filter(item => item.category === 'collect');
    return filteredList.reduce<moneyItem[]>((acc, curr) => {
      const found = acc.find(item => item.name === curr.name);

      if (found) {
        found.money = (parseFloat(found.money) + parseFloat(curr.money)).toString();
      } else {
        acc.push({ ...curr });
      }

      return acc;
    }, []);
  });

  const pay_list = computed(() => {
    const filteredList = moneyList.value.filter(item => item.category === 'pay');
    return filteredList.reduce<moneyItem[]>((acc, curr) => {
      const found = acc.find(item => item.name === curr.name);
      if (found) {
        found.money = (parseFloat(found.money) + parseFloat(curr.money)).toString();
      } else {
        acc.push({ ...curr });
      }
      return acc;
    }, []);
  });

  //总支出
  const total_pay = computed(() => {
    return moneyList.value.reduce((acc, cur) => {
      if (cur.category === 'pay') {
        acc += +cur.money;
      }
      return acc
    }, 0)
  })


  //总收入
  const total_income = computed(() => {
    return moneyList.value.reduce((acc, cur) => {
      if (cur.category === 'collect') {
        acc += +cur.money;
      }
      return acc
    }, 0)
  })

  //总合
  const summary_money = computed(() => {
    return total_income.value - total_pay.value
  })


  // 添加数据
  const addMoney = (category: string, tag: string, money: string, date: Date) => {
    console.log('333', category, tag, money);
    moneyList.value.push({
      id: moneyList.value.length + 1,
      name: tag,
      money,
      category,
      date
    });
    console.log("🚀 ~ addMoney ~ moneyList:", moneyList.value);
  }

  //删除数据
  const delMoney = (index: number) => {
    moneyList.value.splice(index, 1)
  }

  //计算总支出，总收入，汇总
  return { total_pay, total_income, summary_money, category, tag, money, pay_list, collect_list, moneyList, addMoney, delMoney, activateTag }
})
