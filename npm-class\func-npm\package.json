{"name": "@xxin_/string-array-utils", "version": "1.0.3", "description": "包含字符串和数组处理的工具函数", "main": "./dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsc", "prepublish": "npm run build", "test": "jest"}, "repository": {"type": "git", "url": "https://ksogitlab.wps.kingsoft.net/zhouxinyi21"}, "keywords": ["string", "array", "utils", "typescript"], "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "devDependencies": {"@jest/globals": "^30.0.4", "@types/jest": "^30.0.0", "@types/node": "^24.0.13", "jest": "^30.0.4", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}