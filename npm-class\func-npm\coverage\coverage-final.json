{"D:\\code\\func-npm\\src\\index.ts": {"path": "D:\\code\\func-npm\\src\\index.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 9}}, "1": {"start": {"line": 1, "column": 9}, "end": {"line": 1, "column": 32}}, "2": {"start": {"line": 1, "column": 32}, "end": {"line": 1, "column": 61}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 1, "column": 9}, "end": {"line": 1, "column": 30}}, "loc": {"start": {"line": 1, "column": 9}, "end": {"line": 1, "column": 32}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 1, "column": 32}, "end": {"line": 1, "column": 43}}, "loc": {"start": {"line": 1, "column": 32}, "end": {"line": 1, "column": 61}}}}, "branchMap": {}, "s": {"0": 1, "1": 2, "2": 1}, "f": {"0": 1, "1": 0}, "b": {}}, "D:\\code\\func-npm\\src\\utils.ts": {"path": "D:\\code\\func-npm\\src\\utils.ts", "statementMap": {"0": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 16}}, "1": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 16}}, "2": {"start": {"line": 4, "column": 2}, "end": {"line": 6, "column": 3}}, "3": {"start": {"line": 5, "column": 4}, "end": {"line": 5, "column": 15}}, "4": {"start": {"line": 7, "column": 2}, "end": {"line": 7, "column": 52}}, "5": {"start": {"line": 12, "column": 2}, "end": {"line": 14, "column": 3}}, "6": {"start": {"line": 13, "column": 4}, "end": {"line": 13, "column": 45}}, "7": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": 34}}}, "fnMap": {"0": {"name": "capitalizeFirstLetter", "decl": {"start": {"line": 3, "column": 16}, "end": {"line": 3, "column": 37}}, "loc": {"start": {"line": 3, "column": 49}, "end": {"line": 8, "column": 1}}}, "1": {"name": "uniqueArray", "decl": {"start": {"line": 11, "column": 16}, "end": {"line": 11, "column": 27}}, "loc": {"start": {"line": 11, "column": 39}, "end": {"line": 16, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 4, "column": 2}, "end": {"line": 6, "column": 3}}, "type": "if", "locations": [{"start": {"line": 4, "column": 2}, "end": {"line": 6, "column": 3}}, {"start": {}, "end": {}}]}, "1": {"loc": {"start": {"line": 4, "column": 6}, "end": {"line": 4, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 4, "column": 6}, "end": {"line": 4, "column": 29}}, {"start": {"line": 4, "column": 33}, "end": {"line": 4, "column": 49}}]}, "2": {"loc": {"start": {"line": 12, "column": 2}, "end": {"line": 14, "column": 3}}, "type": "if", "locations": [{"start": {"line": 12, "column": 2}, "end": {"line": 14, "column": 3}}, {"start": {}, "end": {}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 0, "4": 1, "5": 0, "6": 0, "7": 0}, "f": {"0": 1, "1": 0}, "b": {"0": [0, 1], "1": [1, 1], "2": [0, 0]}}}