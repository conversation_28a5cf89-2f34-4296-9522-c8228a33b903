<script setup lang="ts">
import { defineProps } from 'vue'
import { useMoney } from '@/hooks/useMoney'
const props = defineProps(['item', 'index'])
const { delMoney } = useMoney()
</script>

<template>
  <div class="item-box">
    <div class="item-li">{{ props.item.name }}</div>
    <div class="item-li">
      {{ props.item.category === 'collect' ? '+' + props.item.money : '-' + props.item.money }}
    </div>
    <div class="item-date">
      {{ props.item.date.toLocaleString() }}
    </div>
    <div class="icon-box" @click="delMoney(props.index)">
      <svg
        t="1724655682866"
        class="icon"
        viewBox="0 0 1024 1024"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        p-id="3175"
        width="15"
        height="15"
      >
        <path
          d="M566 332H458c-69.5 0-126-57.7-126-128.6v-30.9C332 101.7 388.5 44 458 44h108c69.5 0 126 57.7 126 128.6v30.9c0 70.8-56.5 128.5-126 128.5z m54-159.4c0-31.2-24.2-56.6-54-56.6H458c-29.8 0-54 25.4-54 56.6v30.9c0 31.2 24.2 56.6 54 56.6h108c29.8 0 54-25.4 54-56.6v-30.9zM728 980H296c-59.6 0-108-48.4-108-108V224h648v648c0 59.6-48.4 108-108 108z m36-684H260v576c0 19.8 16.2 36 36 36h432c19.8 0 36-16.2 36-36V296zM620 836V404h72v432h-72z m-144 0V404h72v432h-72z m-144 0V404h72v432h-72z m504-504H116v-72h36v-36c0-19.8 16.2-36 36-36h648c19.8 0 36 16.2 36 36v36h36v72h-72z"
          fill="#707070"
          p-id="3176"
        ></path>
      </svg>
    </div>
  </div>
</template>

<style scoped>
.item-box {
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  line-height: 30px;
  border-bottom: 1px solid #e0e0e0;
  padding: 5px 0 5px 0;
}
.item-li {
  width: 20%;
}
.item-date {
  width: 35%;
}
.icon-box {
  text-align: center;
  line-height: 15px;
  background-color: rgba(8, 131, 213, 0.2);
  padding: 3px;
  border-radius: 5px;
}
</style>
