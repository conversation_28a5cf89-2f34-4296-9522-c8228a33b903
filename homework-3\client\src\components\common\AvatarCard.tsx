import { Avatar, HoverCard } from "radix-ui"
import React from "react"
import "./css/AvatarCardCss.css"
interface AvatarHoverCardProps {
  src?: string // 头像 URL
  fallbackText: string // 加载失败显示的字母
  name: string // 用户名
  email: string // 用户邮箱
  onProfileClick?: () => void // 点击“查看资料”
  className?: string
}

export const AvatarHoverCard: React.FC<AvatarHoverCardProps> = ({
  src,
  fallbackText,
  name,
  email,
  onProfileClick,
  className = ""
}) => {
  return (
    <HoverCard.Root>
      <HoverCard.Trigger asChild>
        <Avatar.Root className={`avatar-root ${className}`}>
          <Avatar.Image className="avatar-image" src={src} alt={name} />
          <Avatar.Fallback className="avatar-fallback">
            {fallbackText}
          </Avatar.Fallback>
        </Avatar.Root>
      </HoverCard.Trigger>

      <HoverCard.Portal>
        <HoverCard.Content
          className="hovercard-content"
          sideOffset={6}
          align="center"
        >
          <div className="hovercard-info">
            <div className="hovercard-name">{name}</div>
            <div className="hovercard-email">{email}</div>
            {onProfileClick && (
              <button className="hovercard-button" onClick={onProfileClick}>
                查看资料
              </button>
            )}
          </div>
          <HoverCard.Arrow className="hovercard-arrow" />
        </HoverCard.Content>
      </HoverCard.Portal>
    </HoverCard.Root>
  )
}
