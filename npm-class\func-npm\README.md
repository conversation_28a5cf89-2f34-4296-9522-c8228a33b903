# class01

开发自定义npm包并发布到npm官网

![1752214952775](image/README/1752214952775.png)

![1752216424514](image/README/1752216424514.png)

![1752216412035](image/README/1752216412035.png)

问题：

* 使用esm模块，在dist生成的index.js手动更改了utils的引用（加了.js后缀），网上查是说esm强制要有后缀
* 在 用jest单元测试的时候，安装@type声明，以及使用ts-jest（在配置文件中声明）

  ![1752216587993](image/README/1752216587993.png)
* ts配置文件也要修改

  ![1752216630703](image/README/1752216630703.png)
