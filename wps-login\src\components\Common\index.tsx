/**头标题，尾部考虑为自定义的标题头设置，中间和底部可以传入children */
import React from "react";
import "./index.css";
interface CommonProps {
  onBack?: () => void;
  isShowBack: boolean;
  title: string;
  subTitle?: string;
  children: React.ReactNode;
  fchildren?: React.ReactNode;
  className?: string;
  headerClassName?: string;
  contentClassName?: string;
  footerClassName?: string;
}
function Common(props: CommonProps) {
  const {
    onBack,
    isShowBack,
    title,
    subTitle,
    children,
    fchildren,
    className = "",
    headerClassName = "",
    contentClassName = "",
    footerClassName = ""
  } = props;

  return (
    <div className={`login ${className}`}>
      {isShowBack && (
        <button className="back_btn" onClick={onBack}>
          <svg className="icon" viewBox="0 0 1024 1024" width="32" height="32">
            <path
              d="M631.04 161.941333a42.666667 42.666667 0 0 1 63.061333 57.386667l-2.474666 2.730667-289.962667 292.245333 289.706667 287.402667a42.666667 42.666667 0 0 1 2.730666 57.6l-2.474666 2.752a42.666667 42.666667 0 0 1-57.6 2.709333l-2.752-2.474667-320-317.44a42.666667 42.666667 0 0 1-2.709334-57.6l2.474667-2.752 320-322.56z"
              fill="#4285f4"
            ></path>
          </svg>
          返回
        </button>
      )}
      <div className={`login_header ${headerClassName}`}>
        <div className="nav_title">{title}</div>
        <div className="nav_intro">{subTitle}</div>
      </div>
      <div className={`login-child ${contentClassName}`}>{children}</div>
      <div className={`login-footer ${footerClassName}`}>{fchildren}</div>
    </div>
  );
}

export default Common;
