* {
  margin: 0;
  padding: 0;
}

.App {
  width: 50%;
  height: 100%;
  display: flex;
  flex-direction: column;
  margin: 0 auto;
  border: 1px solid #d6dbda;
  border-radius: 10px;
  padding: 10px;
}


.App .header {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  align-items: center;

}

.ant-image {
  position: relative;
  width: 100%;
  height: 80%;
}

.App .header .logo {
  width: 100%;
  height: 100%;
  object-fit: fill;
}

.tabHeader {
  display: flex;
  justify-content: space-between;
  width: 100%;
  gap: 10px;
  margin: 10px;
}

.tableft {
  display: flex;
  gap: 10px;
  margin-left: 10px;
  align-items: center;
}

.nav-link {
  text-decoration: none;
  color: #000;
  font-size: 16px;
  transition: all 0.3s ease;
  position: relative;
}

.nav-link:hover {
  transform: scale(1.1);
  font-weight: bold;
}

.nav-link:hover::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: -2px;
  height: 2px;
  background-color: orange;

}

.tabright {
  text-align: right;
  position: relative;
}


.custom-datepicker {
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
}

.custom-datepicker-popup {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  z-index: 2000 !important;
}

.eat-time {
  font-size: 14px;
  color: #666;
  text-align: left;
  border: 1px solid #eaf1ef;
  border-radius: 10px;
  padding: 10px 6px;
  margin-bottom: 7px;
}

.main-content {
  flex: 1;
  padding: 0;
  overflow-y: auto;
  background-color: white;
}

.main-page {
  margin-top: 10px;
  background-color: white;
}