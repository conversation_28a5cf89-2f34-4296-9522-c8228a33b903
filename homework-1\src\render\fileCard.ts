import { formatTimestamp, removeFileExtension } from "../utils/utils";
import { FileMentionItem } from "../type";

function getFileTypeIcon(filename: string): string {
  const ext = filename.split(".").pop()?.toLowerCase();
  if (!ext) return defaultIcon();
  if (["doc", "docx"].includes(ext)) return wordIcon();
  if (["xls", "xlsx"].includes(ext)) return excelIcon();
  if (["ppt", "pptx"].includes(ext)) return pptIcon();
  return defaultIcon();
}

function wordIcon(): string {
  return `<svg width="40" height="1em" viewBox="0 0 24 24" fill="none"><path d="M0 3C0 1.34315 1.34315 0 3 0H21C22.6569 0 24 1.34315 24 3V21C24 22.6569 22.6569 24 21 24H3C1.34315 24 0 22.6569 0 21V3Z" fill="#9152FF"></path><path d="M0 18.375H24V21C24 22.6569 22.6569 24 21 24H3C1.34315 24 0 22.6569 0 21V18.375Z" fill="#7C3DEA"></path><path d="M21 1.14742e-07C22.6569 1.87166e-07 24 1.34315 24 3L24 21.0968C24 22.7002 22.7002 24 21.0968 24C19.5936 24 18.375 22.7814 18.375 21.2782L18.375 0L21 1.14742e-07Z" fill="#A470FF"></path><path d="M18.375 21.2782C18.375 22.7814 19.5936 24 21.0968 24C22.7002 24 24 22.7002 24 21.0968C24 19.5936 22.7814 18.375 21.2782 18.375H18.375V21.2782Z" fill="#9152FF"></path><path d="M4.03125 4.78125H14.3438V6.65625H4.03125V4.78125Z" fill="white"></path><path d="M4.03125 8.25H14.3438V10.125H4.03125V8.25Z" fill="white"></path><path d="M10.4062 11.7188H4.03125V13.5938H10.4062V11.7188Z" fill="white"></path></svg>`;
}
function excelIcon(): string {
  return `<svg width="40" height="1em" viewBox="0 0 24 24" fill="none"><path d="M0 3C0 1.34315 1.34315 0 3 0H21C22.6569 0 24 1.34315 24 3V21C24 22.6569 22.6569 24 21 24H3C1.34315 24 0 22.6569 0 21V3Z" fill="#1EA623"></path><path d="M0 18.375H24V21C24 22.6569 22.6569 24 21 24H3C1.34315 24 0 22.6569 0 21V18.375Z" fill="#148F19"></path><path d="M21 1.14742e-07C22.6569 1.87166e-07 24 1.34315 24 3L24 21.0968C24 22.7002 22.7002 24 21.0968 24C19.5936 24 18.375 22.7814 18.375 21.2782L18.375 0L21 1.14742e-07Z" fill="#2ABF30"></path><path d="M18.375 21.2782C18.375 22.7814 19.5936 24 21.0968 24C22.7002 24 24 22.7002 24 21.0968C24 19.5936 22.7814 18.375 21.2782 18.375H18.375V21.2782Z" fill="#1EA623"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M3.65625 4.03125V14.3438H14.7188V4.03125H3.65625ZM12.8438 5.90625H10.125V8.25H12.8438V5.90625ZM12.8438 10.125H10.125V12.4688H12.8438V10.125ZM8.25 8.25V5.90625H5.53125V8.25H8.25ZM5.53125 10.125H8.25V12.4688H5.53125V10.125Z" fill="white"></path></svg>`;
}
function pptIcon(): string {
  return `<svg width="40" height="1em" viewBox="0 0 24 24" fill="none"><path d="M0 3C0 1.34315 1.34315 0 3 0H21C22.6569 0 24 1.34315 24 3V21C24 22.6569 22.6569 24 21 24H3C1.34315 24 0 22.6569 0 21V3Z" fill="#F5670F"></path><path d="M0 18.375H24V21C24 22.6569 22.6569 24 21 24H3C1.34315 24 0 22.6569 0 21V18.375Z" fill="#DF4D00"></path><path d="M21 1.14742e-07C22.6569 1.87166e-07 24 1.34315 24 3L24 21.0968C24 22.7002 22.7002 24 21.0968 24C19.5936 24 18.375 22.7814 18.375 21.2782L18.375 0L21 1.14742e-07Z" fill="#FF8940"></path><path d="M18.375 21.2782C18.375 22.7814 19.5936 24 21.0968 24C22.7002 24 24 22.7002 24 21.0968C24 19.5936 22.7814 18.375 21.2782 18.375H18.375V21.2782Z" fill="#F5670F"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M4.6875 3.5625H10.3125C12.3836 3.5625 14.0625 5.24143 14.0625 7.3125C14.0625 9.38357 12.3836 11.0625 10.3125 11.0625H6.5625V14.8125H4.6875V3.5625ZM6.5625 9.1875H10.3125C11.348 9.1875 12.1875 8.34803 12.1875 7.3125C12.1875 6.27697 11.348 5.4375 10.3125 5.4375H6.5625V9.1875Z" fill="white"></path></svg>`;
}
function defaultIcon(): string {
  return `<svg width="40" height="40" viewBox="0 0 40 40" fill="none"><rect width="40" height="40" rx="12" fill="#8B5CF6"/><rect x="12" y="10" width="16" height="20" rx="2" fill="white" fill-opacity="0.95"/><rect x="15" y="15" width="10" height="2.5" rx="1" fill="#8B5CF6"/><rect x="15" y="20" width="10" height="2.5" rx="1" fill="#8B5CF6"/><rect x="15" y="25" width="7" height="2.5" rx="1" fill="#8B5CF6"/></svg>`;
}

export function fileCard(item: FileMentionItem): HTMLElement {
  const link = document.createElement("a");
  link.className = "file-card";
  link.href = item.path || "#";
  link.target = "_blank";
  console.log("文件card1", item);
  // 图标
  const icon = document.createElement("span");
  icon.className = "file-card-icon";
  const filename = item.value || item.name || "";
  console.log("文件card2", filename);
  console.log("文件card3", getFileTypeIcon(filename));
  icon.innerHTML = getFileTypeIcon(filename);

  // 内容
  const content = document.createElement("div");
  content.className = "file-card-content";
  const title = document.createElement("div");
  let displayName = item.value || item.name || "未命名文件";
  displayName = removeFileExtension(displayName);
  title.textContent = displayName;
  title.className = "file-card-title";
  const meta = document.createElement("div");
  meta.className = "file-card-meta";
  const timeStr = item.file_ctime ? formatTimestamp(item.file_ctime) : "";
  const srcStr = item.file_src || "";
  meta.textContent = `${timeStr} | ${srcStr}`;
  content.appendChild(title);
  content.appendChild(meta);

  link.appendChild(icon);
  link.appendChild(content);
  link.dataset.name = item.name || "";
  link.dataset.value = item.value || "";
  link.dataset.denotationChar = item.denotationChar || "";
  link.dataset.id = item.fileid
    ? String(item.fileid)
    : item.id
      ? String(item.id)
      : "";

  return link;
}
