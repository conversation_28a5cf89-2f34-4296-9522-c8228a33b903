<template>
  <template v-if="slides.length">
    <Screen v-if="screening" />
    <Editor v-else-if="_isPC" />
    <Mobile v-else />
  </template>
  <FullscreenSpin tip="数据初始化中，请稍等 ..." v-else loading :mask="false" />
</template>

<script lang="ts" setup>
import { LOCALSTORAGE_KEY_DISCARDED_DB } from "@/configs/storage"
import api from "@/services"
import {
  useMainStore,
  useScreenStore,
  useSlidesStore,
  useSnapshotStore,
} from "@/store"
import { isPC } from "@/utils/common"
import { deleteDiscardedDB } from "@/utils/database"
import { storeToRefs } from "pinia"
import { onMounted } from "vue"

import FullscreenSpin from "@/components/FullscreenSpin.vue"
import Editor from "./views/Editor/index.vue"
import Mobile from "./views/Mobile/index.vue"
import Screen from "./views/Screen/index.vue"

const _isPC = isPC()

const mainStore = useMainStore()
const slidesStore = useSlidesStore()
const snapshotStore = useSnapshotStore()
const { databaseId } = storeToRefs(mainStore)
const { slides } = storeToRefs(slidesStore)
const { screening } = storeToRefs(useScreenStore())

if (import.meta.env.MODE !== "development") {
  window.onbeforeunload = () => false
}

onMounted(async () => {
  // 先检查 localStorage 是否有持久化数据
  const persistedData = localStorage.getItem("slides")
  if (persistedData) {
    try {
      const parsed = JSON.parse(persistedData)
      slidesStore.$patch(parsed) // 恢复数据到 Pinia
    } catch (error) {
      console.error("本地持久化数据解析失败:", error)
    }
  }

  // 如果 slides 仍然为空，再从 API 获取
  if (slidesStore.slides.length === 0) {
    const slidesData = await api.getFileData("slides")
    slidesStore.setSlides(slidesData)
  }

  // 清理和初始化逻辑
  await deleteDiscardedDB()
  snapshotStore.initSnapshotDatabase()
})

// 应用注销时向 localStorage 中记录下本次 indexedDB 的数据库ID，用于之后清除数据库
window.addEventListener("beforeunload", () => {
  const discardedDB = localStorage.getItem(LOCALSTORAGE_KEY_DISCARDED_DB)
  const discardedDBList: string[] = discardedDB ? JSON.parse(discardedDB) : []

  discardedDBList.push(databaseId.value)

  const newDiscardedDB = JSON.stringify(discardedDBList)
  localStorage.setItem(LOCALSTORAGE_KEY_DISCARDED_DB, newDiscardedDB)
})
</script>

<style lang="scss">
#app {
  height: 100%;
}
</style>
