import React from "react"

import type { ToolbarSectionProps } from "../type/types"
import { ToolbarRenderer } from "../utils/ToolbarRenderer"

import { exportToolbarConfig } from "../config/exportToolbarConfig"
import "../../common/css/header.css"
export const ExportToolbar: React.FC<ToolbarSectionProps> = (props) => {
  return (
    <ToolbarRenderer
      {...props}
      config={exportToolbarConfig}
      ariaLabel="导出工具栏"
      separatorClassName="toolbar-separator-vertical"
    />
  )
}
