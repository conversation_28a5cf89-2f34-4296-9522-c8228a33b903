import prettierConfig from "eslint-config-prettier";
import eslint<PERSON>lugin<PERSON>rettier from "eslint-plugin-prettier";
import eslintPluginPrettierRecommended from "eslint-plugin-prettier/recommended";
import eslintPluginReact from "eslint-plugin-react";
import { defineConfig } from "eslint/config";
import globals from "globals";
import tseslint from "typescript-eslint";

export default defineConfig([
  // 忽略文件
  {
    ignores: [
      ".vscode/**",
      "node_modules/**",
      "dist/**",
      "**/*.min.js",
      "src/**/*.d.ts",
      "package.json",
      ".git/**",
      ".prettierrc",
      ".editorconfig",
      "vite.config.js",
      ".whistle.js",
      ".dockerignore",
      "Dockerfile"
    ]
  },

  // TypeScript 推荐规则
  ...tseslint.configs.recommended,

  // 自定义规则
  {
    files: ["src/**/*.{js,ts,tsx,jsx}"],
    plugins: {
      prettier: eslintPluginPrettier,
      react: eslintPluginReact
    },
    rules: {
      "@typescript-eslint/no-unused-expressions": [
        "error",
        { allowShortCircuit: true }
      ]
    },

    languageOptions: {
      globals: {
        ...globals.browser
      },
      parserOptions: {
        ecmaFeatures: {
          jsx: true
        }
      }
    },
    settings: {
      react: {
        version: "detect"
      }
    }
  },

  // 关闭 ESLint 和 Prettier 冲突的规则
  prettierConfig,
  eslintPluginPrettierRecommended
]);
