import { fireEvent, render, screen } from "@testing-library/react"
import { beforeEach, describe, expect, it, vi } from "vitest"
import "vitest-dom/extend-expect"

import React from "react"
import TodoItem from "../components/TodoItem"
describe("TodoItem 组件", () => {
  let todo
  let onToggle
  let onDelete
  let onEdit
  let showDate
  beforeEach(() => {
    todo = {
      id: 1,
      text: "测试任务",
      completed: false,
      createdAt: new Date("2024-05-01T12:00:00"), // 传 Date 对象
    }
    showDate = true
    onToggle = vi.fn()
    onDelete = vi.fn()
    onEdit = vi.fn()
  })

  // 1. 应该正常渲染内容
  it("应该正常渲染内容", () => {
    render(
      <TodoItem
        todo={todo}
        onToggle={onToggle}
        onDelete={onDelete}
        onEdit={onEdit}
      />
    )
    expect(screen.getByText("测试任务")).toBeInTheDocument()
    expect(screen.getByRole("checkbox")).not.toBeChecked()
    expect(screen.getByTitle("编辑")).toBeInTheDocument()
    expect(screen.getByTitle("删除")).toBeInTheDocument()
  })

  // 2. 勾选切换完成状态，触发onToggle
  it("勾选切换完成状态，触发onToggle", () => {
    render(
      <TodoItem
        todo={todo}
        onToggle={onToggle}
        onDelete={onDelete}
        onEdit={onEdit}
      />
    )
    const checkbox = screen.getByRole("checkbox")
    fireEvent.click(checkbox)
    expect(onToggle).toHaveBeenCalledWith(todo.id)
  })

  // 3. 点击删除按钮触发onDelete
  it("点击删除按钮触发onDelete", () => {
    render(
      <TodoItem
        todo={todo}
        onToggle={onToggle}
        onDelete={onDelete}
        onEdit={onEdit}
      />
    )
    const delBtn = screen.getByTitle("删除")
    fireEvent.click(delBtn)
    expect(onDelete).toHaveBeenCalledWith(todo.id)
  })

  // 4. 点击编辑按钮触发onEdit
  it("点击编辑按钮触发onEdit", () => {
    render(
      <TodoItem
        todo={todo}
        onToggle={onToggle}
        onDelete={onDelete}
        onEdit={onEdit}
      />
    )
    const editBtn = screen.getByTitle("编辑")
    fireEvent.click(editBtn)
    expect(onEdit).toHaveBeenCalledWith(todo)
  })

  // 5. showDate为true时展示日期
  it("showDate为true时展示日期", () => {
    render(
      <TodoItem
        todo={todo}
        showDate={showDate}
        onToggle={onToggle}
        onDelete={onDelete}
        onEdit={onEdit}
      />
    )
    expect(
      screen.getByText((content) => content.includes("2024"))
    ).toBeInTheDocument()
  })

  // 6. 已完成时有completed样式
  it("已完成时有completed样式", () => {
    const completedTodo = { ...todo, completed: true }
    const { container } = render(
      <TodoItem
        todo={completedTodo}
        onToggle={onToggle}
        onDelete={onDelete}
        onEdit={onEdit}
      />
    )
    expect(container.querySelector(".todo-item")).toHaveClass("completed")
    expect(screen.getByRole("checkbox")).toBeChecked()
  })

  // 7. 删除/编辑按钮阻止事件冒泡（点击按钮不会触发onToggle）
  it("点击删除/编辑按钮不会触发onToggle", () => {
    render(
      <TodoItem
        todo={todo}
        onToggle={onToggle}
        onDelete={onDelete}
        onEdit={onEdit}
      />
    )
    const delBtn = screen.getByTitle("删除")
    const editBtn = screen.getByTitle("编辑")
    fireEvent.click(delBtn)
    fireEvent.click(editBtn)
    // onToggle 不应被调用
    expect(onToggle).not.toHaveBeenCalled()
  })
})
