import { describe, expect, it } from "vitest"
import {
  filterTodos,
  formatTodoDate,
  generateTodoId,
  getTodoStats,
  sortTodos,
  validateTodoText,
} from "../utils/todoUtils"

describe("validateTodoText", () => {
  it("空字符串或非字符串返回false", () => {
    expect(validateTodoText("")).toBe(false)
    expect(validateTodoText(null)).toBe(false)
    expect(validateTodoText(undefined)).toBe(false)
    expect(validateTodoText(123)).toBe(false)
  })
  it("只包含空格返回false", () => {
    expect(validateTodoText("   ")).toBe(false)
  })
  it("正常文本返回true", () => {
    expect(validateTodoText("hello")).toBe(true)
  })
  it("长度超过200返回false", () => {
    expect(validateTodoText("a".repeat(201))).toBe(false)
  })
  it("长度等于200返回true", () => {
    expect(validateTodoText("a".repeat(200))).toBe(true)
  })
})

describe("generateTodoId", () => {
  it("每次生成的id都不同且为number", () => {
    const id1 = generateTodoId()
    const id2 = generateTodoId()
    expect(typeof id1).toBe("number")
    expect(typeof id2).toBe("number")
    expect(id1).not.toBe(id2)
  })
})

describe("formatTodoDate", () => {
  it("无参数返回空字符串", () => {
    expect(formatTodoDate()).toBe("")
    expect(formatTodoDate(null)).toBe("")
  })
  it("格式化日期对象", () => {
    const date = new Date("2024-05-01T12:34:00")
    // 格式如：2024/05/01 12:34
    const result = formatTodoDate(date)
    expect(result).toMatch(/2024.*05.*01.*12.*34/)
  })
  it("处理数字类型的时间戳", () => {
    const timestamp = new Date("2024-05-01T12:34:00").getTime()
    const result = formatTodoDate(timestamp)
    expect(result).toMatch(/2024.*05.*01.*12.*34/)
  })
  it("处理无效日期返回空字符串", () => {
    expect(formatTodoDate("invalid date")).toBe("")
    expect(formatTodoDate(new Date("invalid"))).toBe("")
    expect(formatTodoDate({})).toBe("")
  })
})

describe("filterTodos", () => {
  const todos = [
    { text: "a", completed: false },
    { text: "b", completed: true },
    { text: "c", completed: false },
  ]
  it("默认返回全部", () => {
    expect(filterTodos(todos)).toEqual(todos)
    expect(filterTodos(todos, "all")).toEqual(todos)
  })
  it("active只返回未完成", () => {
    expect(filterTodos(todos, "active")).toEqual([
      { text: "a", completed: false },
      { text: "c", completed: false },
    ])
  })
  it("completed只返回已完成", () => {
    expect(filterTodos(todos, "completed")).toEqual([
      { text: "b", completed: true },
    ])
  })
})

describe("sortTodos", () => {
  const todos = [
    { text: "b", completed: false, createdAt: "2024-05-01T10:00:00" },
    { text: "a", completed: true, createdAt: "2024-05-02T10:00:00" },
    { text: "c", completed: false, createdAt: "2024-04-30T10:00:00" },
  ]
  it("默认按created降序", () => {
    const sorted = sortTodos(todos)
    expect(sorted[0].text).toBe("a")
    expect(sorted[1].text).toBe("b")
    expect(sorted[2].text).toBe("c")
  })
  it("按text排序", () => {
    const sorted = sortTodos(todos, "text")
    expect(sorted.map((t) => t.text)).toEqual(["a", "b", "c"])
  })
  it("按completed排序", () => {
    const sorted = sortTodos(todos, "completed")
    expect(sorted[0].completed).toBe(false)
    expect(sorted[2].completed).toBe(true)
  })
  it("createdAt缺失时按最早处理", () => {
    const t = [
      { text: "a", completed: false },
      { text: "b", completed: false, createdAt: "2024-05-01T10:00:00" },
    ]
    const sorted = sortTodos(t)
    expect(sorted[0].text).toBe("b")
    expect(sorted[1].text).toBe("a")
  })
  it("处理createdAt为null或undefined的情况", () => {
    const t = [
      { text: "a", completed: false, createdAt: null },
      { text: "b", completed: false, createdAt: undefined },
      { text: "c", completed: false, createdAt: "2024-05-01T10:00:00" },
    ]
    const sorted = sortTodos(t)
    // 有日期的应该排在前面
    expect(sorted[0].text).toBe("c")
    // null和undefined的应该排在后面，按照new Date(0)处理
    expect(sorted[1].text).toBe("a") // null
    expect(sorted[2].text).toBe("b") // undefined
  })
})

describe("getTodoStats", () => {
  it("统计全部、已完成、未完成和百分比", () => {
    const todos = [
      { completed: true },
      { completed: false },
      { completed: true },
    ]
    const stats = getTodoStats(todos)
    expect(stats.total).toBe(3)
    expect(stats.completed).toBe(2)
    expect(stats.active).toBe(1)
    expect(stats.percentage).toBe(67)
  })
  it("空数组百分比为0", () => {
    const stats = getTodoStats([])
    expect(stats.total).toBe(0)
    expect(stats.completed).toBe(0)
    expect(stats.active).toBe(0)
    expect(stats.percentage).toBe(0)
  })
})
