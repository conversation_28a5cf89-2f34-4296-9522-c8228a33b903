import axios from "axios"

// const http = axios.create({
//   withCredentials: true, // send cookies when cross-domain requests
//   timeout: 1800000, // request timeout 30分钟
//   ContentType: "application/json",
// })

// 映射mealType代码到中文名称
const MEAL_TYPE_MAP = {
  8: "早餐",
  9: "中餐",
  10: "晚餐",
}

export const fetchMealMenu = (canteenCode, date) => {
  // 验证输入
  if (!/^\d{4}-\d{2}-\d{2}$/.test(date)) {
    throw new Error("日期格式应为YYYY-MM-DD")
  }
  if (!canteenCode || typeof canteenCode !== "string") {
    throw new Error("食堂编码不能为空且必须为字符串")
  }

  return axios
    .get("/api/mealMenu/detail/h5", {
      params: { canteenCode, date },
    })
    .then((response) => {
      // 基础响应结构验证
      const { data } = response || {}
      if (!data || !data.data) {
        throw new Error("响应数据格式异常")
      }

      const { mealTimeIntervals = [], menu = {} } = data.data
      const mealPeriods = mealTimeIntervals.map((item) => item.period || "")

      // 安全获取餐次数据
      const mealTypeData = menu.mealTypeAndCategoryFoods || {}

      return {
        breakfast: formatMealItems(mealTypeData["8"] || []),
        lunch: formatMealItems(mealTypeData["9"] || []),
        dinner: formatMealItems(mealTypeData["10"] || []),
        breakfastTime: mealPeriods[0] || "",
        lunchTime: mealPeriods[1] || "",
        dinnerTime: mealPeriods[2] || "",
      }
    })
    .catch((error) => {
      console.error("获取菜单失败:", error)
      // 可以选择返回默认数据或抛出特定错误
      return {
        breakfast: [],
        lunch: [],
        dinner: [],
        breakfastTime: "",
        lunchTime: "",
        dinnerTime: "",
      }
    })
}
/**
 * 格式化单餐数据
 * @param {Array} mealSections 餐别下的各个档口数据
 * @returns {Array} 整理后的菜品列表（包含档口分类）
 */
const formatMealItems = (mealSections) => {
  return mealSections.map((section) => ({
    category: section.category, // 档口分类
    img: section.img,
    isTop: section.isTop,
    dishes: section.datas.map((dish) => ({
      name: dish.name,
      price: parseFloat(dish.price) || 0,
      img: dish.img,
      subTitle: dish.subTitle,
      intro: dish.intro,
    })),
  }))
}
