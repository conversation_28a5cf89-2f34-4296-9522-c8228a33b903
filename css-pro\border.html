<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <title>border</title>
    <link rel="stylesheet" href="style.css" />
  </head>
  <style>
    .border1 {
      width: 100px;
      height: 20px;
      border: solid 1px salmon;
      padding: 10px;
      margin: 10px;
    }
    .border2 {
      box-sizing: border-box;
      width: 100px;
      height: 60px; /* 增加高度以容纳p元素 */
      padding: 10px;
      margin: 10px;
      border: solid 1px #787878;
    }

    /* 重置p元素的默认margin */
    .border2 p {
      margin: 0;
    }
  </style>
  <body>
    <div class="container">
      <div class="border1">内容1</div>
      <div class="border2">
        <p>内容2</p>
      </div>
    </div>
  </body>
</html>
