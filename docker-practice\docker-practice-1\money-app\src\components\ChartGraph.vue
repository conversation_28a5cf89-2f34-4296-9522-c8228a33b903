<script setup lang="ts">
import * as echarts from 'echarts'
import { onMounted, defineProps, shallowRef, watch, nextTick } from 'vue'
export interface chartItem {
  name: string
  value: string | number
}
const props = defineProps(['width', 'height', 'data'])

const echartRef = shallowRef<HTMLElement>()
const echart = shallowRef<echarts.ECharts>()

onMounted(() => {
  nextTick(() => {
    initChart()
  })
  window.onresize = changeSize
})

const initChart = () => {
  if (echartRef.value) {
    echart.value = echarts.init(echartRef.value)
    setChartOptions(props.data)
  }
}

const setChartOptions = (data: chartItem[]) => {
  if (echart.value) {
    const options = {
      grid: {
        top: '20%',
        left: 0
      },
      series: [
        {
          type: 'pie',
          radius: '50%',
          data: data,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
    echart.value.setOption(options)
  }
}

// 监听 props.data 的变化，重新设置图表选项
watch(
  () => props.data,
  (newVal) => {
    setChartOptions(newVal)
  }
)

const changeSize = () => {
  echart.value && echart.value.resize()
}
</script>

<template>
  <div class="echarts-box">
    <div id="myEcharts" ref="echartRef" :style="{ width: props.width, height: props.height }"></div>
  </div>
</template>

<style scoped>
.echarts-box {
  margin-left: 10%;
}
</style>
