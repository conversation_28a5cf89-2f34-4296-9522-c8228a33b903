# 1. 构建阶段
FROM node:22-alpine AS builder

WORKDIR /app

# 先复制依赖文件，利用缓存
COPY package.json package-lock.json ./
RUN npm install

# 复制源代码
COPY . .

# 构建前端
RUN npm run build

# 2. 生产阶段
FROM nginx:alpine

# 拷贝构建产物到 nginx 静态目录
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置文件
# COPY nginx.conf /etc/nginx/nginx.conf
COPY --from=builder /app/dist/. /app/dist/
COPY --from=builder /app/dist/. /app/upload/${repoName}/
# 从builder阶段复制构建产物
# 只复制需要的dist目录，避免复制其他文件
COPY --from=builder /app/dist /usr/share/nginx/html

#本地要设置user为root才能成功运行容器
# 安全性最佳
USER root

# 暴露80端口
EXPOSE 80

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]