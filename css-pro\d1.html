<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>practice1</title>
  </head>
  <!--
  左侧导航宽度固定200px，上方固定80px，
  右侧可随屏幕宽度变化而变化，当屏幕宽度小于800px时，
  不再随着屏幕减小而是内容区出现水平滚动条。
  -->
  <style>
    .container {
      display: flex;
      flex-direction: column;
      height: 100vh;
      background-color: #12981f;
    }
    .container .header {
      height: 80px;
      background-color: #280fb2;
      border-bottom: 1px solid #ccc;
    }
    .main {
      display: flex;
      flex: 1;
      background-color: aqua;
    }
    .main .nav {
      width: 200px;
      background-color: #c61515;
      border-right: 1px solid #9fbd1d;
    }
    .main .content {
      flex: 1;
    }
    @media (max-width: 800px) {
      .container {
        width: 800px;
      }
      .main .content {
        width: 800px;
        overflow-x: auto;
      }
    }
  </style>
  <body>
    <div class="container">
      <div class="header"></div>
      <div class="main">
        <div class="nav"></div>
        <div class="content"></div>
      </div>
    </div>
  </body>
</html>
