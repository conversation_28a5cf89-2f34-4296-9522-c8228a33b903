<script setup lang="ts">
import { ref } from 'vue'
import { useMoney } from '@/hooks/useMoney'
import MessageBox from '@/components/MessageBox.vue'
const { category, tag, money, addMoney } = useMoney()
const message = ref('')

const showMessage = (msg: string) => {
  message.value = msg
}

const handleClose = () => {
  message.value = ''
}
const tool_items = ref(['1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '.'])

//监听每一个字
const handleClick = (val: string) => {
  console.log('点击键盘')
  if (val === '.' && money.value.includes('.')) {
    return
  }
  if (val !== '.' && money.value === '0') {
    money.value = val
  } else {
    money.value += val
  }
}

//清空
const handleClear = () => {
  money.value = '0'
}

//删除，一次删除一个
const handleDelete = () => {
  if (money.value.length === 1) {
    money.value = '0'
  } else {
    money.value = money.value.slice(0, -1)
  }
}

//提交(存储)
const handleSubmit = () => {
  if (tag.value !== '' && money.value !== '0') {
    addMoney(category.value, tag.value, money.value, new Date())
    money.value = '0'
    showMessage('添加成功!')
  } else {
    showMessage('请输入金额!')
  }
}
</script>

<template>
  <div class="tool-box">
    <div class="tool-header">
      <div class="one">{{ tag }}</div>
      <div class="two">￥{{ money }}</div>
    </div>
    <div class="tool-content">
      <div class="toolLeft">
        <div v-for="(item, index) in tool_items" :key="index" @click="handleClick(item)">
          {{ item }}
        </div>
      </div>
      <div class="toolRight">
        <div @click="handleDelete">删除</div>
        <div @click="handleClear">清空</div>
        <div @click="handleSubmit()">OK</div>
      </div>
    </div>
    <MessageBox
      v-if="message"
      :message="message"
      :style="{ backgroundColor: 'gray', color: 'white', padding: '10px', borderRadius: '10px' }"
      :duration="1000"
      @close="handleClose"
    />
  </div>
</template>

<style scoped>
.tool-box {
  left: 0;
  right: 0;
  bottom: 49px;
  position: fixed;
  background: hsla(0, 0%, 87.8%, 0.2);
  font-size: 15px;
  cursor: pointer;
}
.tool-content {
  display: flex;
}
.tool-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
}
.tool-header .one {
  padding-left: 15px;
}
.tool-header .two {
  padding-right: 25px;
}
.toolLeft {
  display: flex;
  align-items: center;
  justify-content: space-around;
  flex-wrap: wrap;
  width: 75%;
}
.toolLeft div {
  width: 33.3%;
  text-align: center;
}
.toolRight {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  width: 25%;
  padding: 0;
  margin: 0;
  height: 200px;
  text-align: center;
}
.toolRight div {
  height: 33.3%;
  line-height: 50px;
}
</style>
