import '@/assets/styles/font.scss'
import '@/assets/styles/global.scss'
import '@/assets/styles/prosemirror.scss'
import '@icon-park/vue-next/styles/index.css'
import 'animate.css'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import 'prosemirror-view/style/prosemirror.css'
import { createApp } from 'vue'
import App from './App.vue'

import Directive from '@/plugins/directive'
import Icon from '@/plugins/icon'

const app = createApp(App)
const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)
app.use(Icon)
app.use(Directive)
app.use(pinia)
app.mount('#app')
