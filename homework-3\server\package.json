{"name": "server", "version": "1.0.0", "main": "index.js", "scripts": {"start": "nodemon index.js", "test": "node test-simple-stream.js", "test-simple": "node test-simple.js", "test-basic": "node test-basic.js", "start-simple": "node start-simple.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.6.0", "koa": "^2.11.0", "koa-body": "^6.0.1", "koa-router": "^13.0.1", "koa-session": "^6.4.0", "koa-static": "^5.0.0"}, "devDependencies": {"nodemon": "^3.1.6"}}