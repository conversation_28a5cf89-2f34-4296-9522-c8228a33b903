import * as Tooltip from "@radix-ui/react-tooltip"
import React from "react"
import "../common/css/header.css"
import type { HeaderProps } from "./type/types"
import { FileControls } from "./views/FileControls"
import { TabNavigation } from "./views/TabNavigation"
import { UserControls } from "./views/UserControls"

export const Header: React.FC<HeaderProps> = ({ activeTab, onTabChange }) => {
  return (
    <Tooltip.Provider delayDuration={100}>
      <header className="header">
        <div className="header-container">
          <FileControls />
          <TabNavigation activeTab={activeTab} onTabChange={onTabChange} />
          <UserControls />
        </div>
      </header>
    </Tooltip.Provider>
  )
}
