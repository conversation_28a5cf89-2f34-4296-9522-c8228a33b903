import React, { useState } from 'react'
import './App.css'
import alarmMp3 from './assets/alarm.mp3'
import CountdownPanel from './components/CountdownPanel'
import CountdownSetupModal from './components/CountdownSetupModal'
import { useCountdownTimer } from './hooks/useCountdownTimer'

const App: React.FC = () => {
  // 控制倒计时设置弹窗是否显示
  const [isModalOpen, setIsModalOpen] = useState(false)
  // 记录倒计时设置（时、分、秒、响铃）
  const [setup, setSetup] = useState({
    hours: 0,
    minutes: 0,
    seconds: 0,
    ring: false,
  })

  // 集成 reset
  const {
    showCountdown: countdownShow,
    leftSeconds: countdownLeftSeconds,
    ring: countdownRing,
    ended: countdownEnded,
    start: countdownStart,
    close: countdownClose,
    toggleRing: countdownToggleRing,
  } = useCountdownTimer(alarmMp3)
  console.log('countdownEnded 状态:', countdownEnded)
  // 打开倒计时设置弹窗
  const showModal = () => setIsModalOpen(true)

  // 关闭倒计时设置弹窗
  const handleCancel = () => {
    setIsModalOpen(false)
    setSetup((prev) => ({
      ...prev,
      hours: 0,
      minutes: 0,
      seconds: 0,
      ring: false,
    }))
  }
  // 设置弹窗内容变更
  const handleSetupChange = (data: typeof setup) => setSetup(data)

  // 开始倒计时
  const handleStart = () => {
    const total = setup.hours * 3600 + setup.minutes * 60 + setup.seconds
    countdownStart(total, setup.ring)
    setIsModalOpen(false)
  }

  // 关闭倒计时
  const handlePanelClose = () => {
    //保留上一次设置的状态
    setSetup((prev) => ({
      ...prev,
      hours: prev.hours,
      minutes: prev.minutes,
      seconds: prev.seconds,
      ring: false,
    }))
    countdownClose()
  }

  // 切换响铃状态
  const handleToggleRing = (val: boolean) => {
    countdownToggleRing(val)
    setSetup((prev) => ({ ...prev, ring: val }))
  }

  // 直接关闭倒计时设置窗口
  const handleAfterClose = () => {
    if (!countdownShow) {
      setSetup((prev) => ({
        ...prev,
        hours: 0,
        minutes: 0,
        seconds: 0,
        ring: false,
      }))
    }
  }

  return (
    <div className="app-center-container">
      <button type="button" onClick={showModal} className="start-timer-btn">
        <svg
          className="icon"
          viewBox="0 0 1024 1024"
          xmlns="http://www.w3.org/2000/svg"
          width="32"
          height="32"
        >
          <path
            d="M533.333333 170.666667c90.112 0 173.184 29.909333 240.554667 79.829333l58.282667-58.282667a42.624 42.624 0 1 1 60.330666 60.330667l-55.68 55.722667A403.157333 403.157333 0 0 1 938.666667 576c0 223.488-181.845333 405.333333-405.333334 405.333333S128 799.488 128 576 309.845333 170.666667 533.333333 170.666667z m0 85.333333C356.864 256 213.333333 399.530667 213.333333 576S356.864 896 533.333333 896s320-143.530667 320-320S709.802667 256 533.333333 256zM512 384a42.666667 42.666667 0 0 1 42.666667 42.666667v147.84l109.013333 72.661333a42.709333 42.709333 0 0 1-47.36 70.997333l-128-85.333333A42.666667 42.666667 0 0 1 469.333333 597.333333v-170.666666a42.666667 42.666667 0 0 1 42.666667-42.666667z m170.666667-341.333333a42.666667 42.666667 0 1 1 0 85.333333H384a42.666667 42.666667 0 1 1 0-85.333333z"
            fill="#515151"
          />
        </svg>
        {countdownEnded ? '重新开始计时' : '开启倒计时'}
      </button>
      <CountdownSetupModal
        open={isModalOpen}
        hours={setup.hours}
        minutes={setup.minutes}
        seconds={setup.seconds}
        ring={setup.ring}
        onChange={handleSetupChange}
        onOk={handleStart}
        onCancel={handleCancel}
        afterClose={handleAfterClose}
        title="倒计时："
        tip="课间休息一会儿吧~"
      />
      <CountdownPanel
        leftSeconds={countdownLeftSeconds}
        visible={countdownShow}
        ring={countdownRing}
        ended={countdownEnded}
        onClose={handlePanelClose}
        onToggleRing={handleToggleRing}
      />
    </div>
  )
}

export default App
