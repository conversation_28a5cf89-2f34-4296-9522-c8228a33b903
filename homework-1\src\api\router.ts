import axios from "axios";
import {
  ChatSearchParams,
  Contact,
  ContactResponse,
  FetchFilePermissionParams,
  FileOperationRecord,
  fileResponse
} from "../type";

export async function fetchArticleList(
  params: { offset?: number; count?: number; without_sid?: boolean } = {}
): Promise<FileOperationRecord[]> {
  console.log("params", params);
  console.log("offset", params.offset ?? 0);
  console.log("count", params.count ?? 20);
  console.log("without_sid", params.without_sid ?? true);
  const response = await axios.get("https://woa.wps.cn/woa/api/v1/roaming", {
    params: {
      offset: 0,
      count: 20,
      without_sid: true
    },
    withCredentials: true
  });

  return response.data as FileOperationRecord[];
}

//文件访问路径，传递fileid
//chatid=47995027&cid=47995027是我的信息
//https://365.kdocs.cn/woa/api/v1/chats/47995027/file/permission?fileid=431642066703&chatid=47995027&cid=47995027
export async function fetchFilePermission(
  params: FetchFilePermissionParams
): Promise<fileResponse> {
  const { fileid, chatid, cid } = params;
  const url = `https://woa.wps.cn/woa/api/v1/chats/${chatid}/file/permission`;
  const response = await axios.get(url, {
    params: { fileid, chatid, cid },
    withCredentials: true
  });
  console.log("response-file:", response.data);
  return response.data as fileResponse;
}

// 联系人
export async function fetchChatList(
  params: ChatSearchParams
): Promise<Contact[]> {
  const res = await axios.get<ContactResponse>(
    "https://woa.wps.cn/woa/api/v3/search/chats",
    {
      params: {
        keyword: params.keyword ?? "",
        page_token: params.page_token ?? "",
        count: params.count ?? 20,
        search_type: params.search_type ?? 104,
        relation_version: params.relation_version ?? 1,
        // Axios 会自动将数组转换为多个同名参数
        search_scopes: params.search_scopes ?? [1, 2, 3]
      },
      withCredentials: true
    }
  );
  return res.data.contacts.list;
}
