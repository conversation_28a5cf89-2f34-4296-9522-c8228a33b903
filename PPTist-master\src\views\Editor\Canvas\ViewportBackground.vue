<template>
  <div class="viewport-background" :style="backgroundStyle">
    <GridLines v-if="gridLineSize" />
  </div>
</template>

<script lang="ts" setup>
import useSlideBackgroundStyle from "@/hooks/useSlideBackgroundStyle"
import { useMainStore, useSlidesStore } from "@/store"
import type { SlideBackground } from "@/types/slides"
import { storeToRefs } from "pinia"
import { computed } from "vue"
import GridLines from "./GridLines.vue"

const { gridLineSize } = storeToRefs(useMainStore())
const { currentSlide } = storeToRefs(useSlidesStore())
const background = computed<SlideBackground | undefined>(
  () => currentSlide.value?.background
)

console.log("🚀 ~~ background  🤖--EndLog--🤖", background)

const { backgroundStyle } = useSlideBackgroundStyle(background)

console.log("🚀 ~~ backgroundStyle 🤖--EndLog--🤖", backgroundStyle)
</script>

<style lang="scss" scoped>
.viewport-background {
  width: 100%;
  height: 100%;
  background-position: center;
  position: absolute;
}
</style>
