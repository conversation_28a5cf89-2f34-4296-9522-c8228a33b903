import React, { useRef, useState, useEffect, useCallback } from 'react';
import UploadButton from './UploadButton';
import ImagePreview from './ImagePreview';
import LoadingMask from './LoadingMask';
import FileName from './FileName';

interface ImageUploadPanelProps {
  label: string;
  alt: string;
  uploadFn: (file: File) => Promise<any>;
  className?: string;
  wrapClassName?: string;
  buttonOnly?: boolean; // true: 只按钮可点，false: 区域可点
}

const ImageUploadPanel = ({
  label,
  alt,
  uploadFn,
  className = '',
  wrapClassName = 'upload-wrap',
  buttonOnly = false
}: ImageUploadPanelProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [src, setSrc] = useState('');
  const [file, setFile] = useState<File | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (file) {
      const src = URL.createObjectURL(file);
      setSrc(src);
      return () => URL.revokeObjectURL(src);
    } else {
      setSrc('');
    }
  }, [file]);

  const openSelector = useCallback(() => {
    const selEvent = document.createEvent('MouseEvents');
    selEvent.initEvent('click', false, true);
    inputRef.current && inputRef.current.dispatchEvent(selEvent);
  }, []);

  const onFileChange = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    const { files } = e.target;
    if (!files || !files.length) return;
    const file = files[0];
    setFile(file);
    setIsLoading(true);
    await uploadFn(file);
    setIsLoading(false);
  }, [uploadFn]);

  return (
    <div className={className}>
      <div
        className={wrapClassName}
        onClick={buttonOnly ? undefined : openSelector}
      >
        <input
          type="file"
          ref={inputRef}
          style={{ display: 'none' }}
          accept={'jpg, jpeg, png'}
          onChange={onFileChange}
        />
        {
          !src && !isLoading &&
          <UploadButton onClick={buttonOnly ? openSelector : undefined}>{`上传${label}`}</UploadButton>
        }
        <ImagePreview src={src} alt={alt} />
        <LoadingMask loading={isLoading}>上传中...</LoadingMask>
      </div>
      <FileName file={file} label={label} />
    </div>
  );
};

export default ImageUploadPanel;