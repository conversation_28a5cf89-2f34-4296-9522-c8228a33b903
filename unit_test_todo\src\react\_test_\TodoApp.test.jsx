import React from "react"
import { vi } from "vitest"

// Mock子组件
vi.mock("../components/TodoInput.jsx", () => ({
  default: React.forwardRef(({ onSubmit, onCancel }, ref) => (
    <div data-testid="todo-input">
      <input
        ref={ref}
        data-testid="input-field"
        onKeyDown={(e) => {
          if (e.key === "Enter" && e.target.value.trim()) {
            onSubmit(e.target.value)
            e.target.value = ""
          }
        }}
      />
      <button data-testid="cancel-btn" onClick={onCancel}>
        取消
      </button>
    </div>
  )),
}))

vi.mock("../components/TodoItem.jsx", () => ({
  default: ({ todo, showDate, onToggle, onDelete, onEdit }) => (
    <div data-testid={`todo-item-${todo.id}`}>
      <span data-testid="todo-text">{todo.text}</span>
      <span data-testid="todo-completed">{todo.completed.toString()}</span>
      {showDate && <span data-testid="todo-date">有日期</span>}
      <button data-testid="toggle-btn" onClick={() => onToggle(todo.id)}>
        切换
      </button>
      <button data-testid="delete-btn" onClick={() => onDelete(todo.id)}>
        删除
      </button>
      <button data-testid="edit-btn" onClick={() => onEdit(todo)}>
        编辑
      </button>
    </div>
  ),
}))

vi.mock("../components/TodoStats.jsx", () => ({
  default: ({ stats, onClearCompleted }) => (
    <div data-testid="todo-stats">
      <span data-testid="stats-total">{stats.total}</span>
      <span data-testid="stats-completed">{stats.completed}</span>
      <span data-testid="stats-active">{stats.active}</span>
      <button data-testid="clear-completed-btn" onClick={onClearCompleted}>
        清除已完成
      </button>
    </div>
  ),
}))

// Mock hooks
vi.mock("../hooks/useTodoStore.js", () => ({
  useTodoStore: vi.fn(),
}))

// Mock utils
vi.mock("../utils/todoUtils.js", () => ({
  filterTodos: vi.fn(),
  getTodoStats: vi.fn(),
  sortTodos: vi.fn(),
}))

import { fireEvent, render, screen } from "@testing-library/react"
import { beforeEach, describe, expect, it } from "vitest"
import TodoApp from "../TodoApp.jsx"
import { useTodoStore } from "../hooks/useTodoStore.js"
import { filterTodos, getTodoStats, sortTodos } from "../utils/todoUtils.js"

describe("TodoApp", () => {
  const mockTodoStore = {
    todos: [],
    addTodo: vi.fn(),
    toggleTodo: vi.fn(),
    deleteTodo: vi.fn(),
    clearCompleted: vi.fn(),
    updateTodo: vi.fn(),
    uncompletedCount: 0,
    completedCount: 0,
    totalCount: 0,
  }

  const mockTodos = [
    {
      id: 1,
      text: "测试任务1",
      completed: false,
      createdAt: new Date("2024-01-01"),
    },
    {
      id: 2,
      text: "测试任务2",
      completed: true,
      createdAt: new Date("2024-01-02"),
    },
  ]

  beforeEach(() => {
    vi.clearAllMocks()
    useTodoStore.mockReturnValue(mockTodoStore)
    filterTodos.mockImplementation((todos, filter) => {
      switch (filter) {
        case "active":
          return todos.filter((todo) => !todo.completed)
        case "completed":
          return todos.filter((todo) => todo.completed)
        default:
          return todos
      }
    })
    sortTodos.mockImplementation((todos) => todos)
    getTodoStats.mockReturnValue({
      total: 2,
      completed: 1,
      active: 1,
    })
  })

  it("渲染基本结构", () => {
    render(<TodoApp />)

    expect(screen.getByText("TODO List")).toBeInTheDocument()
    expect(screen.getByTestId("todo-input")).toBeInTheDocument()
    expect(screen.getByTestId("todo-stats")).toBeInTheDocument()
  })

  it("渲染过滤器按钮", () => {
    render(<TodoApp />)

    expect(screen.getByText("全部")).toBeInTheDocument()
    expect(screen.getByText("进行中")).toBeInTheDocument()
    expect(screen.getByText("已完成")).toBeInTheDocument()
  })

  it("默认选中'全部'过滤器", () => {
    render(<TodoApp />)

    const allButton = screen.getByText("全部")
    expect(allButton).toHaveClass("active")
  })

  it("点击过滤器按钮切换过滤条件", () => {
    useTodoStore.mockReturnValue({
      ...mockTodoStore,
      todos: mockTodos,
    })

    render(<TodoApp />)

    const activeButton = screen.getByText("进行中")
    fireEvent.click(activeButton)

    expect(activeButton).toHaveClass("active")
    expect(screen.getByText("全部")).not.toHaveClass("active")
  })

  it("默认显示日期", () => {
    useTodoStore.mockReturnValue({
      ...mockTodoStore,
      todos: mockTodos,
    })

    render(<TodoApp />)

    // 默认showDate为true，所以应该显示日期
    expect(screen.getAllByTestId("todo-date")).toHaveLength(2)
  })

  it("渲染待办事项列表", () => {
    useTodoStore.mockReturnValue({
      ...mockTodoStore,
      todos: mockTodos,
    })

    render(<TodoApp />)

    expect(screen.getByTestId("todo-item-1")).toBeInTheDocument()
    expect(screen.getByTestId("todo-item-2")).toBeInTheDocument()
  })

  it("显示空状态消息", () => {
    filterTodos.mockReturnValue([])

    render(<TodoApp />)

    expect(screen.getByText("还没有待办项，开始添加吧！")).toBeInTheDocument()
  })

  it("处理添加待办事项", () => {
    render(<TodoApp />)

    const input = screen.getByTestId("input-field")
    fireEvent.keyDown(input, { key: "Enter", target: { value: "新任务" } })

    expect(mockTodoStore.addTodo).toHaveBeenCalledWith("新任务")
  })

  it("处理切换待办事项状态", () => {
    useTodoStore.mockReturnValue({
      ...mockTodoStore,
      todos: mockTodos,
    })

    render(<TodoApp />)

    const toggleButton = screen.getAllByTestId("toggle-btn")[0]
    fireEvent.click(toggleButton)

    expect(mockTodoStore.toggleTodo).toHaveBeenCalledWith(1)
  })

  it("处理删除待办事项", () => {
    useTodoStore.mockReturnValue({
      ...mockTodoStore,
      todos: mockTodos,
    })

    render(<TodoApp />)

    const deleteButton = screen.getAllByTestId("delete-btn")[0]
    fireEvent.click(deleteButton)

    expect(mockTodoStore.deleteTodo).toHaveBeenCalledWith(1)
  })

  it("处理编辑待办事项", () => {
    const consoleSpy = vi.spyOn(console, "log").mockImplementation(() => {})

    useTodoStore.mockReturnValue({
      ...mockTodoStore,
      todos: mockTodos,
    })

    render(<TodoApp />)

    const editButton = screen.getAllByTestId("edit-btn")[0]
    fireEvent.click(editButton)

    expect(consoleSpy).toHaveBeenCalledWith("编辑待办项:", mockTodos[0])

    consoleSpy.mockRestore()
  })

  it("处理清除已完成项", () => {
    render(<TodoApp />)

    const clearButton = screen.getByTestId("clear-completed-btn")
    fireEvent.click(clearButton)

    expect(mockTodoStore.clearCompleted).toHaveBeenCalled()
  })

  it("自动聚焦输入框", () => {
    // 测试useEffect被调用，通过检查组件是否正确渲染来验证
    render(<TodoApp />)

    // 验证输入框存在，这表明useEffect中的聚焦逻辑会被执行
    expect(screen.getByTestId("input-field")).toBeInTheDocument()
  })

  it("过滤器正确调用filterTodos", () => {
    useTodoStore.mockReturnValue({
      ...mockTodoStore,
      todos: mockTodos,
    })

    render(<TodoApp />)

    // 点击"进行中"过滤器
    const activeButton = screen.getByText("进行中")
    fireEvent.click(activeButton)

    expect(filterTodos).toHaveBeenCalledWith(mockTodos, "active")
  })

  it("正确调用getTodoStats", () => {
    useTodoStore.mockReturnValue({
      ...mockTodoStore,
      todos: mockTodos,
    })

    render(<TodoApp />)

    expect(getTodoStats).toHaveBeenCalledWith(mockTodos)
  })

  it("正确调用sortTodos", () => {
    useTodoStore.mockReturnValue({
      ...mockTodoStore,
      todos: mockTodos,
    })

    render(<TodoApp />)

    expect(sortTodos).toHaveBeenCalled()
  })

  it("传递showDate属性给TodoItem", () => {
    useTodoStore.mockReturnValue({
      ...mockTodoStore,
      todos: mockTodos,
    })

    render(<TodoApp />)

    // 默认showDate为true，应该显示日期
    expect(screen.getAllByTestId("todo-date")).toHaveLength(2)
  })

  it("处理取消编辑", () => {
    render(<TodoApp />)

    const cancelButton = screen.getByTestId("cancel-btn")
    fireEvent.click(cancelButton)

    // 取消操作不应该调用任何store方法
    expect(mockTodoStore.addTodo).not.toHaveBeenCalled()
    expect(mockTodoStore.updateTodo).not.toHaveBeenCalled()
  })

  it("显示不同过滤器的空状态消息", () => {
    filterTodos.mockReturnValue([])

    render(<TodoApp />)

    // 默认"全部"过滤器
    expect(screen.getByText("还没有待办项，开始添加吧！")).toBeInTheDocument()

    // 切换到"进行中"
    const activeButton = screen.getByText("进行中")
    fireEvent.click(activeButton)
    expect(screen.getByText("没有进行中的待办项")).toBeInTheDocument()

    // 切换到"已完成"
    const completedButton = screen.getByText("已完成")
    fireEvent.click(completedButton)
    expect(screen.getByText("没有已完成的待办项")).toBeInTheDocument()
  })

  it("成功添加待办事项后的处理", () => {
    mockTodoStore.addTodo.mockReturnValue(true)

    render(<TodoApp />)

    const input = screen.getByTestId("input-field")
    fireEvent.keyDown(input, { key: "Enter", target: { value: "新任务" } })

    expect(mockTodoStore.addTodo).toHaveBeenCalledWith("新任务")
  })

  it("成功删除待办事项后的处理", () => {
    mockTodoStore.deleteTodo.mockReturnValue(true)
    useTodoStore.mockReturnValue({
      ...mockTodoStore,
      todos: mockTodos,
    })

    render(<TodoApp />)

    const deleteButton = screen.getAllByTestId("delete-btn")[0]
    fireEvent.click(deleteButton)

    expect(mockTodoStore.deleteTodo).toHaveBeenCalledWith(1)
  })

  it("成功清除已完成项后的处理", () => {
    mockTodoStore.clearCompleted.mockReturnValue(2)

    render(<TodoApp />)

    const clearButton = screen.getByTestId("clear-completed-btn")
    fireEvent.click(clearButton)

    expect(mockTodoStore.clearCompleted).toHaveBeenCalled()
  })
})
