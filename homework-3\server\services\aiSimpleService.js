const crypto = require("crypto")
const axios = require("axios")
const aiConfig = require("../config/ai")
const { STREAM_PROMPT } = require("../config/prompt")

function generateActionId() {
  return crypto.randomBytes(16).toString("hex")
}

function buildAIRequest(prompt) {
  return JSON.stringify({
    messages: [
      {
        role: "system",
        content: STREAM_PROMPT,
      },
      {
        role: "user",
        content: `请为以下主题生成思维导图：${prompt}`,
      },
    ],
    stream: true,
    model: aiConfig.modle_name,
    max_tokens: 100,
    temperature: 0.7,
    stop: ["STOP", "生成结束", "end_of_turn"],
  })
}

/**
 * 解析一行markdown为节点
 * @param {string} line
 * @returns {object|null}
 */
function parseLineToNode(line) {
  const trimmed = line.trim()
  if (!trimmed.startsWith("-")) return null
  const indent = line.search(/\S|$/)
  const level = Math.floor(indent / 2) // 每2个空格为一级
  const text = trimmed.slice(1).trim()
  if (!text) return null
  return {
    id: `node_${Date.now()}_${Math.random().toString(36).slice(2, 8)}`,
    text,
    level,
    children: [], // 始终为空数组，让前端建立关系
    parentId: null, // 将在后续处理中设置
  }
}

/**
 * 构建节点树结构
 * @param {Array} nodes 节点数组
 * @returns {object} 根节点和所有节点的映射
 */
function buildNodeTree(nodes) {
  if (nodes.length === 0) return { root: null, nodeMap: {} }

  const nodeMap = {}
  const stack = [] // 用于跟踪每个层级的最后一个节点

  // 创建根节点
  const rootNode = {
    id: "root",
    text: nodes[0].text, // 使用第一个节点的文本作为根节点
    level: 0,
    children: [],
    parentId: null,
  }
  nodeMap["root"] = rootNode
  stack[0] = rootNode

  // 处理剩余节点
  for (let i = 1; i < nodes.length; i++) {
    const currentNode = nodes[i]
    const currentLevel = currentNode.level

    // 找到父节点（上一个层级更小的节点）
    let parentNode = null
    for (let j = currentLevel - 1; j >= 0; j--) {
      if (stack[j]) {
        parentNode = stack[j]
        break
      }
    }

    if (parentNode) {
      currentNode.parentId = parentNode.id
      parentNode.children.push(currentNode.id)
    }

    // 更新stack
    stack[currentLevel] = currentNode
    // 清除更深层级的节点
    for (let k = currentLevel + 1; k < stack.length; k++) {
      stack[k] = null
    }

    nodeMap[currentNode.id] = currentNode
  }

  return { root: rootNode, nodeMap }
}

/**
 * 发送AI请求，流式返回解析后的节点
 * @param {string} prompt
 * @param {function} onData ({type: 'node'|'done', node?}) 回调
 * @param {function} onError (err) 错误回调
 * @param {function} onEnd () 完成回调
 */
async function sendAIRequest(prompt, onData, onError, onEnd) {
  try {
    const requestData = buildAIRequest(prompt)

    const response = await axios({
      method: "POST",
      url: `https://${aiConfig.AI_HOST_URL}/api/v3/chat/completions`,
      headers: {
        Authorization: `Bearer ${aiConfig.aiToken}`,
        "Content-Type": "application/json",
      },
      data: requestData,
      responseType: "stream",
      timeout: 30000,
    })

    let lineBuffer = ""
    let shouldStop = false
    let nodeStack = [] // 用于维护节点层级关系
    let allNodes = [] // 存储所有节点
    let rootNodeSent = false // 标记根节点是否已发送

    response.data.on("data", (chunk) => {
      if (shouldStop) return

      const chunkStr = chunk.toString()
      const lines = chunkStr.split("\n")

      for (const line of lines) {
        console.log("🚀 ~~ sendAIRequest ~~ line  🤖--EndLog--🤖", line)

        if (shouldStop) break
        if (!line.trim()) continue
        if (!line.startsWith("data: ")) continue

        const data = line.slice(6).trim()

        if (data === "[DONE]") {
          shouldStop = true
          break
        }

        try {
          const parsed = JSON.parse(data)
          const content = parsed.choices?.[0]?.delta?.content
          if (content) {
            lineBuffer += content

            let newlineIndex
            while ((newlineIndex = lineBuffer.indexOf("\n")) !== -1) {
              const oneLine = lineBuffer.slice(0, newlineIndex)
              lineBuffer = lineBuffer.slice(newlineIndex + 1)

              const node = parseLineToNode(oneLine)
              if (node) {
                // 建立父子关系
                if (allNodes.length === 0) {
                  // 设置虚拟根节点到栈中（用户输入的根节点）
                  const virtualRoot = {
                    id: "root",
                    text: "用户输入的根节点",
                    level: -1, // 虚拟层级，比所有AI节点都高
                    children: [],
                    parentId: null,
                  }
                  nodeStack[-1] = virtualRoot
                  rootNodeSent = true
                }

                // 调整level：AI的level需要+1，因为用户输入的根节点占据了level=0
                const adjustedLevel = node.level + 1
                node.level = adjustedLevel

                if (adjustedLevel === 1) {
                  // AI返回的原level=0节点，现在是level=1，作为用户根节点的子节点
                  node.parentId = "root"
                } else {
                  // 找到父节点
                  let parentNode = null
                  for (let i = adjustedLevel - 1; i >= 0; i--) {
                    if (nodeStack[i]) {
                      parentNode = nodeStack[i]
                      break
                    }
                  }

                  if (parentNode) {
                    node.parentId = parentNode.id
                  } else {
                    // 如果找不到父节点，默认设为root的子节点
                    console.warn(
                      `⚠️ 节点 ${node.id} 找不到父节点，设为root子节点`
                    )
                    node.parentId = "root"
                  }
                }

                // 清空children数组，让前端自己建立父子关系
                node.children = []

                // 更新栈
                nodeStack[adjustedLevel] = node
                // 清除更深层级的节点
                for (let k = adjustedLevel + 1; k < nodeStack.length; k++) {
                  nodeStack[k] = null
                }

                allNodes.push(node)

                // 创建节点的副本用于发送，避免后续修改影响已发送的数据
                const nodeToSend = {
                  id: node.id,
                  text: node.text,
                  level: node.level,
                  children: [], // 始终为空数组，让前端建立关系
                  parentId: node.parentId,
                }

                // 添加延迟发送，模拟真正的流式效果
                const delay = allNodes.length * 100 // 每个节点延迟100ms
                setTimeout(() => {
                  console.log(`📤 准备发送节点: ${JSON.stringify(nodeToSend)}`)
                  onData && onData({ type: "node", node: nodeToSend })
                }, delay)
              }
            }

            if (
              lineBuffer.includes("STOP") ||
              lineBuffer.includes("生成结束")
            ) {
              // 标记应该停止，但不立即发送done消息
              shouldStop = true
              console.log("🛑 检测到结束信号，标记停止")
            }
          }
        } catch (e) {
          console.error("解析错误:", e.message)
        }
      }
    })

    response.data.on("end", () => {
      if (lineBuffer.trim()) {
        const node = parseLineToNode(lineBuffer)
        if (node) {
          // 处理最后一个节点的父子关系
          if (allNodes.length === 0) {
            node.id = "root"
            node.level = 0
            node.parentId = null
          } else {
            let parentNode = null
            for (let i = node.level - 1; i >= 0; i--) {
              if (nodeStack[i]) {
                parentNode = nodeStack[i]
                break
              }
            }

            if (parentNode) {
              node.parentId = parentNode.id
            }
          }

          allNodes.push(node)

          // 创建节点的副本用于发送
          const nodeToSend = {
            id: node.id,
            text: node.text,
            level: node.level,
            children: [], // 始终为空数组
            parentId: node.parentId,
          }

          console.log(`📤 准备发送最后节点: ${JSON.stringify(nodeToSend)}`)
          onData && onData({ type: "node", node: nodeToSend })
        }
      }

      // 在所有数据处理完毕后发送done消息
      const finalDelay = (allNodes.length + 1) * 100
      setTimeout(() => {
        console.log("🏁 发送done消息，所有节点处理完毕")
        onData && onData({ type: "done" })
        onEnd && onEnd()
      }, finalDelay)
    })

    response.data.on("error", (err) => {
      onError && onError(err.message)
    })
  } catch (error) {
    onError && onError(error.message)
  }
}

module.exports = {
  sendAIRequest,
  buildAIRequest,
  generateActionId,
}
