import React, { useEffect, useState } from "react"
import AnswerItem from "../../components/AnswerItem"
import { questions } from "../../constants/question"
import "./index.css"
interface QuestionAnswer {
  questionId: string
  questionTitle: string
  selectedOption: string | null
}

const Answer: React.FC = () => {
  const [correctNumber, setCorrectNumber] = useState(0)
  const getAllAnswers = (): QuestionAnswer[] => {
    const answers: QuestionAnswer[] = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key?.startsWith("question-")) {
        const value = localStorage.getItem(key)
        if (value) {
          const val = JSON.parse(value)
          answers.push(val)
        }
      }
    }
    return answers.sort((a, b) => Number(a.questionId) - Number(b.questionId))
  }

  const answers = getAllAnswers()

  // 计算答对的题目数
  useEffect(() => {
    const correctAnswers = answers.filter((item) => {
      const questionData = questions.find((q) => q.id === item.questionId)
      let uanswer
      if (item.selectedOption) {
        uanswer = item.selectedOption.split(".")[0]
      }
      return (
        questionData?.answer ===
        (item.selectedOption ? uanswer : item.selectedOption)
      )
    }).length

    setCorrectNumber(correctAnswers)
  }, [answers])

  return (
    <div className="answer">
      <h1>答题结果</h1>
      <h2>
        答对题数：{correctNumber}/{questions.length}
      </h2>
      <div className="answer-container">
        {answers.map((item) => {
          const questionData = questions.find((q) => q.id === item.questionId)
          const correctAnswer = questionData?.answer
          return (
            <AnswerItem
              key={item.questionId}
              question={item.questionTitle}
              answer={correctAnswer || ""}
              userAnswer={item.selectedOption || ""}
            />
          )
        })}
      </div>
    </div>
  )
}

export default Answer
