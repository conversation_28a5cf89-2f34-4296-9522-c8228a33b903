

const uploadImg = (file: File) => {
    return new Promise<boolean>((resolve) => {
        setTimeout(() => {
            console.log('上传图片中... file:', file)
            resolve(true)
        }, 2000)
    })
}

const uploadCover = (file: File) => {
    return new Promise<boolean>((resolve) => {
        setTimeout(() => {
            console.log('上传封面中... file:', file)
            resolve(true)
        }, 2000)
    })
}

export {
    uploadImg,
    uploadCover,
}