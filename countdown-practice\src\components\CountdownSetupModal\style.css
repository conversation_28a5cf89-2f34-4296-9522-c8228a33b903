/* 只在hover时显示加减按钮 */
.custom-input-number .ant-input-number-handler-wrap {
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s;
}

.custom-input-number:hover .ant-input-number-handler-wrap {
  opacity: 1;
  pointer-events: auto;
}

/* 纵向排列在输入框上下两侧 */
.custom-input-number .ant-input-number-handler-wrap {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  pointer-events: none;
}

.custom-input-number:hover .ant-input-number-handler-wrap {
  pointer-events: auto;
}

.custom-input-number .ant-input-number-handler-up,
.custom-input-number .ant-input-number-handler-down {
  position: static;
  width: 100%;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  box-shadow: none;
}

.custom-input-number .ant-input-number-handler-up {
  order: 0;
}

.custom-input-number .ant-input-number-handler-down {
  order: 1;
}

/* 保证输入框内容不被遮挡 */
.custom-input-number .ant-input-number-input {
  text-align: center;
  font-size: 24px;
  color: #888;
  background: transparent;
  z-index: 1;
}

.custom-input-number {
  position: relative;
  overflow: visible;
}

.countdown-setup-title {
  margin-bottom: 12px;
  font-weight: 500;
}

.countdown-setup-tip {
  margin-bottom: 8px;
  color: #888;
}

.countdown-setup-inputs {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.countdown-setup-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.countdown-setup-btn {
  min-width: 120px;
}
