{"name": "clock_down", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "prettier:write": "prettier --write .", "prettier:check": "prettier --check ."}, "dependencies": {"antd": "^5.26.5", "react": "18.3.1", "react-dom": "18.3.1"}, "devDependencies": {"@eslint/js": "^9.31.0", "@types/node": "^24.0.14", "@types/react": "18.3.1", "@types/react-dom": "18.3.1", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "prettier": "^3.6.2", "terser": "^5.43.1", "typescript": "~5.8.3", "typescript-eslint": "^8.37.0", "vite": "^7.0.4"}}