lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@testing-library/jest-dom':
        specifier: ^5.16.5
        version: 5.17.0
      '@testing-library/react':
        specifier: ^13.4.0
        version: 13.4.0(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@testing-library/user-event':
        specifier: ^13.5.0
        version: 13.5.0(@testing-library/dom@8.20.1)
      '@types/jest':
        specifier: ^27.5.2
        version: 27.5.2
      '@types/node':
        specifier: ^16.18.36
        version: 16.18.126
      '@types/react':
        specifier: ^18.2.12
        version: 18.3.23
      '@types/react-dom':
        specifier: ^18.2.5
        version: 18.3.7(@types/react@18.3.23)
      classnames:
        specifier: ^2.3.2
        version: 2.5.1
      craco-less:
        specifier: ^2.0.0
        version: 2.0.0(@craco/craco@7.1.0(@types/node@16.18.126)(postcss@8.5.6)(react-scripts@5.0.1(@babel/plugin-syntax-flow@7.27.1(@babel/core@7.28.0))(@babel/plugin-transform-react-jsx@7.27.1(@babel/core@7.28.0))(@types/babel__core@7.20.5)(eslint@8.57.1)(react@18.3.1)(ts-node@10.9.2(@types/node@16.18.126)(typescript@4.9.5))(type-fest@0.21.3)(typescript@4.9.5))(typescript@4.9.5))(react-scripts@5.0.1(@babel/plugin-syntax-flow@7.27.1(@babel/core@7.28.0))(@babel/plugin-transform-react-jsx@7.27.1(@babel/core@7.28.0))(@types/babel__core@7.20.5)(eslint@8.57.1)(react@18.3.1)(ts-node@10.9.2(@types/node@16.18.126)(typescript@4.9.5))(type-fest@0.21.3)(typescript@4.9.5))(webpack@5.100.0)
      postcss:
        specifier: ^8.4.25
        version: 8.5.6
      react:
        specifier: ^18.2.0
        version: 18.3.1
      react-dom:
        specifier: ^18.2.0
        version: 18.3.1(react@18.3.1)
      react-router-dom:
        specifier: ^6.14.1
        version: 6.30.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react-scripts:
        specifier: 5.0.1
        version: 5.0.1(@babel/plugin-syntax-flow@7.27.1(@babel/core@7.28.0))(@babel/plugin-transform-react-jsx@7.27.1(@babel/core@7.28.0))(@types/babel__core@7.20.5)(eslint@8.57.1)(react@18.3.1)(ts-node@10.9.2(@types/node@16.18.126)(typescript@4.9.5))(type-fest@0.21.3)(typescript@4.9.5)
      typescript:
        specifier: ^4.9.5
        version: 4.9.5
      web-vitals:
        specifier: ^2.1.4
        version: 2.1.4
    devDependencies:
      '@craco/craco':
        specifier: ^7.1.0
        version: 7.1.0(@types/node@16.18.126)(postcss@8.5.6)(react-scripts@5.0.1(@babel/plugin-syntax-flow@7.27.1(@babel/core@7.28.0))(@babel/plugin-transform-react-jsx@7.27.1(@babel/core@7.28.0))(@types/babel__core@7.20.5)(eslint@8.57.1)(react@18.3.1)(ts-node@10.9.2(@types/node@16.18.126)(typescript@4.9.5))(type-fest@0.21.3)(typescript@4.9.5))(typescript@4.9.5)
      '@craco/types':
        specifier: ^7.1.0
        version: 7.1.0(eslint@8.57.1)(postcss@8.5.6)
      less:
        specifier: ^4.1.3
        version: 4.3.0
      less-loader:
        specifier: ^11.1.3
        version: 11.1.4(less@4.3.0)(webpack@5.100.0)
      tailwindcss:
        specifier: ^3.3.2
        version: 3.4.17(ts-node@10.9.2(@types/node@16.18.126)(typescript@4.9.5))

packages:

  '@adobe/css-tools@4.4.3':
    resolution: {integrity: sha1-vuu++wJk/esy0wUqyuDg2UMVqaI=, tarball: https://registry.npm.wps.cn/@adobe/css-tools/download/@adobe/css-tools-4.4.3.tgz}

  '@alloc/quick-lru@5.2.0':
    resolution: {integrity: sha1-e/aLIMCjUPk2kV/K4G9Y4yAHzjA=, tarball: https://registry.npm.wps.cn/@alloc/quick-lru/download/@alloc/quick-lru-5.2.0.tgz}
    engines: {node: '>=10'}

  '@ampproject/remapping@2.3.0':
    resolution: {integrity: sha1-7UQbb6YAByUgzhi0PSyMyMrsx/Q=, tarball: https://registry.npm.wps.cn/@ampproject/remapping/download/@ampproject/remapping-2.3.0.tgz}
    engines: {node: '>=6.0.0'}

  '@apideck/better-ajv-errors@0.3.6':
    resolution: {integrity: sha1-lX1MKOiGpkqBQfdSJ4O+ZXM/8Jc=, tarball: https://registry.npm.wps.cn/@apideck/better-ajv-errors/download/@apideck/better-ajv-errors-0.3.6.tgz}
    engines: {node: '>=10'}
    peerDependencies:
      ajv: '>=8'

  '@babel/code-frame@7.27.1':
    resolution: {integrity: sha1-IA9xXmbVKiOyIalDVTSpHME61b4=, tarball: https://registry.npm.wps.cn/@babel/code-frame/download/@babel/code-frame-7.27.1.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/compat-data@7.28.0':
    resolution: {integrity: sha1-n8b9WMKmoVJDzROYMiSWg5IHB5A=, tarball: https://registry.npm.wps.cn/@babel/compat-data/download/@babel/compat-data-7.28.0.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/core@7.28.0':
    resolution: {integrity: sha1-VdrYCNW/NEWhCO78iOo/3wNHSaQ=, tarball: https://registry.npm.wps.cn/@babel/core/download/@babel/core-7.28.0.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/eslint-parser@7.28.0':
    resolution: {integrity: sha1-wbP7ugcPW6wy49AvJEIBrdSv3W4=, tarball: https://registry.npm.wps.cn/@babel/eslint-parser/download/@babel/eslint-parser-7.28.0.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || >=14.0.0}
    peerDependencies:
      '@babel/core': ^7.11.0
      eslint: ^7.5.0 || ^8.0.0 || ^9.0.0

  '@babel/generator@7.28.0':
    resolution: {integrity: sha1-nML3vW6wVNd9xmwmZBSKDFEYrNI=, tarball: https://registry.npm.wps.cn/@babel/generator/download/@babel/generator-7.28.0.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-annotate-as-pure@7.27.3':
    resolution: {integrity: sha1-8x/Ya5FfxNrx86xpdsWb5whO2cU=, tarball: https://registry.npm.wps.cn/@babel/helper-annotate-as-pure/download/@babel/helper-annotate-as-pure-7.27.3.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-compilation-targets@7.27.2':
    resolution: {integrity: sha1-RqD276uAjVHSnOloWN0Qzocycz0=, tarball: https://registry.npm.wps.cn/@babel/helper-compilation-targets/download/@babel/helper-compilation-targets-7.27.2.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-create-class-features-plugin@7.27.1':
    resolution: {integrity: sha1-W+5CYqbqXdyFLQgGGZ6xfKPekoE=, tarball: https://registry.npm.wps.cn/@babel/helper-create-class-features-plugin/download/@babel/helper-create-class-features-plugin-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-create-regexp-features-plugin@7.27.1':
    resolution: {integrity: sha1-BbCILZe6HU0DUZ5LzmFdcK+hjFM=, tarball: https://registry.npm.wps.cn/@babel/helper-create-regexp-features-plugin/download/@babel/helper-create-regexp-features-plugin-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-define-polyfill-provider@0.6.5':
    resolution: {integrity: sha1-dCzPHLADwHtIhZ/J+iwbvkDl91M=, tarball: https://registry.npm.wps.cn/@babel/helper-define-polyfill-provider/download/@babel/helper-define-polyfill-provider-0.6.5.tgz}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  '@babel/helper-globals@7.28.0':
    resolution: {integrity: sha1-uUMN8qpOF7woZl6t6uiqHZheZnQ=, tarball: https://registry.npm.wps.cn/@babel/helper-globals/download/@babel/helper-globals-7.28.0.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-member-expression-to-functions@7.27.1':
    resolution: {integrity: sha1-6hIRJ2vpPnmM4ZA32m8G+7mU+kQ=, tarball: https://registry.npm.wps.cn/@babel/helper-member-expression-to-functions/download/@babel/helper-member-expression-to-functions-7.27.1.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-imports@7.27.1':
    resolution: {integrity: sha1-fvdpoyPiZV4SZnO7bS1pE7vq0gQ=, tarball: https://registry.npm.wps.cn/@babel/helper-module-imports/download/@babel/helper-module-imports-7.27.1.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-transforms@7.27.3':
    resolution: {integrity: sha1-2wu8+6WAL573hwcFp++HiFCO3gI=, tarball: https://registry.npm.wps.cn/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.27.3.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-optimise-call-expression@7.27.1':
    resolution: {integrity: sha1-xlIhthpkPz5icF5d0rXxFeNfkgA=, tarball: https://registry.npm.wps.cn/@babel/helper-optimise-call-expression/download/@babel/helper-optimise-call-expression-7.27.1.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-plugin-utils@7.27.1':
    resolution: {integrity: sha1-3bL4dlNP+AE+bCspm/TTmzxR1Ew=, tarball: https://registry.npm.wps.cn/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.27.1.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-remap-async-to-generator@7.27.1':
    resolution: {integrity: sha1-RgHVx84usq6lgyjUNyVSP802LOY=, tarball: https://registry.npm.wps.cn/@babel/helper-remap-async-to-generator/download/@babel/helper-remap-async-to-generator-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-replace-supers@7.27.1':
    resolution: {integrity: sha1-se0tY0zjvbcw5LUt4w+MzP1pK8A=, tarball: https://registry.npm.wps.cn/@babel/helper-replace-supers/download/@babel/helper-replace-supers-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-skip-transparent-expression-wrappers@7.27.1':
    resolution: {integrity: sha1-YruRs6u6jH8f7AJS2dvqEbPuelY=, tarball: https://registry.npm.wps.cn/@babel/helper-skip-transparent-expression-wrappers/download/@babel/helper-skip-transparent-expression-wrappers-7.27.1.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-string-parser@7.27.1':
    resolution: {integrity: sha1-VNp5YJerGc5n7Z+ItHuy7Ek2doc=, tarball: https://registry.npm.wps.cn/@babel/helper-string-parser/download/@babel/helper-string-parser-7.27.1.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-identifier@7.27.1':
    resolution: {integrity: sha1-pwVNzBRaln3U3I/uhFpXwTFsnfg=, tarball: https://registry.npm.wps.cn/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.27.1.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-option@7.27.1':
    resolution: {integrity: sha1-+lL1sefbGrBJRFtCHERxMDiXcC8=, tarball: https://registry.npm.wps.cn/@babel/helper-validator-option/download/@babel/helper-validator-option-7.27.1.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-wrap-function@7.27.1':
    resolution: {integrity: sha1-uIKFAJwxQnrzGNT+N2Uc1ioUJAk=, tarball: https://registry.npm.wps.cn/@babel/helper-wrap-function/download/@babel/helper-wrap-function-7.27.1.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helpers@7.27.6':
    resolution: {integrity: sha1-ZFb+0VsstmnS0fq+hLZrNJkdgSw=, tarball: https://registry.npm.wps.cn/@babel/helpers/download/@babel/helpers-7.27.6.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/parser@7.28.0':
    resolution: {integrity: sha1-l5gp+6tRop4TkB5agHE9vLhAgl4=, tarball: https://registry.npm.wps.cn/@babel/parser/download/@babel/parser-7.28.0.tgz}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.27.1':
    resolution: {integrity: sha1-Yd2KjmH361aCaNG18SnaPu42S/k=, tarball: https://registry.npm.wps.cn/@babel/plugin-bugfix-firefox-class-in-computed-class-key/download/@babel/plugin-bugfix-firefox-class-in-computed-class-key-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-bugfix-safari-class-field-initializer-scope@7.27.1':
    resolution: {integrity: sha1-Q/cKbX79UjcO7731WuA9kbKThW0=, tarball: https://registry.npm.wps.cn/@babel/plugin-bugfix-safari-class-field-initializer-scope/download/@babel/plugin-bugfix-safari-class-field-initializer-scope-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.27.1':
    resolution: {integrity: sha1-vrYjvVc7i28wR70EwyUGrcPlinI=, tarball: https://registry.npm.wps.cn/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/download/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.27.1':
    resolution: {integrity: sha1-4TSlR56yupwCcU6MHr8eyQdhJP0=, tarball: https://registry.npm.wps.cn/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/download/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.13.0

  '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.27.1':
    resolution: {integrity: sha1-uxwlrzTXURXOIpod5/pEv4+VVnA=, tarball: https://registry.npm.wps.cn/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly/download/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-proposal-class-properties@7.18.6':
    resolution: {integrity: sha1-sRD1l0GJX37CGm//aW7EYmXERqM=, tarball: https://registry.npm.wps.cn/@babel/plugin-proposal-class-properties/download/@babel/plugin-proposal-class-properties-7.18.6.tgz}
    engines: {node: '>=6.9.0'}
    deprecated: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead.
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-decorators@7.28.0':
    resolution: {integrity: sha1-QZyKzDEIjgWndDRMAhgA993Dm/A=, tarball: https://registry.npm.wps.cn/@babel/plugin-proposal-decorators/download/@babel/plugin-proposal-decorators-7.28.0.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-nullish-coalescing-operator@7.18.6':
    resolution: {integrity: sha1-/dlAqZp0Dld9bHU6tvu0P9uUZ+E=, tarball: https://registry.npm.wps.cn/@babel/plugin-proposal-nullish-coalescing-operator/download/@babel/plugin-proposal-nullish-coalescing-operator-7.18.6.tgz}
    engines: {node: '>=6.9.0'}
    deprecated: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-nullish-coalescing-operator instead.
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-numeric-separator@7.18.6':
    resolution: {integrity: sha1-iZsU+6/ofwU9LF/wWzYCnGLhPHU=, tarball: https://registry.npm.wps.cn/@babel/plugin-proposal-numeric-separator/download/@babel/plugin-proposal-numeric-separator-7.18.6.tgz}
    engines: {node: '>=6.9.0'}
    deprecated: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-numeric-separator instead.
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-optional-chaining@7.21.0':
    resolution: {integrity: sha1-iG9ciXjet9MPZ4suJDRrKHI00+o=, tarball: https://registry.npm.wps.cn/@babel/plugin-proposal-optional-chaining/download/@babel/plugin-proposal-optional-chaining-7.21.0.tgz}
    engines: {node: '>=6.9.0'}
    deprecated: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-optional-chaining instead.
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-private-methods@7.18.6':
    resolution: {integrity: sha1-UgnefSE0V1SKmENvoogvUvS+a+o=, tarball: https://registry.npm.wps.cn/@babel/plugin-proposal-private-methods/download/@babel/plugin-proposal-private-methods-7.18.6.tgz}
    engines: {node: '>=6.9.0'}
    deprecated: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-methods instead.
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2':
    resolution: {integrity: sha1-eET5KJVG76n+usLeTP41igUL1wM=, tarball: https://registry.npm.wps.cn/@babel/plugin-proposal-private-property-in-object/download/@babel/plugin-proposal-private-property-in-object-7.21.0-placeholder-for-preset-env.2.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-private-property-in-object@7.21.11':
    resolution: {integrity: sha1-adWXCGtnYMQSZSXPoVTzRjH/Jyw=, tarball: https://registry.npm.wps.cn/@babel/plugin-proposal-private-property-in-object/download/@babel/plugin-proposal-private-property-in-object-7.21.11.tgz}
    engines: {node: '>=6.9.0'}
    deprecated: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-property-in-object instead.
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-async-generators@7.8.4':
    resolution: {integrity: sha1-qYP7Gusuw/btBCohD2QOkOeG/g0=, tarball: https://registry.npm.wps.cn/@babel/plugin-syntax-async-generators/download/@babel/plugin-syntax-async-generators-7.8.4.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-bigint@7.8.3':
    resolution: {integrity: sha1-TJpvZp9dDN8bkKFnHpoUa+UwDOo=, tarball: https://registry.npm.wps.cn/@babel/plugin-syntax-bigint/download/@babel/plugin-syntax-bigint-7.8.3.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-class-properties@7.12.13':
    resolution: {integrity: sha1-tcmHJ0xKOoK4lxR5aTGmtTVErhA=, tarball: https://registry.npm.wps.cn/@babel/plugin-syntax-class-properties/download/@babel/plugin-syntax-class-properties-7.12.13.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-class-static-block@7.14.5':
    resolution: {integrity: sha1-GV34mxRrS3izv4l/16JXyEZZ1AY=, tarball: https://registry.npm.wps.cn/@babel/plugin-syntax-class-static-block/download/@babel/plugin-syntax-class-static-block-7.14.5.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-decorators@7.27.1':
    resolution: {integrity: sha1-7n3ZWQruvAX51MjAVgAHsFl5pj0=, tarball: https://registry.npm.wps.cn/@babel/plugin-syntax-decorators/download/@babel/plugin-syntax-decorators-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-flow@7.27.1':
    resolution: {integrity: sha1-bIPPDX1jW3FoJyhLfs1a6tkjdmI=, tarball: https://registry.npm.wps.cn/@babel/plugin-syntax-flow/download/@babel/plugin-syntax-flow-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-import-assertions@7.27.1':
    resolution: {integrity: sha1-iIlK79KwO17mrRVip8jhWHSWrs0=, tarball: https://registry.npm.wps.cn/@babel/plugin-syntax-import-assertions/download/@babel/plugin-syntax-import-assertions-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-import-attributes@7.27.1':
    resolution: {integrity: sha1-NMAX1USW+bEbYUdOfqPf1VY//gc=, tarball: https://registry.npm.wps.cn/@babel/plugin-syntax-import-attributes/download/@babel/plugin-syntax-import-attributes-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-import-meta@7.10.4':
    resolution: {integrity: sha1-7mATSMNw+jNNIge+FYd3SWUh/VE=, tarball: https://registry.npm.wps.cn/@babel/plugin-syntax-import-meta/download/@babel/plugin-syntax-import-meta-7.10.4.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-json-strings@7.8.3':
    resolution: {integrity: sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo=, tarball: https://registry.npm.wps.cn/@babel/plugin-syntax-json-strings/download/@babel/plugin-syntax-json-strings-7.8.3.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-jsx@7.27.1':
    resolution: {integrity: sha1-L5vrXv8w+lB8VTLRB9qse4iPo0w=, tarball: https://registry.npm.wps.cn/@babel/plugin-syntax-jsx/download/@babel/plugin-syntax-jsx-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-logical-assignment-operators@7.10.4':
    resolution: {integrity: sha1-ypHvRjA1MESLkGZSusLp/plB9pk=, tarball: https://registry.npm.wps.cn/@babel/plugin-syntax-logical-assignment-operators/download/@babel/plugin-syntax-logical-assignment-operators-7.10.4.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3':
    resolution: {integrity: sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak=, tarball: https://registry.npm.wps.cn/@babel/plugin-syntax-nullish-coalescing-operator/download/@babel/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-numeric-separator@7.10.4':
    resolution: {integrity: sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c=, tarball: https://registry.npm.wps.cn/@babel/plugin-syntax-numeric-separator/download/@babel/plugin-syntax-numeric-separator-7.10.4.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-object-rest-spread@7.8.3':
    resolution: {integrity: sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE=, tarball: https://registry.npm.wps.cn/@babel/plugin-syntax-object-rest-spread/download/@babel/plugin-syntax-object-rest-spread-7.8.3.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-optional-catch-binding@7.8.3':
    resolution: {integrity: sha1-YRGiZbz7Ag6579D9/X0mQCue1sE=, tarball: https://registry.npm.wps.cn/@babel/plugin-syntax-optional-catch-binding/download/@babel/plugin-syntax-optional-catch-binding-7.8.3.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-optional-chaining@7.8.3':
    resolution: {integrity: sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io=, tarball: https://registry.npm.wps.cn/@babel/plugin-syntax-optional-chaining/download/@babel/plugin-syntax-optional-chaining-7.8.3.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-private-property-in-object@7.14.5':
    resolution: {integrity: sha1-DcZnHsDqIrbpShEU+FeXDNOd4a0=, tarball: https://registry.npm.wps.cn/@babel/plugin-syntax-private-property-in-object/download/@babel/plugin-syntax-private-property-in-object-7.14.5.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-top-level-await@7.14.5':
    resolution: {integrity: sha1-wc/a3DWmRiQAAfBhOCR7dBw02Uw=, tarball: https://registry.npm.wps.cn/@babel/plugin-syntax-top-level-await/download/@babel/plugin-syntax-top-level-await-7.14.5.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-typescript@7.27.1':
    resolution: {integrity: sha1-UUfSkGank0UPIgxj+jqUMbfm3Rg=, tarball: https://registry.npm.wps.cn/@babel/plugin-syntax-typescript/download/@babel/plugin-syntax-typescript-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-unicode-sets-regex@7.18.6':
    resolution: {integrity: sha1-1Jo7PmtS5b5nQAIjF1gCNKakc1c=, tarball: https://registry.npm.wps.cn/@babel/plugin-syntax-unicode-sets-regex/download/@babel/plugin-syntax-unicode-sets-regex-7.18.6.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-arrow-functions@7.27.1':
    resolution: {integrity: sha1-biBhBnujqwJm2DSp+UgRGW8qupo=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-arrow-functions/download/@babel/plugin-transform-arrow-functions-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-async-generator-functions@7.28.0':
    resolution: {integrity: sha1-Enbmxyhass0ezLC8c1a3pp/4QsI=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-async-generator-functions/download/@babel/plugin-transform-async-generator-functions-7.28.0.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-async-to-generator@7.27.1':
    resolution: {integrity: sha1-mpOJO5N5s5Rmx0R09VrwPeeMZuc=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-async-to-generator/download/@babel/plugin-transform-async-to-generator-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-block-scoped-functions@7.27.1':
    resolution: {integrity: sha1-VYqdbiTPcoAt07YqS1Hg1iwPV/k=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-block-scoped-functions/download/@babel/plugin-transform-block-scoped-functions-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-block-scoping@7.28.0':
    resolution: {integrity: sha1-58UMuswYA08hC5Pe+oljhmYJlFE=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-block-scoping/download/@babel/plugin-transform-block-scoping-7.28.0.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-class-properties@7.27.1':
    resolution: {integrity: sha1-3UCmo3Df1J0yNiriBt2vK7CCqSU=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-class-properties/download/@babel/plugin-transform-class-properties-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-class-static-block@7.27.1':
    resolution: {integrity: sha1-fpINViWyW7zNMGGu+8wFgF7VbOQ=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-class-static-block/download/@babel/plugin-transform-class-static-block-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.12.0

  '@babel/plugin-transform-classes@7.28.0':
    resolution: {integrity: sha1-EvpGz/wypuCEARtlBTnogK3YoPg=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-classes/download/@babel/plugin-transform-classes-7.28.0.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-computed-properties@7.27.1':
    resolution: {integrity: sha1-gWYueL9ec0qXmCwrfwp5MojvPKo=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-computed-properties/download/@babel/plugin-transform-computed-properties-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-destructuring@7.28.0':
    resolution: {integrity: sha1-DxVliPacWWCJt9Wwb1r4PZqn+Xo=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-destructuring/download/@babel/plugin-transform-destructuring-7.28.0.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-dotall-regex@7.27.1':
    resolution: {integrity: sha1-qmgh3oZMUosf7PKG8KF0446Cb00=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-dotall-regex/download/@babel/plugin-transform-dotall-regex-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-duplicate-keys@7.27.1':
    resolution: {integrity: sha1-8fv2KOzhjhLnsysXWUDmg1j1RtE=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-duplicate-keys/download/@babel/plugin-transform-duplicate-keys-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-duplicate-named-capturing-groups-regex@7.27.1':
    resolution: {integrity: sha1-UEOFTKYgqUFJNy5pAw/4y2qesOw=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-duplicate-named-capturing-groups-regex/download/@babel/plugin-transform-duplicate-named-capturing-groups-regex-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-dynamic-import@7.27.1':
    resolution: {integrity: sha1-THjzVVKsDgaqH248Vz1naV6K9aQ=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-dynamic-import/download/@babel/plugin-transform-dynamic-import-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-explicit-resource-management@7.28.0':
    resolution: {integrity: sha1-Rb5iEbd42/S51UxOiitC+nLgmho=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-explicit-resource-management/download/@babel/plugin-transform-explicit-resource-management-7.28.0.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-exponentiation-operator@7.27.1':
    resolution: {integrity: sha1-/El7EtgnflWXR/Wj7YaN2AZPg+E=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-exponentiation-operator/download/@babel/plugin-transform-exponentiation-operator-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-export-namespace-from@7.27.1':
    resolution: {integrity: sha1-ccpp00ce3W2qcRz038NABBXfnCM=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-export-namespace-from/download/@babel/plugin-transform-export-namespace-from-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-flow-strip-types@7.27.1':
    resolution: {integrity: sha1-Xe8+Hncw8AjWgxRPt5tyT5LFzfk=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-flow-strip-types/download/@babel/plugin-transform-flow-strip-types-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-for-of@7.27.1':
    resolution: {integrity: sha1-vCT3CA6f9yG2OnCseyVkyhW2xAo=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-for-of/download/@babel/plugin-transform-for-of-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-function-name@7.27.1':
    resolution: {integrity: sha1-TQvzB3IOTc5tfDD8sf1sp3ves6c=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-function-name/download/@babel/plugin-transform-function-name-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-json-strings@7.27.1':
    resolution: {integrity: sha1-ouDObvJWN2vVJ/KQ2gI5g1J6T0w=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-json-strings/download/@babel/plugin-transform-json-strings-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-literals@7.27.1':
    resolution: {integrity: sha1-uq76TRCh1CBvnc3aUNfVgnu3CyQ=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-literals/download/@babel/plugin-transform-literals-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-logical-assignment-operators@7.27.1':
    resolution: {integrity: sha1-iQyyDgJw4OW+vj8CW0NIQcMtW6o=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-logical-assignment-operators/download/@babel/plugin-transform-logical-assignment-operators-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-member-expression-literals@7.27.1':
    resolution: {integrity: sha1-N7iLpZTYUkGOmVNvVhL3lfI66vk=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-member-expression-literals/download/@babel/plugin-transform-member-expression-literals-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-amd@7.27.1':
    resolution: {integrity: sha1-pBRfnYfCKR/i0F+ZS2XbpOPnGW8=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-modules-amd/download/@babel/plugin-transform-modules-amd-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-commonjs@7.27.1':
    resolution: {integrity: sha1-jkTtN8J4fswjvcNn9Jl3R2YU6DI=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-modules-commonjs/download/@babel/plugin-transform-modules-commonjs-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-systemjs@7.27.1':
    resolution: {integrity: sha1-AOBbYYYwcNDzKSoAEmwWwOAkxO0=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-modules-systemjs/download/@babel/plugin-transform-modules-systemjs-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-umd@7.27.1':
    resolution: {integrity: sha1-Y/LPT23BXevBL2lORHFIY9NM0zQ=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-modules-umd/download/@babel/plugin-transform-modules-umd-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-named-capturing-groups-regex@7.27.1':
    resolution: {integrity: sha1-8yuPeBjY/AzEbuIKjvdfBxr5duE=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-named-capturing-groups-regex/download/@babel/plugin-transform-named-capturing-groups-regex-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-new-target@7.27.1':
    resolution: {integrity: sha1-JZxDk5coytFwasFzUbfmp76hq+s=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-new-target/download/@babel/plugin-transform-new-target-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-nullish-coalescing-operator@7.27.1':
    resolution: {integrity: sha1-T50xU79ngtc91CeFqdItAxl7yR0=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-nullish-coalescing-operator/download/@babel/plugin-transform-nullish-coalescing-operator-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-numeric-separator@7.27.1':
    resolution: {integrity: sha1-YU4LFcyADlmX2t2b1upSTtbIGcY=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-numeric-separator/download/@babel/plugin-transform-numeric-separator-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-object-rest-spread@7.28.0':
    resolution: {integrity: sha1-0jAhhX/9fNgJ9U1iQpm4CGQC7Y0=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-object-rest-spread/download/@babel/plugin-transform-object-rest-spread-7.28.0.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-object-super@7.27.1':
    resolution: {integrity: sha1-HJMs0nvzh0xDpcrE9D6/lwyYcbU=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-object-super/download/@babel/plugin-transform-object-super-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-optional-catch-binding@7.27.1':
    resolution: {integrity: sha1-hMc0Hr3jXM02sTfp5FhmglByoww=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-optional-catch-binding/download/@babel/plugin-transform-optional-catch-binding-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-optional-chaining@7.27.1':
    resolution: {integrity: sha1-h0zjxPBrd4BZLpRgJut2oygwRU8=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-optional-chaining/download/@babel/plugin-transform-optional-chaining-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-parameters@7.27.7':
    resolution: {integrity: sha1-H9L+u3x059Ic87BfeuvJB5QK9To=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-parameters/download/@babel/plugin-transform-parameters-7.27.7.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-private-methods@7.27.1':
    resolution: {integrity: sha1-/ay6scXtgexw39u4shPWXaFItq8=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-private-methods/download/@babel/plugin-transform-private-methods-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-private-property-in-object@7.27.1':
    resolution: {integrity: sha1-TbvvKDtbLwGiHoHimfduNfkA+xE=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-private-property-in-object/download/@babel/plugin-transform-private-property-in-object-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-property-literals@7.27.1':
    resolution: {integrity: sha1-B+r9YYgAWR6IBzoK8blA2aQsZCQ=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-property-literals/download/@babel/plugin-transform-property-literals-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-constant-elements@7.27.1':
    resolution: {integrity: sha1-bGtQQk50mm5Ir9FM97kvmMuTg/k=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-react-constant-elements/download/@babel/plugin-transform-react-constant-elements-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-display-name@7.28.0':
    resolution: {integrity: sha1-byCnKV/qffQutC/tj4loE/W5NN4=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-react-display-name/download/@babel/plugin-transform-react-display-name-7.28.0.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-jsx-development@7.27.1':
    resolution: {integrity: sha1-R/+VlA4go6cOaK09T8tle2R/bJg=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-react-jsx-development/download/@babel/plugin-transform-react-jsx-development-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-jsx@7.27.1':
    resolution: {integrity: sha1-ECO8lLeLCi1oyCtelq7Vc7z7nbA=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-react-jsx/download/@babel/plugin-transform-react-jsx-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-pure-annotations@7.27.1':
    resolution: {integrity: sha1-M58c41Xq4kLgZJ8jKxxokHwC6Hk=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-react-pure-annotations/download/@babel/plugin-transform-react-pure-annotations-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-regenerator@7.28.0':
    resolution: {integrity: sha1-8ZyjVY9xIZJPxLps0q/jpc2sibE=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-regenerator/download/@babel/plugin-transform-regenerator-7.28.0.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-regexp-modifiers@7.27.1':
    resolution: {integrity: sha1-35ulV3yXTj8USYiLcLdhaZmKbQk=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-regexp-modifiers/download/@babel/plugin-transform-regexp-modifiers-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-reserved-words@7.27.1':
    resolution: {integrity: sha1-QPukh4zL0cVmBaRHmjqJGsAnS7Q=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-reserved-words/download/@babel/plugin-transform-reserved-words-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-runtime@7.28.0':
    resolution: {integrity: sha1-Ri55AIzHvawD5MXhdlud4rzTHCE=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-runtime/download/@babel/plugin-transform-runtime-7.28.0.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-shorthand-properties@7.27.1':
    resolution: {integrity: sha1-Uyq9rN7Ie/7h4O+OL83uVD/jK5A=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-shorthand-properties/download/@babel/plugin-transform-shorthand-properties-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-spread@7.27.1':
    resolution: {integrity: sha1-GiZNX8EnUJGPUOP+PiTkNxeKuwg=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-spread/download/@babel/plugin-transform-spread-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-sticky-regex@7.27.1':
    resolution: {integrity: sha1-GJhJNdnSKWhDpJHXigFJOffc0oA=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-sticky-regex/download/@babel/plugin-transform-sticky-regex-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-template-literals@7.27.1':
    resolution: {integrity: sha1-Gg6zXYuz5u/AbJ/UDrC871SDKLg=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-template-literals/download/@babel/plugin-transform-template-literals-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-typeof-symbol@7.27.1':
    resolution: {integrity: sha1-cOlmu0kuA1Cc836vptzDBR+EQ2k=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-typeof-symbol/download/@babel/plugin-transform-typeof-symbol-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-typescript@7.28.0':
    resolution: {integrity: sha1-eWy9JJq1bBgWi0nj4dNBtyrwSms=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-typescript/download/@babel/plugin-transform-typescript-7.28.0.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-escapes@7.27.1':
    resolution: {integrity: sha1-PjFD+EOK74Qt4ogW7OWHgBkM+AY=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-unicode-escapes/download/@babel/plugin-transform-unicode-escapes-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-property-regex@7.27.1':
    resolution: {integrity: sha1-vf4tMXDHjFaRo8O+k0yMAIdSWVY=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-unicode-property-regex/download/@babel/plugin-transform-unicode-property-regex-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-regex@7.27.1':
    resolution: {integrity: sha1-JZSPXDldsV9gkCjjcGZ+2Lrpr5c=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-unicode-regex/download/@babel/plugin-transform-unicode-regex-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-sets-regex@7.27.1':
    resolution: {integrity: sha1-arcG0Q+AG1xy2ouyVIVh+gQZPNE=, tarball: https://registry.npm.wps.cn/@babel/plugin-transform-unicode-sets-regex/download/@babel/plugin-transform-unicode-sets-regex-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/preset-env@7.28.0':
    resolution: {integrity: sha1-0jprwXtDIn0R23cIGgd5xwa1Vpw=, tarball: https://registry.npm.wps.cn/@babel/preset-env/download/@babel/preset-env-7.28.0.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/preset-modules@0.1.6-no-external-plugins':
    resolution: {integrity: sha1-zLiKLEnIFyNoYf7ngmCAVzuKkjo=, tarball: https://registry.npm.wps.cn/@babel/preset-modules/download/@babel/preset-modules-0.1.6-no-external-plugins.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0 || ^8.0.0-0 <8.0.0

  '@babel/preset-react@7.27.1':
    resolution: {integrity: sha1-huoKXKOYRmP3RL4v0my2dHw/0Ow=, tarball: https://registry.npm.wps.cn/@babel/preset-react/download/@babel/preset-react-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/preset-typescript@7.27.1':
    resolution: {integrity: sha1-GQdCpkKNKCMGZIpVsFKbVhSE+RI=, tarball: https://registry.npm.wps.cn/@babel/preset-typescript/download/@babel/preset-typescript-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/runtime@7.27.6':
    resolution: {integrity: sha1-7EBwoE12uujduxB3C6VXFKQXt8Y=, tarball: https://registry.npm.wps.cn/@babel/runtime/download/@babel/runtime-7.27.6.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/template@7.27.2':
    resolution: {integrity: sha1-+njO7TxOe2Pr9ss55YUvykX2gJ0=, tarball: https://registry.npm.wps.cn/@babel/template/download/@babel/template-7.27.2.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/traverse@7.28.0':
    resolution: {integrity: sha1-UYqhEzWbBiBCN54zPbGDgLU340s=, tarball: https://registry.npm.wps.cn/@babel/traverse/download/@babel/traverse-7.28.0.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/types@7.28.0':
    resolution: {integrity: sha1-L9AVmm3HNTkzkgxDE2M1qbJk2VA=, tarball: https://registry.npm.wps.cn/@babel/types/download/@babel/types-7.28.0.tgz}
    engines: {node: '>=6.9.0'}

  '@bcoe/v8-coverage@0.2.3':
    resolution: {integrity: sha1-daLotRy3WKdVPWgEpZMteqznXDk=, tarball: https://registry.npm.wps.cn/@bcoe/v8-coverage/download/@bcoe/v8-coverage-0.2.3.tgz}

  '@craco/craco@7.1.0':
    resolution: {integrity: sha1-Er05TH8DNOIUMC5NNaF2j2gEL7s=, tarball: https://registry.npm.wps.cn/@craco/craco/download/@craco/craco-7.1.0.tgz}
    engines: {node: '>=6'}
    hasBin: true
    peerDependencies:
      react-scripts: ^5.0.0

  '@craco/types@7.1.0':
    resolution: {integrity: sha1-m8moP60qQq5TvyH/Xlh4JPEPoxo=, tarball: https://registry.npm.wps.cn/@craco/types/download/@craco/types-7.1.0.tgz}

  '@cspotcode/source-map-support@0.8.1':
    resolution: {integrity: sha1-AGKcNaaI4FqIsc2mhPudXnPwAKE=, tarball: https://registry.npm.wps.cn/@cspotcode/source-map-support/download/@cspotcode/source-map-support-0.8.1.tgz}
    engines: {node: '>=12'}

  '@csstools/normalize.css@12.1.1':
    resolution: {integrity: sha1-8K0iG3KA8/yBRol4b9nuCSd2748=, tarball: https://registry.npm.wps.cn/@csstools/normalize.css/download/@csstools/normalize.css-12.1.1.tgz}

  '@csstools/postcss-cascade-layers@1.1.1':
    resolution: {integrity: sha1-ipl+35fTQHHdLjfqYCJEfdnnla0=, tarball: https://registry.npm.wps.cn/@csstools/postcss-cascade-layers/download/@csstools/postcss-cascade-layers-1.1.1.tgz}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/postcss-color-function@1.1.1':
    resolution: {integrity: sha1-K9Nqs0+C0El8+s3JsY00teb2S2s=, tarball: https://registry.npm.wps.cn/@csstools/postcss-color-function/download/@csstools/postcss-color-function-1.1.1.tgz}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/postcss-font-format-keywords@1.0.1':
    resolution: {integrity: sha1-Z3s06eiK6ZemcoMxFleXMVDosWo=, tarball: https://registry.npm.wps.cn/@csstools/postcss-font-format-keywords/download/@csstools/postcss-font-format-keywords-1.0.1.tgz}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/postcss-hwb-function@1.0.2':
    resolution: {integrity: sha1-q1Sp/OCsECx1SFR2mWLyQiroqos=, tarball: https://registry.npm.wps.cn/@csstools/postcss-hwb-function/download/@csstools/postcss-hwb-function-1.0.2.tgz}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/postcss-ic-unit@1.0.1':
    resolution: {integrity: sha1-KCN9gSoSTRoWpazFw4MrBAswPlg=, tarball: https://registry.npm.wps.cn/@csstools/postcss-ic-unit/download/@csstools/postcss-ic-unit-1.0.1.tgz}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/postcss-is-pseudo-class@2.0.7':
    resolution: {integrity: sha1-hGrmwNWh6qh4/ONSxUT5wpVQnNE=, tarball: https://registry.npm.wps.cn/@csstools/postcss-is-pseudo-class/download/@csstools/postcss-is-pseudo-class-2.0.7.tgz}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/postcss-nested-calc@1.0.0':
    resolution: {integrity: sha1-1+nR0NPRXPWsiRsWAoryoQRNDCY=, tarball: https://registry.npm.wps.cn/@csstools/postcss-nested-calc/download/@csstools/postcss-nested-calc-1.0.0.tgz}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/postcss-normalize-display-values@1.0.1':
    resolution: {integrity: sha1-FdpUo26GezrFFj7hLB1/gtTWEsM=, tarball: https://registry.npm.wps.cn/@csstools/postcss-normalize-display-values/download/@csstools/postcss-normalize-display-values-1.0.1.tgz}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/postcss-oklab-function@1.1.1':
    resolution: {integrity: sha1-iM7g+8jW3ycHnr0voBbuJh7s+EQ=, tarball: https://registry.npm.wps.cn/@csstools/postcss-oklab-function/download/@csstools/postcss-oklab-function-1.1.1.tgz}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/postcss-progressive-custom-properties@1.3.0':
    resolution: {integrity: sha1-VCKSVYOENhd2tFyFImuaOjTydvo=, tarball: https://registry.npm.wps.cn/@csstools/postcss-progressive-custom-properties/download/@csstools/postcss-progressive-custom-properties-1.3.0.tgz}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.3

  '@csstools/postcss-stepped-value-functions@1.0.1':
    resolution: {integrity: sha1-+HcsNoHMK+/taV4rCx1o4i8IxPQ=, tarball: https://registry.npm.wps.cn/@csstools/postcss-stepped-value-functions/download/@csstools/postcss-stepped-value-functions-1.0.1.tgz}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/postcss-text-decoration-shorthand@1.0.0':
    resolution: {integrity: sha1-6pbPvIfZIeypFNOtKTQNm8xMlT8=, tarball: https://registry.npm.wps.cn/@csstools/postcss-text-decoration-shorthand/download/@csstools/postcss-text-decoration-shorthand-1.0.0.tgz}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/postcss-trigonometric-functions@1.0.2':
    resolution: {integrity: sha1-lNPkd0w2013NyIzgkTNst3DTJ1Y=, tarball: https://registry.npm.wps.cn/@csstools/postcss-trigonometric-functions/download/@csstools/postcss-trigonometric-functions-1.0.2.tgz}
    engines: {node: ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/postcss-unset-value@1.0.2':
    resolution: {integrity: sha1-yZu3DizccxKUjR60HfJBIzC4H3c=, tarball: https://registry.npm.wps.cn/@csstools/postcss-unset-value/download/@csstools/postcss-unset-value-1.0.2.tgz}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/selector-specificity@2.2.0':
    resolution: {integrity: sha1-LLz4Ir83ZMlljE0uVovQwMt0gBY=, tarball: https://registry.npm.wps.cn/@csstools/selector-specificity/download/@csstools/selector-specificity-2.2.0.tgz}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss-selector-parser: ^6.0.10

  '@eslint-community/eslint-utils@4.7.0':
    resolution: {integrity: sha1-YHCEYwxsAzmSoILebm+8GotSF1o=, tarball: https://registry.npm.wps.cn/@eslint-community/eslint-utils/download/@eslint-community/eslint-utils-4.7.0.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0

  '@eslint-community/regexpp@4.12.1':
    resolution: {integrity: sha1-z8bP/jnfOQo4Qc3iq8z5Lqp64OA=, tarball: https://registry.npm.wps.cn/@eslint-community/regexpp/download/@eslint-community/regexpp-4.12.1.tgz}
    engines: {node: ^12.0.0 || ^14.0.0 || >=16.0.0}

  '@eslint/eslintrc@2.1.4':
    resolution: {integrity: sha1-OIomnw8lwbatwxe1osVXFIlMcK0=, tarball: https://registry.npm.wps.cn/@eslint/eslintrc/download/@eslint/eslintrc-2.1.4.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@eslint/js@8.57.1':
    resolution: {integrity: sha1-3mM9s+wu9qPIni8ZA4Bj6KEi4sI=, tarball: https://registry.npm.wps.cn/@eslint/js/download/@eslint/js-8.57.1.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@humanwhocodes/config-array@0.13.0':
    resolution: {integrity: sha1-+5B2JN8yVtBLmqLfUNeql+xkh0g=, tarball: https://registry.npm.wps.cn/@humanwhocodes/config-array/download/@humanwhocodes/config-array-0.13.0.tgz}
    engines: {node: '>=10.10.0'}
    deprecated: Use @eslint/config-array instead

  '@humanwhocodes/module-importer@1.0.1':
    resolution: {integrity: sha1-r1smkaIrRL6EewyoFkHF+2rQFyw=, tarball: https://registry.npm.wps.cn/@humanwhocodes/module-importer/download/@humanwhocodes/module-importer-1.0.1.tgz}
    engines: {node: '>=12.22'}

  '@humanwhocodes/object-schema@2.0.3':
    resolution: {integrity: sha1-Siho111taWPkI7z5C3/RvjQ0CdM=, tarball: https://registry.npm.wps.cn/@humanwhocodes/object-schema/download/@humanwhocodes/object-schema-2.0.3.tgz}
    deprecated: Use @eslint/object-schema instead

  '@isaacs/cliui@8.0.2':
    resolution: {integrity: sha1-s3Znt7wYHBaHgiWbq0JHT79StVA=, tarball: https://registry.npm.wps.cn/@isaacs/cliui/download/@isaacs/cliui-8.0.2.tgz}
    engines: {node: '>=12'}

  '@istanbuljs/load-nyc-config@1.1.0':
    resolution: {integrity: sha1-/T2x1Z7PfPEh6AZQu4ZxL5tV7O0=, tarball: https://registry.npm.wps.cn/@istanbuljs/load-nyc-config/download/@istanbuljs/load-nyc-config-1.1.0.tgz}
    engines: {node: '>=8'}

  '@istanbuljs/schema@0.1.3':
    resolution: {integrity: sha1-5F44TkuOwWvOL9kDr3hFD2v37Jg=, tarball: https://registry.npm.wps.cn/@istanbuljs/schema/download/@istanbuljs/schema-0.1.3.tgz}
    engines: {node: '>=8'}

  '@jest/console@27.5.1':
    resolution: {integrity: sha1-Jg/nI5YC/lEwqU8ao4bv9UsBS7o=, tarball: https://registry.npm.wps.cn/@jest/console/download/@jest/console-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  '@jest/console@28.1.3':
    resolution: {integrity: sha1-IDBgbsA6GMMYA7ijY4J2LkR2Vd8=, tarball: https://registry.npm.wps.cn/@jest/console/download/@jest/console-28.1.3.tgz}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}

  '@jest/core@27.5.1':
    resolution: {integrity: sha1-JnrF9wTgncUt4pIsvzr57c1ktiY=, tarball: https://registry.npm.wps.cn/@jest/core/download/@jest/core-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true

  '@jest/environment@27.5.1':
    resolution: {integrity: sha1-10JYIFEf5xWKu+zAEBQMP9O+nHQ=, tarball: https://registry.npm.wps.cn/@jest/environment/download/@jest/environment-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  '@jest/fake-timers@27.5.1':
    resolution: {integrity: sha1-dpeXRc4FecipSkZ4r3p0jtqK2nQ=, tarball: https://registry.npm.wps.cn/@jest/fake-timers/download/@jest/fake-timers-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  '@jest/globals@27.5.1':
    resolution: {integrity: sha1-esBs5Xq5ZlZseWNDHO9FhDRgGys=, tarball: https://registry.npm.wps.cn/@jest/globals/download/@jest/globals-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  '@jest/reporters@27.5.1':
    resolution: {integrity: sha1-ztp76WFwsDySPDeYe2QBWBL/7AQ=, tarball: https://registry.npm.wps.cn/@jest/reporters/download/@jest/reporters-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true

  '@jest/schemas@28.1.3':
    resolution: {integrity: sha1-rYuGpm8R8zYZ49fh3N3X8tQP+QU=, tarball: https://registry.npm.wps.cn/@jest/schemas/download/@jest/schemas-28.1.3.tgz}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}

  '@jest/source-map@27.5.1':
    resolution: {integrity: sha1-Zgg5HkZa3UIF6uBztV5/J54E6M8=, tarball: https://registry.npm.wps.cn/@jest/source-map/download/@jest/source-map-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  '@jest/test-result@27.5.1':
    resolution: {integrity: sha1-VqZYX6gPfNq3K4xfwuhx0Dgy9bs=, tarball: https://registry.npm.wps.cn/@jest/test-result/download/@jest/test-result-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  '@jest/test-result@28.1.3':
    resolution: {integrity: sha1-Xq6UX9n0uPz8500jnm9yW2vwdsU=, tarball: https://registry.npm.wps.cn/@jest/test-result/download/@jest/test-result-28.1.3.tgz}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}

  '@jest/test-sequencer@27.5.1':
    resolution: {integrity: sha1-QFfg6c6kQ55UTGNTxq/+WNCVdFs=, tarball: https://registry.npm.wps.cn/@jest/test-sequencer/download/@jest/test-sequencer-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  '@jest/transform@27.5.1':
    resolution: {integrity: sha1-bDUB3MAMTAiRXykqYA7OXs/h9Ak=, tarball: https://registry.npm.wps.cn/@jest/transform/download/@jest/transform-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  '@jest/types@27.5.1':
    resolution: {integrity: sha1-PHnsSoumHBcL+Te8+emKnfF17IA=, tarball: https://registry.npm.wps.cn/@jest/types/download/@jest/types-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  '@jest/types@28.1.3':
    resolution: {integrity: sha1-sF3oCZb/ElErxc6x0ggoWn0RdIs=, tarball: https://registry.npm.wps.cn/@jest/types/download/@jest/types-28.1.3.tgz}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}

  '@jridgewell/gen-mapping@0.3.12':
    resolution: {integrity: sha1-IjTOJsYoifA9s9f+pDwZMqs+kns=, tarball: https://registry.npm.wps.cn/@jridgewell/gen-mapping/download/@jridgewell/gen-mapping-0.3.12.tgz}

  '@jridgewell/resolve-uri@3.1.2':
    resolution: {integrity: sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y=, tarball: https://registry.npm.wps.cn/@jridgewell/resolve-uri/download/@jridgewell/resolve-uri-3.1.2.tgz}
    engines: {node: '>=6.0.0'}

  '@jridgewell/source-map@0.3.6':
    resolution: {integrity: sha1-nXHKiG4yUC65NiyadKRnh8Nt+Bo=, tarball: https://registry.npm.wps.cn/@jridgewell/source-map/download/@jridgewell/source-map-0.3.6.tgz}

  '@jridgewell/sourcemap-codec@1.5.4':
    resolution: {integrity: sha1-c1gENDOy5dpWmqAsvEwSHaOvJ9c=, tarball: https://registry.npm.wps.cn/@jridgewell/sourcemap-codec/download/@jridgewell/sourcemap-codec-1.5.4.tgz}

  '@jridgewell/trace-mapping@0.3.29':
    resolution: {integrity: sha1-pY0x6q2vksZpVoCy4dRkqbj79/w=, tarball: https://registry.npm.wps.cn/@jridgewell/trace-mapping/download/@jridgewell/trace-mapping-0.3.29.tgz}

  '@jridgewell/trace-mapping@0.3.9':
    resolution: {integrity: sha1-ZTT9WTOlO6fL86F2FeJzoNEnP/k=, tarball: https://registry.npm.wps.cn/@jridgewell/trace-mapping/download/@jridgewell/trace-mapping-0.3.9.tgz}

  '@leichtgewicht/ip-codec@2.0.5':
    resolution: {integrity: sha1-T8VsFcWAua233DwzOhNOVAtEv7E=, tarball: https://registry.npm.wps.cn/@leichtgewicht/ip-codec/download/@leichtgewicht/ip-codec-2.0.5.tgz}

  '@nicolo-ribaudo/eslint-scope-5-internals@5.1.1-v1':
    resolution: {integrity: sha1-2/czqWXKR7GXMXfcC7bIie3PsSk=, tarball: https://registry.npm.wps.cn/@nicolo-ribaudo/eslint-scope-5-internals/download/@nicolo-ribaudo/eslint-scope-5-internals-5.1.1-v1.tgz}

  '@nodelib/fs.scandir@2.1.5':
    resolution: {integrity: sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=, tarball: https://registry.npm.wps.cn/@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.5.tgz}
    engines: {node: '>= 8'}

  '@nodelib/fs.stat@2.0.5':
    resolution: {integrity: sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=, tarball: https://registry.npm.wps.cn/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.5.tgz}
    engines: {node: '>= 8'}

  '@nodelib/fs.walk@1.2.8':
    resolution: {integrity: sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=, tarball: https://registry.npm.wps.cn/@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.8.tgz}
    engines: {node: '>= 8'}

  '@pkgjs/parseargs@0.11.0':
    resolution: {integrity: sha1-p36nQvqyV3UUVDTrHSMoz1ATrDM=, tarball: https://registry.npm.wps.cn/@pkgjs/parseargs/download/@pkgjs/parseargs-0.11.0.tgz}
    engines: {node: '>=14'}

  '@pmmmwh/react-refresh-webpack-plugin@0.5.17':
    resolution: {integrity: sha1-jC80yoZR33SJVCIEbhHOWhIOeTA=, tarball: https://registry.npm.wps.cn/@pmmmwh/react-refresh-webpack-plugin/download/@pmmmwh/react-refresh-webpack-plugin-0.5.17.tgz}
    engines: {node: '>= 10.13'}
    peerDependencies:
      '@types/webpack': 4.x || 5.x
      react-refresh: '>=0.10.0 <1.0.0'
      sockjs-client: ^1.4.0
      type-fest: '>=0.17.0 <5.0.0'
      webpack: '>=4.43.0 <6.0.0'
      webpack-dev-server: 3.x || 4.x || 5.x
      webpack-hot-middleware: 2.x
      webpack-plugin-serve: 0.x || 1.x
    peerDependenciesMeta:
      '@types/webpack':
        optional: true
      sockjs-client:
        optional: true
      type-fest:
        optional: true
      webpack-dev-server:
        optional: true
      webpack-hot-middleware:
        optional: true
      webpack-plugin-serve:
        optional: true

  '@remix-run/router@1.23.0':
    resolution: {integrity: sha1-NTkNDnd5YmwCaxE3baZ4nrg4kkI=, tarball: https://registry.npm.wps.cn/@remix-run/router/download/@remix-run/router-1.23.0.tgz}
    engines: {node: '>=14.0.0'}

  '@rollup/plugin-babel@5.3.1':
    resolution: {integrity: sha1-BLwGCPSqSy5LGuvyhDRND2j9ooM=, tarball: https://registry.npm.wps.cn/@rollup/plugin-babel/download/@rollup/plugin-babel-5.3.1.tgz}
    engines: {node: '>= 10.0.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
      '@types/babel__core': ^7.1.9
      rollup: ^1.20.0||^2.0.0
    peerDependenciesMeta:
      '@types/babel__core':
        optional: true

  '@rollup/plugin-node-resolve@11.2.1':
    resolution: {integrity: sha1-gqpZOXopzU4TJIsQbmpKGIA2KmA=, tarball: https://registry.npm.wps.cn/@rollup/plugin-node-resolve/download/@rollup/plugin-node-resolve-11.2.1.tgz}
    engines: {node: '>= 10.0.0'}
    peerDependencies:
      rollup: ^1.20.0||^2.0.0

  '@rollup/plugin-replace@2.4.2':
    resolution: {integrity: sha1-otU5MU+8d8JEhY+qUjASglBoUQo=, tarball: https://registry.npm.wps.cn/@rollup/plugin-replace/download/@rollup/plugin-replace-2.4.2.tgz}
    peerDependencies:
      rollup: ^1.20.0 || ^2.0.0

  '@rollup/pluginutils@3.1.0':
    resolution: {integrity: sha1-cGtFJO5tyLEDs8mVUz5a1oDAK5s=, tarball: https://registry.npm.wps.cn/@rollup/pluginutils/download/@rollup/pluginutils-3.1.0.tgz}
    engines: {node: '>= 8.0.0'}
    peerDependencies:
      rollup: ^1.20.0||^2.0.0

  '@rtsao/scc@1.1.0':
    resolution: {integrity: sha1-kn3S+um8M2FAOsLHoAwy3c6a1+g=, tarball: https://registry.npm.wps.cn/@rtsao/scc/download/@rtsao/scc-1.1.0.tgz}

  '@rushstack/eslint-patch@1.12.0':
    resolution: {integrity: sha1-Mmp7RvbUz6VK4lu4iFUWl4cwabQ=, tarball: https://registry.npm.wps.cn/@rushstack/eslint-patch/download/@rushstack/eslint-patch-1.12.0.tgz}

  '@sinclair/typebox@0.24.51':
    resolution: {integrity: sha1-ZF8z/k4C3v4m8vXAQQ4cCU6sf18=, tarball: https://registry.npm.wps.cn/@sinclair/typebox/download/@sinclair/typebox-0.24.51.tgz}

  '@sinonjs/commons@1.8.6':
    resolution: {integrity: sha1-gMUWpNwmTCppEV51eNYlgf9FXtk=, tarball: https://registry.npm.wps.cn/@sinonjs/commons/download/@sinonjs/commons-1.8.6.tgz}

  '@sinonjs/fake-timers@8.1.0':
    resolution: {integrity: sha1-P9wrbLWJNbIb+40WJesTAEhDFuc=, tarball: https://registry.npm.wps.cn/@sinonjs/fake-timers/download/@sinonjs/fake-timers-8.1.0.tgz}

  '@surma/rollup-plugin-off-main-thread@2.2.3':
    resolution: {integrity: sha1-7jSYWVLKIVWKsNlS8AKYrSGQwFM=, tarball: https://registry.npm.wps.cn/@surma/rollup-plugin-off-main-thread/download/@surma/rollup-plugin-off-main-thread-2.2.3.tgz}

  '@svgr/babel-plugin-add-jsx-attribute@5.4.0':
    resolution: {integrity: sha1-ge9hlHuyaOudUFI0RvnGOPs1WQY=, tarball: https://registry.npm.wps.cn/@svgr/babel-plugin-add-jsx-attribute/download/@svgr/babel-plugin-add-jsx-attribute-5.4.0.tgz}
    engines: {node: '>=10'}

  '@svgr/babel-plugin-remove-jsx-attribute@5.4.0':
    resolution: {integrity: sha1-ayx3DJXIdGVP1eHV70dbeKCpYu8=, tarball: https://registry.npm.wps.cn/@svgr/babel-plugin-remove-jsx-attribute/download/@svgr/babel-plugin-remove-jsx-attribute-5.4.0.tgz}
    engines: {node: '>=10'}

  '@svgr/babel-plugin-remove-jsx-empty-expression@5.0.1':
    resolution: {integrity: sha1-JWIaiRXtetcNps6j0KbbwuqTPv0=, tarball: https://registry.npm.wps.cn/@svgr/babel-plugin-remove-jsx-empty-expression/download/@svgr/babel-plugin-remove-jsx-empty-expression-5.0.1.tgz}
    engines: {node: '>=10'}

  '@svgr/babel-plugin-replace-jsx-attribute-value@5.0.1':
    resolution: {integrity: sha1-CyIfxX+fzRDpH+IZ4s0N0DFFqJc=, tarball: https://registry.npm.wps.cn/@svgr/babel-plugin-replace-jsx-attribute-value/download/@svgr/babel-plugin-replace-jsx-attribute-value-5.0.1.tgz}
    engines: {node: '>=10'}

  '@svgr/babel-plugin-svg-dynamic-title@5.4.0':
    resolution: {integrity: sha1-E5tUbdDDGGtuXbT+/CbLC66nKdc=, tarball: https://registry.npm.wps.cn/@svgr/babel-plugin-svg-dynamic-title/download/@svgr/babel-plugin-svg-dynamic-title-5.4.0.tgz}
    engines: {node: '>=10'}

  '@svgr/babel-plugin-svg-em-dimensions@5.4.0':
    resolution: {integrity: sha1-ZUP2lSZjKhM85cq6uWXe6uoiNKA=, tarball: https://registry.npm.wps.cn/@svgr/babel-plugin-svg-em-dimensions/download/@svgr/babel-plugin-svg-em-dimensions-5.4.0.tgz}
    engines: {node: '>=10'}

  '@svgr/babel-plugin-transform-react-native-svg@5.4.0':
    resolution: {integrity: sha1-AL+aenPxytOUjNqx+N+3dHUPjIA=, tarball: https://registry.npm.wps.cn/@svgr/babel-plugin-transform-react-native-svg/download/@svgr/babel-plugin-transform-react-native-svg-5.4.0.tgz}
    engines: {node: '>=10'}

  '@svgr/babel-plugin-transform-svg-component@5.5.0':
    resolution: {integrity: sha1-WDpeKhk+IU2i86/rC56NMlASa0o=, tarball: https://registry.npm.wps.cn/@svgr/babel-plugin-transform-svg-component/download/@svgr/babel-plugin-transform-svg-component-5.5.0.tgz}
    engines: {node: '>=10'}

  '@svgr/babel-preset@5.5.0':
    resolution: {integrity: sha1-ivVPPgqK3XseKw/NWogsVTk98yc=, tarball: https://registry.npm.wps.cn/@svgr/babel-preset/download/@svgr/babel-preset-5.5.0.tgz}
    engines: {node: '>=10'}

  '@svgr/core@5.5.0':
    resolution: {integrity: sha1-gugmuHFdcQgxIP6PJJLsfXh0pXk=, tarball: https://registry.npm.wps.cn/@svgr/core/download/@svgr/core-5.5.0.tgz}
    engines: {node: '>=10'}

  '@svgr/hast-util-to-babel-ast@5.5.0':
    resolution: {integrity: sha1-XuUqnCUz9z5j+PIrd5+TzUMqVGE=, tarball: https://registry.npm.wps.cn/@svgr/hast-util-to-babel-ast/download/@svgr/hast-util-to-babel-ast-5.5.0.tgz}
    engines: {node: '>=10'}

  '@svgr/plugin-jsx@5.5.0':
    resolution: {integrity: sha1-GqjNeYodtxc6wENGbXtSI2s2kAA=, tarball: https://registry.npm.wps.cn/@svgr/plugin-jsx/download/@svgr/plugin-jsx-5.5.0.tgz}
    engines: {node: '>=10'}

  '@svgr/plugin-svgo@5.5.0':
    resolution: {integrity: sha1-AtpV2FMgVJMk4gHHsuU79DH8wkY=, tarball: https://registry.npm.wps.cn/@svgr/plugin-svgo/download/@svgr/plugin-svgo-5.5.0.tgz}
    engines: {node: '>=10'}

  '@svgr/webpack@5.5.0':
    resolution: {integrity: sha1-quhY7lefX6jObDFm71bGobOBtkA=, tarball: https://registry.npm.wps.cn/@svgr/webpack/download/@svgr/webpack-5.5.0.tgz}
    engines: {node: '>=10'}

  '@testing-library/dom@8.20.1':
    resolution: {integrity: sha1-LlKjLkb8iDae737vY0rCoZLezZ8=, tarball: https://registry.npm.wps.cn/@testing-library/dom/download/@testing-library/dom-8.20.1.tgz}
    engines: {node: '>=12'}

  '@testing-library/jest-dom@5.17.0':
    resolution: {integrity: sha1-XpfI+aFcz0ZW2gD+yrUFco3oHgw=, tarball: https://registry.npm.wps.cn/@testing-library/jest-dom/download/@testing-library/jest-dom-5.17.0.tgz}
    engines: {node: '>=8', npm: '>=6', yarn: '>=1'}

  '@testing-library/react@13.4.0':
    resolution: {integrity: sha1-ajHjv1lRYVWTrZhOlrnl4tk4CWY=, tarball: https://registry.npm.wps.cn/@testing-library/react/download/@testing-library/react-13.4.0.tgz}
    engines: {node: '>=12'}
    peerDependencies:
      react: ^18.0.0
      react-dom: ^18.0.0

  '@testing-library/user-event@13.5.0':
    resolution: {integrity: sha1-addwB/HhJNVTFKK3P9IEszOxMpU=, tarball: https://registry.npm.wps.cn/@testing-library/user-event/download/@testing-library/user-event-13.5.0.tgz}
    engines: {node: '>=10', npm: '>=6'}
    peerDependencies:
      '@testing-library/dom': '>=7.21.4'

  '@tootallnate/once@1.1.2':
    resolution: {integrity: sha1-zLkURTYBeaBOf+av94wA/8Hur4I=, tarball: https://registry.npm.wps.cn/@tootallnate/once/download/@tootallnate/once-1.1.2.tgz}
    engines: {node: '>= 6'}

  '@trysound/sax@0.2.0':
    resolution: {integrity: sha1-zMqrdYr1Z2Hre/N69vA/Mm3XmK0=, tarball: https://registry.npm.wps.cn/@trysound/sax/download/@trysound/sax-0.2.0.tgz}
    engines: {node: '>=10.13.0'}

  '@tsconfig/node10@1.0.11':
    resolution: {integrity: sha1-buRkAGhfEw4ngSjHs4t+Ax/1svI=, tarball: https://registry.npm.wps.cn/@tsconfig/node10/download/@tsconfig/node10-1.0.11.tgz}

  '@tsconfig/node12@1.0.11':
    resolution: {integrity: sha1-7j3vHyfZ7WbaxuRqKVz/sBUuBY0=, tarball: https://registry.npm.wps.cn/@tsconfig/node12/download/@tsconfig/node12-1.0.11.tgz}

  '@tsconfig/node14@1.0.3':
    resolution: {integrity: sha1-5DhjFihPALmENb9A9y91oJ2r9sE=, tarball: https://registry.npm.wps.cn/@tsconfig/node14/download/@tsconfig/node14-1.0.3.tgz}

  '@tsconfig/node16@1.0.4':
    resolution: {integrity: sha1-C5LcwMwcgfbzBqOB8o4xsaVlNuk=, tarball: https://registry.npm.wps.cn/@tsconfig/node16/download/@tsconfig/node16-1.0.4.tgz}

  '@types/aria-query@5.0.4':
    resolution: {integrity: sha1-GjHD03iFDSd42rtjdNA23LpLpwg=, tarball: https://registry.npm.wps.cn/@types/aria-query/download/@types/aria-query-5.0.4.tgz}

  '@types/babel__core@7.20.5':
    resolution: {integrity: sha1-PfFfJ7qFMZyqB7oI0HIYibs5wBc=, tarball: https://registry.npm.wps.cn/@types/babel__core/download/@types/babel__core-7.20.5.tgz}

  '@types/babel__generator@7.27.0':
    resolution: {integrity: sha1-tYGSlMUReZV6+uw0FEL5NB5BCKk=, tarball: https://registry.npm.wps.cn/@types/babel__generator/download/@types/babel__generator-7.27.0.tgz}

  '@types/babel__template@7.4.4':
    resolution: {integrity: sha1-VnJRNwHBshmbxtrWNqnXSRWGdm8=, tarball: https://registry.npm.wps.cn/@types/babel__template/download/@types/babel__template-7.4.4.tgz}

  '@types/babel__traverse@7.20.7':
    resolution: {integrity: sha1-lozcI2bsPaFZ9hFmQo7kDzcOVsI=, tarball: https://registry.npm.wps.cn/@types/babel__traverse/download/@types/babel__traverse-7.20.7.tgz}

  '@types/body-parser@1.19.6':
    resolution: {integrity: sha1-GFm+u4/X2smRikXVTBlxq4ta9HQ=, tarball: https://registry.npm.wps.cn/@types/body-parser/download/@types/body-parser-1.19.6.tgz}

  '@types/bonjour@3.5.13':
    resolution: {integrity: sha1-rfkM4aEF6B3R+cYf3Fr9ob+5KVY=, tarball: https://registry.npm.wps.cn/@types/bonjour/download/@types/bonjour-3.5.13.tgz}

  '@types/connect-history-api-fallback@1.5.4':
    resolution: {integrity: sha1-fecWRaEDBWtIrDzgezUguBnB1bM=, tarball: https://registry.npm.wps.cn/@types/connect-history-api-fallback/download/@types/connect-history-api-fallback-1.5.4.tgz}

  '@types/connect@3.4.38':
    resolution: {integrity: sha1-W6fzvE+73q/43e2VLl/yzFP42Fg=, tarball: https://registry.npm.wps.cn/@types/connect/download/@types/connect-3.4.38.tgz}

  '@types/eslint-scope@3.7.7':
    resolution: {integrity: sha1-MQi9XxiwzbJ3yGez3UScntcHmsU=, tarball: https://registry.npm.wps.cn/@types/eslint-scope/download/@types/eslint-scope-3.7.7.tgz}

  '@types/eslint@8.56.12':
    resolution: {integrity: sha1-FlfIFP/rpNL4TA1LoPRMp+ocpTo=, tarball: https://registry.npm.wps.cn/@types/eslint/download/@types/eslint-8.56.12.tgz}

  '@types/estree@0.0.39':
    resolution: {integrity: sha1-4Xfmme4bjCLSMXTKqnQiZEOJUJ8=, tarball: https://registry.npm.wps.cn/@types/estree/download/@types/estree-0.0.39.tgz}

  '@types/estree@1.0.8':
    resolution: {integrity: sha1-lYuRyZGxhnztMYvt6g4hXuBQcm4=, tarball: https://registry.npm.wps.cn/@types/estree/download/@types/estree-1.0.8.tgz}

  '@types/express-serve-static-core@4.19.6':
    resolution: {integrity: sha1-4BMkwqAk/zZ9ksZvSFU87Qq1Amc=, tarball: https://registry.npm.wps.cn/@types/express-serve-static-core/download/@types/express-serve-static-core-4.19.6.tgz}

  '@types/express-serve-static-core@5.0.6':
    resolution: {integrity: sha1-Qf7E6iDpx7IvAkq4ipXGuyiPUbg=, tarball: https://registry.npm.wps.cn/@types/express-serve-static-core/download/@types/express-serve-static-core-5.0.6.tgz}

  '@types/express@4.17.23':
    resolution: {integrity: sha1-Na8xk8ZAv9TX/ncZHNDtQRpDO+8=, tarball: https://registry.npm.wps.cn/@types/express/download/@types/express-4.17.23.tgz}

  '@types/graceful-fs@4.1.9':
    resolution: {integrity: sha1-Kga8D2iiCrN7PjaqI4vmq99J6LQ=, tarball: https://registry.npm.wps.cn/@types/graceful-fs/download/@types/graceful-fs-4.1.9.tgz}

  '@types/html-minifier-terser@6.1.0':
    resolution: {integrity: sha1-T8M6AMHQwWmHsaIM+S0gYUxVrDU=, tarball: https://registry.npm.wps.cn/@types/html-minifier-terser/download/@types/html-minifier-terser-6.1.0.tgz}

  '@types/http-errors@2.0.5':
    resolution: {integrity: sha1-W3SasrFroRNCP+saZKldzTA5hHI=, tarball: https://registry.npm.wps.cn/@types/http-errors/download/@types/http-errors-2.0.5.tgz}

  '@types/http-proxy@1.17.16':
    resolution: {integrity: sha1-3uNgcHs1s8yFr83on/7r/31/kkA=, tarball: https://registry.npm.wps.cn/@types/http-proxy/download/@types/http-proxy-1.17.16.tgz}

  '@types/istanbul-lib-coverage@2.0.6':
    resolution: {integrity: sha1-dznCMqH+6bTTzomF8xTAxtM1Sdc=, tarball: https://registry.npm.wps.cn/@types/istanbul-lib-coverage/download/@types/istanbul-lib-coverage-2.0.6.tgz}

  '@types/istanbul-lib-report@3.0.3':
    resolution: {integrity: sha1-UwR2FK5y4Z/AQB2HLeOuK0zjUL8=, tarball: https://registry.npm.wps.cn/@types/istanbul-lib-report/download/@types/istanbul-lib-report-3.0.3.tgz}

  '@types/istanbul-reports@3.0.4':
    resolution: {integrity: sha1-DwPj0vZw+9rFhuNLQzeDBwzBb1Q=, tarball: https://registry.npm.wps.cn/@types/istanbul-reports/download/@types/istanbul-reports-3.0.4.tgz}

  '@types/jest@27.5.2':
    resolution: {integrity: sha1-7EnSnZJlAP+5/SK4QmLoYgScAmw=, tarball: https://registry.npm.wps.cn/@types/jest/download/@types/jest-27.5.2.tgz}

  '@types/json-schema@7.0.15':
    resolution: {integrity: sha1-WWoXRyM2lNUPatinhp/Lb1bPWEE=, tarball: https://registry.npm.wps.cn/@types/json-schema/download/@types/json-schema-7.0.15.tgz}

  '@types/json5@0.0.29':
    resolution: {integrity: sha1-7ihweulOEdK4J7y+UnC86n8+ce4=, tarball: https://registry.npm.wps.cn/@types/json5/download/@types/json5-0.0.29.tgz}

  '@types/mime@1.3.5':
    resolution: {integrity: sha1-HvMC4Bz30rWg+lJnkMkSO/HQZpA=, tarball: https://registry.npm.wps.cn/@types/mime/download/@types/mime-1.3.5.tgz}

  '@types/node-forge@1.3.11':
    resolution: {integrity: sha1-CXLqU43bD02cL6DsXbVyR3OmBNo=, tarball: https://registry.npm.wps.cn/@types/node-forge/download/@types/node-forge-1.3.11.tgz}

  '@types/node@16.18.126':
    resolution: {integrity: sha1-J4dfqikmwPR1s5qLseVGwBdvjUs=, tarball: https://registry.npm.wps.cn/@types/node/download/@types/node-16.18.126.tgz}

  '@types/parse-json@4.0.2':
    resolution: {integrity: sha1-WVDlCWB5MFWEXpVsQn/CsNcMUjk=, tarball: https://registry.npm.wps.cn/@types/parse-json/download/@types/parse-json-4.0.2.tgz}

  '@types/prettier@2.7.3':
    resolution: {integrity: sha1-PlGhfikdAdF9P8YUIgFakzr3oI8=, tarball: https://registry.npm.wps.cn/@types/prettier/download/@types/prettier-2.7.3.tgz}

  '@types/prop-types@15.7.15':
    resolution: {integrity: sha1-5uWobWAr6spxzlFj+t9fldcJMcc=, tarball: https://registry.npm.wps.cn/@types/prop-types/download/@types/prop-types-15.7.15.tgz}

  '@types/q@1.5.8':
    resolution: {integrity: sha1-lfbGoI8q2Gi6Iw6tHS1/e+PbODc=, tarball: https://registry.npm.wps.cn/@types/q/download/@types/q-1.5.8.tgz}

  '@types/qs@6.14.0':
    resolution: {integrity: sha1-2LYM7PYvLbD7aOXgBgd7kXi4XeU=, tarball: https://registry.npm.wps.cn/@types/qs/download/@types/qs-6.14.0.tgz}

  '@types/range-parser@1.2.7':
    resolution: {integrity: sha1-UK5DU+qt3AQEQnmBL1LIxlhX28s=, tarball: https://registry.npm.wps.cn/@types/range-parser/download/@types/range-parser-1.2.7.tgz}

  '@types/react-dom@18.3.7':
    resolution: {integrity: sha1-uJ3fLNg7T+r8xOLqQa/fuVoNGU8=, tarball: https://registry.npm.wps.cn/@types/react-dom/download/@types/react-dom-18.3.7.tgz}
    peerDependencies:
      '@types/react': ^18.0.0

  '@types/react@18.3.23':
    resolution: {integrity: sha1-hq5va5WkjEGP7NrMyAaeD7tjaWo=, tarball: https://registry.npm.wps.cn/@types/react/download/@types/react-18.3.23.tgz}

  '@types/resolve@1.17.1':
    resolution: {integrity: sha1-Ov1q2JZ8d+Q3bFmKgt3Vj0bsRdY=, tarball: https://registry.npm.wps.cn/@types/resolve/download/@types/resolve-1.17.1.tgz}

  '@types/retry@0.12.0':
    resolution: {integrity: sha1-KzXsz87n04zXKtmSMvvVi/+zyE0=, tarball: https://registry.npm.wps.cn/@types/retry/download/@types/retry-0.12.0.tgz}

  '@types/semver@7.7.0':
    resolution: {integrity: sha1-ZMRBva4DOzeLbu99DD13wym5N44=, tarball: https://registry.npm.wps.cn/@types/semver/download/@types/semver-7.7.0.tgz}

  '@types/send@0.17.5':
    resolution: {integrity: sha1-2ZHU8rFvKx70lxMfAKkRQpB5HnQ=, tarball: https://registry.npm.wps.cn/@types/send/download/@types/send-0.17.5.tgz}

  '@types/serve-index@1.9.4':
    resolution: {integrity: sha1-5q4T1QU8sG7TY5IRC0+aSaxOyJg=, tarball: https://registry.npm.wps.cn/@types/serve-index/download/@types/serve-index-1.9.4.tgz}

  '@types/serve-static@1.15.8':
    resolution: {integrity: sha1-gYDD++SnDo8AufcLm6fwjzWYeHc=, tarball: https://registry.npm.wps.cn/@types/serve-static/download/@types/serve-static-1.15.8.tgz}

  '@types/sockjs@0.3.36':
    resolution: {integrity: sha1-zjIs8HvMEZ1Mv3+IlU86O9D2dTU=, tarball: https://registry.npm.wps.cn/@types/sockjs/download/@types/sockjs-0.3.36.tgz}

  '@types/stack-utils@2.0.3':
    resolution: {integrity: sha1-YgkyHrLBcSp+dGZCK4yx/A2d1dg=, tarball: https://registry.npm.wps.cn/@types/stack-utils/download/@types/stack-utils-2.0.3.tgz}

  '@types/testing-library__jest-dom@5.14.9':
    resolution: {integrity: sha1-D7HmoCeNh7ZzfbVa9ZZ1cLZ8tGY=, tarball: https://registry.npm.wps.cn/@types/testing-library__jest-dom/download/@types/testing-library__jest-dom-5.14.9.tgz}

  '@types/trusted-types@2.0.7':
    resolution: {integrity: sha1-usywepcLkXB986PoumiWxX6tLRE=, tarball: https://registry.npm.wps.cn/@types/trusted-types/download/@types/trusted-types-2.0.7.tgz}

  '@types/ws@8.18.1':
    resolution: {integrity: sha1-SEZOS/Ld/RfbE9hFRn9gcP/qSqk=, tarball: https://registry.npm.wps.cn/@types/ws/download/@types/ws-8.18.1.tgz}

  '@types/yargs-parser@21.0.3':
    resolution: {integrity: sha1-gV4wt4bS6PDc2F/VvPXhoE0AjxU=, tarball: https://registry.npm.wps.cn/@types/yargs-parser/download/@types/yargs-parser-21.0.3.tgz}

  '@types/yargs@16.0.9':
    resolution: {integrity: sha1-ulBiFeRfdwfmy8rzhpgRVberlW4=, tarball: https://registry.npm.wps.cn/@types/yargs/download/@types/yargs-16.0.9.tgz}

  '@types/yargs@17.0.33':
    resolution: {integrity: sha1-jDIwPag+7AUKhLPHrnufki0T4y0=, tarball: https://registry.npm.wps.cn/@types/yargs/download/@types/yargs-17.0.33.tgz}

  '@typescript-eslint/eslint-plugin@5.62.0':
    resolution: {integrity: sha1-ru8DKNFyueN9m6ttvBO4ftiJd9s=, tarball: https://registry.npm.wps.cn/@typescript-eslint/eslint-plugin/download/@typescript-eslint/eslint-plugin-5.62.0.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      '@typescript-eslint/parser': ^5.0.0
      eslint: ^6.0.0 || ^7.0.0 || ^8.0.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@typescript-eslint/experimental-utils@5.62.0':
    resolution: {integrity: sha1-FFWb9zODowgCa0J6SmEpuuIUZ0E=, tarball: https://registry.npm.wps.cn/@typescript-eslint/experimental-utils/download/@typescript-eslint/experimental-utils-5.62.0.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || ^8.0.0

  '@typescript-eslint/parser@5.62.0':
    resolution: {integrity: sha1-G2PQgthJovyuilaSSPvi7huKVsc=, tarball: https://registry.npm.wps.cn/@typescript-eslint/parser/download/@typescript-eslint/parser-5.62.0.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || ^8.0.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@typescript-eslint/scope-manager@5.62.0':
    resolution: {integrity: sha1-2UV8zGoLjWs30OslKiMCJHjFRgw=, tarball: https://registry.npm.wps.cn/@typescript-eslint/scope-manager/download/@typescript-eslint/scope-manager-5.62.0.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@typescript-eslint/type-utils@5.62.0':
    resolution: {integrity: sha1-KG8DicQWgTds2tlrMJzt0X1wNGo=, tarball: https://registry.npm.wps.cn/@typescript-eslint/type-utils/download/@typescript-eslint/type-utils-5.62.0.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: '*'
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@typescript-eslint/types@5.62.0':
    resolution: {integrity: sha1-JYYH5g7/ownwZ2CJMcPfb+1B/S8=, tarball: https://registry.npm.wps.cn/@typescript-eslint/types/download/@typescript-eslint/types-5.62.0.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@typescript-eslint/typescript-estree@5.62.0':
    resolution: {integrity: sha1-fRd5S3f6vKxhXWpI+xQzMNli65s=, tarball: https://registry.npm.wps.cn/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-5.62.0.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@typescript-eslint/utils@5.62.0':
    resolution: {integrity: sha1-FB6AnHFjbkp12qOfrtL7X0sQ34Y=, tarball: https://registry.npm.wps.cn/@typescript-eslint/utils/download/@typescript-eslint/utils-5.62.0.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || ^8.0.0

  '@typescript-eslint/visitor-keys@5.62.0':
    resolution: {integrity: sha1-IXQBGRfOWCh1lU/+L2kS1ZMeNT4=, tarball: https://registry.npm.wps.cn/@typescript-eslint/visitor-keys/download/@typescript-eslint/visitor-keys-5.62.0.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@ungap/structured-clone@1.3.0':
    resolution: {integrity: sha1-0Gu7OE689sUF/eHD0O1N3/4Kr/g=, tarball: https://registry.npm.wps.cn/@ungap/structured-clone/download/@ungap/structured-clone-1.3.0.tgz}

  '@webassemblyjs/ast@1.14.1':
    resolution: {integrity: sha1-qfagfysDyVyNOMRTah/ftSH/VbY=, tarball: https://registry.npm.wps.cn/@webassemblyjs/ast/download/@webassemblyjs/ast-1.14.1.tgz}

  '@webassemblyjs/floating-point-hex-parser@1.13.2':
    resolution: {integrity: sha1-/Moe7dscxOe27tT8eVbWgTshufs=, tarball: https://registry.npm.wps.cn/@webassemblyjs/floating-point-hex-parser/download/@webassemblyjs/floating-point-hex-parser-1.13.2.tgz}

  '@webassemblyjs/helper-api-error@1.13.2':
    resolution: {integrity: sha1-4KFhUiSLw42u523X4h8Vxe86sec=, tarball: https://registry.npm.wps.cn/@webassemblyjs/helper-api-error/download/@webassemblyjs/helper-api-error-1.13.2.tgz}

  '@webassemblyjs/helper-buffer@1.14.1':
    resolution: {integrity: sha1-giqbxgMWZTH31d+E5ntb+ZtyuWs=, tarball: https://registry.npm.wps.cn/@webassemblyjs/helper-buffer/download/@webassemblyjs/helper-buffer-1.14.1.tgz}

  '@webassemblyjs/helper-numbers@1.13.2':
    resolution: {integrity: sha1-29kyVI5xGfS4p4d/1ajSDmNJCy0=, tarball: https://registry.npm.wps.cn/@webassemblyjs/helper-numbers/download/@webassemblyjs/helper-numbers-1.13.2.tgz}

  '@webassemblyjs/helper-wasm-bytecode@1.13.2':
    resolution: {integrity: sha1-5VYQh1j0SKroTIUOWTzhig6zHgs=, tarball: https://registry.npm.wps.cn/@webassemblyjs/helper-wasm-bytecode/download/@webassemblyjs/helper-wasm-bytecode-1.13.2.tgz}

  '@webassemblyjs/helper-wasm-section@1.14.1':
    resolution: {integrity: sha1-lindqcRDDqtUtZEFPW3G87oFA0g=, tarball: https://registry.npm.wps.cn/@webassemblyjs/helper-wasm-section/download/@webassemblyjs/helper-wasm-section-1.14.1.tgz}

  '@webassemblyjs/ieee754@1.13.2':
    resolution: {integrity: sha1-HF6qzh1gatosf9cEXqk1bFnuDbo=, tarball: https://registry.npm.wps.cn/@webassemblyjs/ieee754/download/@webassemblyjs/ieee754-1.13.2.tgz}

  '@webassemblyjs/leb128@1.13.2':
    resolution: {integrity: sha1-V8XD3rAQXQLOJfo/109OvJ/Qu7A=, tarball: https://registry.npm.wps.cn/@webassemblyjs/leb128/download/@webassemblyjs/leb128-1.13.2.tgz}

  '@webassemblyjs/utf8@1.13.2':
    resolution: {integrity: sha1-kXog6T9xrVYClmwtaFrgxsIfYPE=, tarball: https://registry.npm.wps.cn/@webassemblyjs/utf8/download/@webassemblyjs/utf8-1.13.2.tgz}

  '@webassemblyjs/wasm-edit@1.14.1':
    resolution: {integrity: sha1-rGaJ9QIhm1kZjd7ELc1JaxAE1Zc=, tarball: https://registry.npm.wps.cn/@webassemblyjs/wasm-edit/download/@webassemblyjs/wasm-edit-1.14.1.tgz}

  '@webassemblyjs/wasm-gen@1.14.1':
    resolution: {integrity: sha1-mR5/DAkMsLtiu6yIIHbj0hnalXA=, tarball: https://registry.npm.wps.cn/@webassemblyjs/wasm-gen/download/@webassemblyjs/wasm-gen-1.14.1.tgz}

  '@webassemblyjs/wasm-opt@1.14.1':
    resolution: {integrity: sha1-5vce18yuRngcIGAX08FMUO+oEGs=, tarball: https://registry.npm.wps.cn/@webassemblyjs/wasm-opt/download/@webassemblyjs/wasm-opt-1.14.1.tgz}

  '@webassemblyjs/wasm-parser@1.14.1':
    resolution: {integrity: sha1-s+E/GJNgXKeLUsaOVM9qhl+Qufs=, tarball: https://registry.npm.wps.cn/@webassemblyjs/wasm-parser/download/@webassemblyjs/wasm-parser-1.14.1.tgz}

  '@webassemblyjs/wast-printer@1.14.1':
    resolution: {integrity: sha1-O7PpY4qK5f2vlhDnoGtNn5qm/gc=, tarball: https://registry.npm.wps.cn/@webassemblyjs/wast-printer/download/@webassemblyjs/wast-printer-1.14.1.tgz}

  '@xtuc/ieee754@1.2.0':
    resolution: {integrity: sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A=, tarball: https://registry.npm.wps.cn/@xtuc/ieee754/download/@xtuc/ieee754-1.2.0.tgz}

  '@xtuc/long@4.2.2':
    resolution: {integrity: sha1-0pHGpOl5ibXGHZrPOWrk/hM6cY0=, tarball: https://registry.npm.wps.cn/@xtuc/long/download/@xtuc/long-4.2.2.tgz}

  abab@2.0.6:
    resolution: {integrity: sha1-QbgPLIcdGWhiFrgjCSMc/Tyz0pE=, tarball: https://registry.npm.wps.cn/abab/download/abab-2.0.6.tgz}
    deprecated: Use your platform's native atob() and btoa() methods instead

  accepts@1.3.8:
    resolution: {integrity: sha1-C/C+EltnAUrcsLCSHmLbe//hay4=, tarball: https://registry.npm.wps.cn/accepts/download/accepts-1.3.8.tgz}
    engines: {node: '>= 0.6'}

  acorn-globals@6.0.0:
    resolution: {integrity: sha1-Rs3Tnw+P8IqHZhm1X1rIptx3C0U=, tarball: https://registry.npm.wps.cn/acorn-globals/download/acorn-globals-6.0.0.tgz}

  acorn-import-phases@1.0.3:
    resolution: {integrity: sha1-MDlKHczuXzgK7LggW4xptPeuaI4=, tarball: https://registry.npm.wps.cn/acorn-import-phases/download/acorn-import-phases-1.0.3.tgz}
    engines: {node: '>=10.13.0'}
    peerDependencies:
      acorn: ^8.14.0

  acorn-jsx@5.3.2:
    resolution: {integrity: sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=, tarball: https://registry.npm.wps.cn/acorn-jsx/download/acorn-jsx-5.3.2.tgz}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0

  acorn-walk@7.2.0:
    resolution: {integrity: sha1-DeiJpgEgOQmw++B7iTjcIdLpZ7w=, tarball: https://registry.npm.wps.cn/acorn-walk/download/acorn-walk-7.2.0.tgz}
    engines: {node: '>=0.4.0'}

  acorn-walk@8.3.4:
    resolution: {integrity: sha1-eU3RacOXft9LpOpHWDWHxYZiNrc=, tarball: https://registry.npm.wps.cn/acorn-walk/download/acorn-walk-8.3.4.tgz}
    engines: {node: '>=0.4.0'}

  acorn@7.4.1:
    resolution: {integrity: sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo=, tarball: https://registry.npm.wps.cn/acorn/download/acorn-7.4.1.tgz}
    engines: {node: '>=0.4.0'}
    hasBin: true

  acorn@8.15.0:
    resolution: {integrity: sha1-o2CJi8QV7arEbIJB9jg5dbkwuBY=, tarball: https://registry.npm.wps.cn/acorn/download/acorn-8.15.0.tgz}
    engines: {node: '>=0.4.0'}
    hasBin: true

  address@1.2.2:
    resolution: {integrity: sha1-K1JI2sVIWmOQUyxqUX/aLj+qyJ4=, tarball: https://registry.npm.wps.cn/address/download/address-1.2.2.tgz}
    engines: {node: '>= 10.0.0'}

  adjust-sourcemap-loader@4.0.0:
    resolution: {integrity: sha1-/EoP0ID30QRx8wpzIPJVYK3ijJk=, tarball: https://registry.npm.wps.cn/adjust-sourcemap-loader/download/adjust-sourcemap-loader-4.0.0.tgz}
    engines: {node: '>=8.9'}

  agent-base@6.0.2:
    resolution: {integrity: sha1-Sf/1hXfP7j83F2/qtMIuAPhtf3c=, tarball: https://registry.npm.wps.cn/agent-base/download/agent-base-6.0.2.tgz}
    engines: {node: '>= 6.0.0'}

  ajv-formats@2.1.1:
    resolution: {integrity: sha1-bmaUAGWet0lzu/LjMycYCgmWtSA=, tarball: https://registry.npm.wps.cn/ajv-formats/download/ajv-formats-2.1.1.tgz}
    peerDependencies:
      ajv: ^8.0.0
    peerDependenciesMeta:
      ajv:
        optional: true

  ajv-keywords@3.5.2:
    resolution: {integrity: sha1-MfKdpatuANHC0yms97WSlhTVAU0=, tarball: https://registry.npm.wps.cn/ajv-keywords/download/ajv-keywords-3.5.2.tgz}
    peerDependencies:
      ajv: ^6.9.1

  ajv-keywords@5.1.0:
    resolution: {integrity: sha1-adTThaRzPNvqtElkoRcKiPh/DhY=, tarball: https://registry.npm.wps.cn/ajv-keywords/download/ajv-keywords-5.1.0.tgz}
    peerDependencies:
      ajv: ^8.8.2

  ajv@6.12.6:
    resolution: {integrity: sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=, tarball: https://registry.npm.wps.cn/ajv/download/ajv-6.12.6.tgz}

  ajv@8.17.1:
    resolution: {integrity: sha1-N9mlx3ava8ktf0+VEOukwKYNEaY=, tarball: https://registry.npm.wps.cn/ajv/download/ajv-8.17.1.tgz}

  ansi-escapes@4.3.2:
    resolution: {integrity: sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=, tarball: https://registry.npm.wps.cn/ansi-escapes/download/ansi-escapes-4.3.2.tgz}
    engines: {node: '>=8'}

  ansi-html-community@0.0.8:
    resolution: {integrity: sha1-afvE1sy+OD+XNpNK40w/gpDxv0E=, tarball: https://registry.npm.wps.cn/ansi-html-community/download/ansi-html-community-0.0.8.tgz}
    engines: {'0': node >= 0.8.0}
    hasBin: true

  ansi-html@0.0.9:
    resolution: {integrity: sha1-ZRLQI0KuLMaBMZUmRKEpy3NM0/A=, tarball: https://registry.npm.wps.cn/ansi-html/download/ansi-html-0.0.9.tgz}
    engines: {'0': node >= 0.8.0}
    hasBin: true

  ansi-regex@5.0.1:
    resolution: {integrity: sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=, tarball: https://registry.npm.wps.cn/ansi-regex/download/ansi-regex-5.0.1.tgz}
    engines: {node: '>=8'}

  ansi-regex@6.1.0:
    resolution: {integrity: sha1-lexAnGlhnWyxuLNPFLZg7yjr1lQ=, tarball: https://registry.npm.wps.cn/ansi-regex/download/ansi-regex-6.1.0.tgz}
    engines: {node: '>=12'}

  ansi-styles@3.2.1:
    resolution: {integrity: sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=, tarball: https://registry.npm.wps.cn/ansi-styles/download/ansi-styles-3.2.1.tgz}
    engines: {node: '>=4'}

  ansi-styles@4.3.0:
    resolution: {integrity: sha1-7dgDYornHATIWuegkG7a00tkiTc=, tarball: https://registry.npm.wps.cn/ansi-styles/download/ansi-styles-4.3.0.tgz}
    engines: {node: '>=8'}

  ansi-styles@5.2.0:
    resolution: {integrity: sha1-B0SWkK1Fd30ZJKwquy/IiV26g2s=, tarball: https://registry.npm.wps.cn/ansi-styles/download/ansi-styles-5.2.0.tgz}
    engines: {node: '>=10'}

  ansi-styles@6.2.1:
    resolution: {integrity: sha1-DmIyDPmcIa//OzASGSVGqsv7BcU=, tarball: https://registry.npm.wps.cn/ansi-styles/download/ansi-styles-6.2.1.tgz}
    engines: {node: '>=12'}

  any-promise@1.3.0:
    resolution: {integrity: sha1-q8av7tzqUugJzcA3au0845Y10X8=, tarball: https://registry.npm.wps.cn/any-promise/download/any-promise-1.3.0.tgz}

  anymatch@3.1.3:
    resolution: {integrity: sha1-eQxYsZuhcgqEIFtXxhjVrYUklz4=, tarball: https://registry.npm.wps.cn/anymatch/download/anymatch-3.1.3.tgz}
    engines: {node: '>= 8'}

  arg@4.1.3:
    resolution: {integrity: sha1-Jp/HrVuOQstjyJbVZmAXJhwUQIk=, tarball: https://registry.npm.wps.cn/arg/download/arg-4.1.3.tgz}

  arg@5.0.2:
    resolution: {integrity: sha1-yBQzzEJ8ksTc9IZRQtvKbxWs1Zw=, tarball: https://registry.npm.wps.cn/arg/download/arg-5.0.2.tgz}

  argparse@1.0.10:
    resolution: {integrity: sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=, tarball: https://registry.npm.wps.cn/argparse/download/argparse-1.0.10.tgz}

  argparse@2.0.1:
    resolution: {integrity: sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=, tarball: https://registry.npm.wps.cn/argparse/download/argparse-2.0.1.tgz}

  aria-query@5.1.3:
    resolution: {integrity: sha1-GdsnzRARUnc2MTlvepWjtYwiw14=, tarball: https://registry.npm.wps.cn/aria-query/download/aria-query-5.1.3.tgz}

  aria-query@5.3.2:
    resolution: {integrity: sha1-k/gaQ0gOM6M48ZFjo9EKUMAdzVk=, tarball: https://registry.npm.wps.cn/aria-query/download/aria-query-5.3.2.tgz}
    engines: {node: '>= 0.4'}

  array-buffer-byte-length@1.0.2:
    resolution: {integrity: sha1-OE0So3KVrsN2mrAirTI6GKUcz4s=, tarball: https://registry.npm.wps.cn/array-buffer-byte-length/download/array-buffer-byte-length-1.0.2.tgz}
    engines: {node: '>= 0.4'}

  array-flatten@1.1.1:
    resolution: {integrity: sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=, tarball: https://registry.npm.wps.cn/array-flatten/download/array-flatten-1.1.1.tgz}

  array-includes@3.1.9:
    resolution: {integrity: sha1-HwzKoI6Qzbw+tDMhD5A60PF8Pzo=, tarball: https://registry.npm.wps.cn/array-includes/download/array-includes-3.1.9.tgz}
    engines: {node: '>= 0.4'}

  array-union@2.1.0:
    resolution: {integrity: sha1-t5hCCtvrHego2ErNii4j0+/oXo0=, tarball: https://registry.npm.wps.cn/array-union/download/array-union-2.1.0.tgz}
    engines: {node: '>=8'}

  array.prototype.findlast@1.2.5:
    resolution: {integrity: sha1-Pk+8swoVp/W/ZM8vquItE5wuSQQ=, tarball: https://registry.npm.wps.cn/array.prototype.findlast/download/array.prototype.findlast-1.2.5.tgz}
    engines: {node: '>= 0.4'}

  array.prototype.findlastindex@1.2.6:
    resolution: {integrity: sha1-z6EGXIHctk40VXybgdAS9qQhxWQ=, tarball: https://registry.npm.wps.cn/array.prototype.findlastindex/download/array.prototype.findlastindex-1.2.6.tgz}
    engines: {node: '>= 0.4'}

  array.prototype.flat@1.3.3:
    resolution: {integrity: sha1-U0qvnm6N15+2uamRf4Oe8exjr+U=, tarball: https://registry.npm.wps.cn/array.prototype.flat/download/array.prototype.flat-1.3.3.tgz}
    engines: {node: '>= 0.4'}

  array.prototype.flatmap@1.3.3:
    resolution: {integrity: sha1-cSzHkq5wNwrkBYYmRinjOqtd04s=, tarball: https://registry.npm.wps.cn/array.prototype.flatmap/download/array.prototype.flatmap-1.3.3.tgz}
    engines: {node: '>= 0.4'}

  array.prototype.reduce@1.0.8:
    resolution: {integrity: sha1-Qvl/UHja7cpofURj/TwFy/2D2lc=, tarball: https://registry.npm.wps.cn/array.prototype.reduce/download/array.prototype.reduce-1.0.8.tgz}
    engines: {node: '>= 0.4'}

  array.prototype.tosorted@1.1.4:
    resolution: {integrity: sha1-/pVGeP9TA05xfqM1KgPwsLhvf/w=, tarball: https://registry.npm.wps.cn/array.prototype.tosorted/download/array.prototype.tosorted-1.1.4.tgz}
    engines: {node: '>= 0.4'}

  arraybuffer.prototype.slice@1.0.4:
    resolution: {integrity: sha1-nXYNhNvdBtDL+SyISWFaGnqzGDw=, tarball: https://registry.npm.wps.cn/arraybuffer.prototype.slice/download/arraybuffer.prototype.slice-1.0.4.tgz}
    engines: {node: '>= 0.4'}

  asap@2.0.6:
    resolution: {integrity: sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY=, tarball: https://registry.npm.wps.cn/asap/download/asap-2.0.6.tgz}

  ast-types-flow@0.0.8:
    resolution: {integrity: sha1-CoXhySaVdprBOkKLtlPnU4vqJ9Y=, tarball: https://registry.npm.wps.cn/ast-types-flow/download/ast-types-flow-0.0.8.tgz}

  async-function@1.0.0:
    resolution: {integrity: sha1-UJyfymDq+FA0xoKYOBiOTkyP+ys=, tarball: https://registry.npm.wps.cn/async-function/download/async-function-1.0.0.tgz}
    engines: {node: '>= 0.4'}

  async@3.2.6:
    resolution: {integrity: sha1-Gwco4Ukp1RuFtEm38G4nwRReOM4=, tarball: https://registry.npm.wps.cn/async/download/async-3.2.6.tgz}

  asynckit@0.4.0:
    resolution: {integrity: sha1-x57Zf380y48robyXkLzDZkdLS3k=, tarball: https://registry.npm.wps.cn/asynckit/download/asynckit-0.4.0.tgz}

  at-least-node@1.0.0:
    resolution: {integrity: sha1-YCzUtG6EStTv/JKoARo8RuAjjcI=, tarball: https://registry.npm.wps.cn/at-least-node/download/at-least-node-1.0.0.tgz}
    engines: {node: '>= 4.0.0'}

  autoprefixer@10.4.21:
    resolution: {integrity: sha1-dxiUaOeorR2aN/vAjvyfSAzwqV0=, tarball: https://registry.npm.wps.cn/autoprefixer/download/autoprefixer-10.4.21.tgz}
    engines: {node: ^10 || ^12 || >=14}
    hasBin: true
    peerDependencies:
      postcss: ^8.1.0

  available-typed-arrays@1.0.7:
    resolution: {integrity: sha1-pcw3XWoDwu/IelU/PgsVIt7xSEY=, tarball: https://registry.npm.wps.cn/available-typed-arrays/download/available-typed-arrays-1.0.7.tgz}
    engines: {node: '>= 0.4'}

  axe-core@4.10.3:
    resolution: {integrity: sha1-BBRZZax4lPrdusMIYeXY8Rv9FPw=, tarball: https://registry.npm.wps.cn/axe-core/download/axe-core-4.10.3.tgz}
    engines: {node: '>=4'}

  axobject-query@4.1.0:
    resolution: {integrity: sha1-KHaMdtDjz/IbxiqeLQtqwwBCoe4=, tarball: https://registry.npm.wps.cn/axobject-query/download/axobject-query-4.1.0.tgz}
    engines: {node: '>= 0.4'}

  babel-jest@27.5.1:
    resolution: {integrity: sha1-ob+NYZKO3+/SHaJ+uGppW/1pFEQ=, tarball: https://registry.npm.wps.cn/babel-jest/download/babel-jest-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}
    peerDependencies:
      '@babel/core': ^7.8.0

  babel-loader@8.4.1:
    resolution: {integrity: sha1-bMt1xm5iw7FE4cXy6uxbj2wIxnU=, tarball: https://registry.npm.wps.cn/babel-loader/download/babel-loader-8.4.1.tgz}
    engines: {node: '>= 8.9'}
    peerDependencies:
      '@babel/core': ^7.0.0
      webpack: '>=2'

  babel-plugin-istanbul@6.1.1:
    resolution: {integrity: sha1-+ojsWSMv2bTjbbvFQKjsmptH2nM=, tarball: https://registry.npm.wps.cn/babel-plugin-istanbul/download/babel-plugin-istanbul-6.1.1.tgz}
    engines: {node: '>=8'}

  babel-plugin-jest-hoist@27.5.1:
    resolution: {integrity: sha1-m+mOzyjDMeufXfnHLW+J3rgYHC4=, tarball: https://registry.npm.wps.cn/babel-plugin-jest-hoist/download/babel-plugin-jest-hoist-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  babel-plugin-macros@3.1.0:
    resolution: {integrity: sha1-nvbcdN65NLTbNE3Jc+6FHRSMUME=, tarball: https://registry.npm.wps.cn/babel-plugin-macros/download/babel-plugin-macros-3.1.0.tgz}
    engines: {node: '>=10', npm: '>=6'}

  babel-plugin-named-asset-import@0.3.8:
    resolution: {integrity: sha1-a3+kPFkiloU2hoPCi8lzTyRSTMI=, tarball: https://registry.npm.wps.cn/babel-plugin-named-asset-import/download/babel-plugin-named-asset-import-0.3.8.tgz}
    peerDependencies:
      '@babel/core': ^7.1.0

  babel-plugin-polyfill-corejs2@0.4.14:
    resolution: {integrity: sha1-gQG4K3acVog1YRVCSI1GM5XC748=, tarball: https://registry.npm.wps.cn/babel-plugin-polyfill-corejs2/download/babel-plugin-polyfill-corejs2-0.4.14.tgz}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  babel-plugin-polyfill-corejs3@0.13.0:
    resolution: {integrity: sha1-u39q7vet3/F/dgKgim0ZoSjDAWQ=, tarball: https://registry.npm.wps.cn/babel-plugin-polyfill-corejs3/download/babel-plugin-polyfill-corejs3-0.13.0.tgz}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  babel-plugin-polyfill-regenerator@0.6.5:
    resolution: {integrity: sha1-MnUuOKtvZ2e5JlA0e/JqMbFq6MU=, tarball: https://registry.npm.wps.cn/babel-plugin-polyfill-regenerator/download/babel-plugin-polyfill-regenerator-0.6.5.tgz}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  babel-plugin-transform-react-remove-prop-types@0.4.24:
    resolution: {integrity: sha1-8u2vm0xqX75cHWeL+1MQeMFVXzo=, tarball: https://registry.npm.wps.cn/babel-plugin-transform-react-remove-prop-types/download/babel-plugin-transform-react-remove-prop-types-0.4.24.tgz}

  babel-preset-current-node-syntax@1.1.0:
    resolution: {integrity: sha1-mpKer+zkGWEu9K5PYLGGLrrY7zA=, tarball: https://registry.npm.wps.cn/babel-preset-current-node-syntax/download/babel-preset-current-node-syntax-1.1.0.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0

  babel-preset-jest@27.5.1:
    resolution: {integrity: sha1-kfEPWANMt5ictPlitp+m7vamvIE=, tarball: https://registry.npm.wps.cn/babel-preset-jest/download/babel-preset-jest-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}
    peerDependencies:
      '@babel/core': ^7.0.0

  babel-preset-react-app@10.1.0:
    resolution: {integrity: sha1-42fyI/bCeHjmzChHHQ1Qapq5+Ww=, tarball: https://registry.npm.wps.cn/babel-preset-react-app/download/babel-preset-react-app-10.1.0.tgz}

  balanced-match@1.0.2:
    resolution: {integrity: sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=, tarball: https://registry.npm.wps.cn/balanced-match/download/balanced-match-1.0.2.tgz}

  batch@0.6.1:
    resolution: {integrity: sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY=, tarball: https://registry.npm.wps.cn/batch/download/batch-0.6.1.tgz}

  bfj@7.1.0:
    resolution: {integrity: sha1-xRd9UiED+QQOGxKYD+jDjPQdP4s=, tarball: https://registry.npm.wps.cn/bfj/download/bfj-7.1.0.tgz}
    engines: {node: '>= 8.0.0'}

  big.js@5.2.2:
    resolution: {integrity: sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg=, tarball: https://registry.npm.wps.cn/big.js/download/big.js-5.2.2.tgz}

  binary-extensions@2.3.0:
    resolution: {integrity: sha1-9uFKl4WNMnJSIAJC1Mz+UixEVSI=, tarball: https://registry.npm.wps.cn/binary-extensions/download/binary-extensions-2.3.0.tgz}
    engines: {node: '>=8'}

  bluebird@3.7.2:
    resolution: {integrity: sha1-nyKcFb4nJFT/qXOs4NvueaGww28=, tarball: https://registry.npm.wps.cn/bluebird/download/bluebird-3.7.2.tgz}

  body-parser@1.20.3:
    resolution: {integrity: sha1-GVNDEiHG+1zWPEs21T+rCSjlSMY=, tarball: https://registry.npm.wps.cn/body-parser/download/body-parser-1.20.3.tgz}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}

  bonjour-service@1.3.0:
    resolution: {integrity: sha1-gNhnQwtaDaZOgqgEf8HjVb23FyI=, tarball: https://registry.npm.wps.cn/bonjour-service/download/bonjour-service-1.3.0.tgz}

  boolbase@1.0.0:
    resolution: {integrity: sha1-aN/1++YMUes3cl6p4+0xDcwed24=, tarball: https://registry.npm.wps.cn/boolbase/download/boolbase-1.0.0.tgz}

  brace-expansion@1.1.12:
    resolution: {integrity: sha1-q5tFRGblqMw6GHvqrVgEEqnFuEM=, tarball: https://registry.npm.wps.cn/brace-expansion/download/brace-expansion-1.1.12.tgz}

  brace-expansion@2.0.2:
    resolution: {integrity: sha1-VPxTI3phPYVMe9N0Y6rRffhyFOc=, tarball: https://registry.npm.wps.cn/brace-expansion/download/brace-expansion-2.0.2.tgz}

  braces@3.0.3:
    resolution: {integrity: sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=, tarball: https://registry.npm.wps.cn/braces/download/braces-3.0.3.tgz}
    engines: {node: '>=8'}

  browser-process-hrtime@1.0.0:
    resolution: {integrity: sha1-PJtLfXgsgSHlbxAQbYTA0P/JRiY=, tarball: https://registry.npm.wps.cn/browser-process-hrtime/download/browser-process-hrtime-1.0.0.tgz}

  browserslist@4.25.1:
    resolution: {integrity: sha1-up6ObymKHYb4Kcm5deB5SJZ7sRE=, tarball: https://registry.npm.wps.cn/browserslist/download/browserslist-4.25.1.tgz}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true

  bser@2.1.1:
    resolution: {integrity: sha1-5nh9og7OnQeZhTPP2d5vXDj0vAU=, tarball: https://registry.npm.wps.cn/bser/download/bser-2.1.1.tgz}

  buffer-from@1.1.2:
    resolution: {integrity: sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=, tarball: https://registry.npm.wps.cn/buffer-from/download/buffer-from-1.1.2.tgz}

  builtin-modules@3.3.0:
    resolution: {integrity: sha1-yuYoEriYAellYzbkYiPgMDhr57Y=, tarball: https://registry.npm.wps.cn/builtin-modules/download/builtin-modules-3.3.0.tgz}
    engines: {node: '>=6'}

  bytes@3.1.2:
    resolution: {integrity: sha1-iwvuuYYFrfGxKPpDhkA8AJ4CIaU=, tarball: https://registry.npm.wps.cn/bytes/download/bytes-3.1.2.tgz}
    engines: {node: '>= 0.8'}

  call-bind-apply-helpers@1.0.2:
    resolution: {integrity: sha1-S1QowiK+mF15w9gmV0edvgtZstY=, tarball: https://registry.npm.wps.cn/call-bind-apply-helpers/download/call-bind-apply-helpers-1.0.2.tgz}
    engines: {node: '>= 0.4'}

  call-bind@1.0.8:
    resolution: {integrity: sha1-BzapZg9TfjOIgm9EDV7EX3ROqkw=, tarball: https://registry.npm.wps.cn/call-bind/download/call-bind-1.0.8.tgz}
    engines: {node: '>= 0.4'}

  call-bound@1.0.4:
    resolution: {integrity: sha1-I43pNdKippKSjFOMfM+pEGf9Bio=, tarball: https://registry.npm.wps.cn/call-bound/download/call-bound-1.0.4.tgz}
    engines: {node: '>= 0.4'}

  callsites@3.1.0:
    resolution: {integrity: sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=, tarball: https://registry.npm.wps.cn/callsites/download/callsites-3.1.0.tgz}
    engines: {node: '>=6'}

  camel-case@4.1.2:
    resolution: {integrity: sha1-lygHKpVPgFIoIlpt7qazhGHhvVo=, tarball: https://registry.npm.wps.cn/camel-case/download/camel-case-4.1.2.tgz}

  camelcase-css@2.0.1:
    resolution: {integrity: sha1-7pePaUeRTMMMa0R0G27R338EP9U=, tarball: https://registry.npm.wps.cn/camelcase-css/download/camelcase-css-2.0.1.tgz}
    engines: {node: '>= 6'}

  camelcase@5.3.1:
    resolution: {integrity: sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=, tarball: https://registry.npm.wps.cn/camelcase/download/camelcase-5.3.1.tgz}
    engines: {node: '>=6'}

  camelcase@6.3.0:
    resolution: {integrity: sha1-VoW5XrIJrJwMF3Rnd4ychN9Yupo=, tarball: https://registry.npm.wps.cn/camelcase/download/camelcase-6.3.0.tgz}
    engines: {node: '>=10'}

  caniuse-api@3.0.0:
    resolution: {integrity: sha1-Xk2Q4idJYdRikZl99Znj7QCO5MA=, tarball: https://registry.npm.wps.cn/caniuse-api/download/caniuse-api-3.0.0.tgz}

  caniuse-lite@1.0.30001727:
    resolution: {integrity: sha1-IulwZCKtN6pQVWr4wQ5A4tk6i4U=, tarball: https://registry.npm.wps.cn/caniuse-lite/download/caniuse-lite-1.0.30001727.tgz}

  case-sensitive-paths-webpack-plugin@2.4.0:
    resolution: {integrity: sha1-22QGbGQi7tLgjMFLmGykN5bbxtQ=, tarball: https://registry.npm.wps.cn/case-sensitive-paths-webpack-plugin/download/case-sensitive-paths-webpack-plugin-2.4.0.tgz}
    engines: {node: '>=4'}

  chalk@2.4.2:
    resolution: {integrity: sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=, tarball: https://registry.npm.wps.cn/chalk/download/chalk-2.4.2.tgz}
    engines: {node: '>=4'}

  chalk@3.0.0:
    resolution: {integrity: sha1-P3PCv1JlkfV0zEksUeJFY0n4ROQ=, tarball: https://registry.npm.wps.cn/chalk/download/chalk-3.0.0.tgz}
    engines: {node: '>=8'}

  chalk@4.1.2:
    resolution: {integrity: sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=, tarball: https://registry.npm.wps.cn/chalk/download/chalk-4.1.2.tgz}
    engines: {node: '>=10'}

  char-regex@1.0.2:
    resolution: {integrity: sha1-10Q1giYhf5ge1Y9Hmx1rzClUXc8=, tarball: https://registry.npm.wps.cn/char-regex/download/char-regex-1.0.2.tgz}
    engines: {node: '>=10'}

  char-regex@2.0.2:
    resolution: {integrity: sha1-gThbsHGvTfd0v/hyHQyhXvKeoLs=, tarball: https://registry.npm.wps.cn/char-regex/download/char-regex-2.0.2.tgz}
    engines: {node: '>=12.20'}

  check-types@11.2.3:
    resolution: {integrity: sha1-H/32j6rk6UH84lKECxeHuO3JO3E=, tarball: https://registry.npm.wps.cn/check-types/download/check-types-11.2.3.tgz}

  chokidar@3.6.0:
    resolution: {integrity: sha1-GXxsxmnvKo3F57TZfuTgksPrDVs=, tarball: https://registry.npm.wps.cn/chokidar/download/chokidar-3.6.0.tgz}
    engines: {node: '>= 8.10.0'}

  chrome-trace-event@1.0.4:
    resolution: {integrity: sha1-Bb/9f/koRlCTMUcIyTvfqb0fD1s=, tarball: https://registry.npm.wps.cn/chrome-trace-event/download/chrome-trace-event-1.0.4.tgz}
    engines: {node: '>=6.0'}

  ci-info@3.9.0:
    resolution: {integrity: sha1-QnmmICinsfJi80c/yWBfXiGMWbQ=, tarball: https://registry.npm.wps.cn/ci-info/download/ci-info-3.9.0.tgz}
    engines: {node: '>=8'}

  cjs-module-lexer@1.4.3:
    resolution: {integrity: sha1-D3lzHrjP4exyrNQGbvrJ1hmRsA0=, tarball: https://registry.npm.wps.cn/cjs-module-lexer/download/cjs-module-lexer-1.4.3.tgz}

  classnames@2.5.1:
    resolution: {integrity: sha1-undMYUvg8BbaEFyFjnFZ6ujnaHs=, tarball: https://registry.npm.wps.cn/classnames/download/classnames-2.5.1.tgz}

  clean-css@5.3.3:
    resolution: {integrity: sha1-szBlPNO9a3UAnMJccUyue5M1HM0=, tarball: https://registry.npm.wps.cn/clean-css/download/clean-css-5.3.3.tgz}
    engines: {node: '>= 10.0'}

  cliui@7.0.4:
    resolution: {integrity: sha1-oCZe5lVHb8gHrqnfPfjfd4OAi08=, tarball: https://registry.npm.wps.cn/cliui/download/cliui-7.0.4.tgz}

  clone-deep@4.0.1:
    resolution: {integrity: sha1-wZ/Zvbv4WUK0/ZechNz31fB8I4c=, tarball: https://registry.npm.wps.cn/clone-deep/download/clone-deep-4.0.1.tgz}
    engines: {node: '>=6'}

  co@4.6.0:
    resolution: {integrity: sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=, tarball: https://registry.npm.wps.cn/co/download/co-4.6.0.tgz}
    engines: {iojs: '>= 1.0.0', node: '>= 0.12.0'}

  coa@2.0.2:
    resolution: {integrity: sha1-Q/bCEVG07yv1cYfbDXPeIp4+fsM=, tarball: https://registry.npm.wps.cn/coa/download/coa-2.0.2.tgz}
    engines: {node: '>= 4.0'}

  collect-v8-coverage@1.0.2:
    resolution: {integrity: sha1-wLKbzTO80HeaE0TCE2BR5q/T2ek=, tarball: https://registry.npm.wps.cn/collect-v8-coverage/download/collect-v8-coverage-1.0.2.tgz}

  color-convert@1.9.3:
    resolution: {integrity: sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=, tarball: https://registry.npm.wps.cn/color-convert/download/color-convert-1.9.3.tgz}

  color-convert@2.0.1:
    resolution: {integrity: sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=, tarball: https://registry.npm.wps.cn/color-convert/download/color-convert-2.0.1.tgz}
    engines: {node: '>=7.0.0'}

  color-name@1.1.3:
    resolution: {integrity: sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=, tarball: https://registry.npm.wps.cn/color-name/download/color-name-1.1.3.tgz}

  color-name@1.1.4:
    resolution: {integrity: sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=, tarball: https://registry.npm.wps.cn/color-name/download/color-name-1.1.4.tgz}

  colord@2.9.3:
    resolution: {integrity: sha1-T4zpGd5Fbx1cHDaMMH/iDz5Z+0M=, tarball: https://registry.npm.wps.cn/colord/download/colord-2.9.3.tgz}

  colorette@2.0.20:
    resolution: {integrity: sha1-nreT5oMwZ/cjWQL807CZF6AAqVo=, tarball: https://registry.npm.wps.cn/colorette/download/colorette-2.0.20.tgz}

  combined-stream@1.0.8:
    resolution: {integrity: sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=, tarball: https://registry.npm.wps.cn/combined-stream/download/combined-stream-1.0.8.tgz}
    engines: {node: '>= 0.8'}

  commander@2.20.3:
    resolution: {integrity: sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=, tarball: https://registry.npm.wps.cn/commander/download/commander-2.20.3.tgz}

  commander@4.1.1:
    resolution: {integrity: sha1-n9YCvZNilOnp70aj9NaWQESxgGg=, tarball: https://registry.npm.wps.cn/commander/download/commander-4.1.1.tgz}
    engines: {node: '>= 6'}

  commander@7.2.0:
    resolution: {integrity: sha1-o2y1fQtQHOEI5NIFWaFQo5HZerc=, tarball: https://registry.npm.wps.cn/commander/download/commander-7.2.0.tgz}
    engines: {node: '>= 10'}

  commander@8.3.0:
    resolution: {integrity: sha1-SDfqGy2me5xhamevuw+v7lZ7ymY=, tarball: https://registry.npm.wps.cn/commander/download/commander-8.3.0.tgz}
    engines: {node: '>= 12'}

  common-tags@1.8.2:
    resolution: {integrity: sha1-lOuzwHbSYDJ0X9VPrOf2iO9aycY=, tarball: https://registry.npm.wps.cn/common-tags/download/common-tags-1.8.2.tgz}
    engines: {node: '>=4.0.0'}

  commondir@1.0.1:
    resolution: {integrity: sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=, tarball: https://registry.npm.wps.cn/commondir/download/commondir-1.0.1.tgz}

  compressible@2.0.18:
    resolution: {integrity: sha1-r1PMprBw1MPAdQ+9dyhqbXzEb7o=, tarball: https://registry.npm.wps.cn/compressible/download/compressible-2.0.18.tgz}
    engines: {node: '>= 0.6'}

  compression@1.8.0:
    resolution: {integrity: sha1-CUIO/JbhGg9E86VY3lnjITZBgPc=, tarball: https://registry.npm.wps.cn/compression/download/compression-1.8.0.tgz}
    engines: {node: '>= 0.8.0'}

  concat-map@0.0.1:
    resolution: {integrity: sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=, tarball: https://registry.npm.wps.cn/concat-map/download/concat-map-0.0.1.tgz}

  confusing-browser-globals@1.0.11:
    resolution: {integrity: sha1-rkDptXzdORVAiigF69OlWFYI3IE=, tarball: https://registry.npm.wps.cn/confusing-browser-globals/download/confusing-browser-globals-1.0.11.tgz}

  connect-history-api-fallback@2.0.0:
    resolution: {integrity: sha1-ZHJkhFJRoNryW5fOh4NMrOD18cg=, tarball: https://registry.npm.wps.cn/connect-history-api-fallback/download/connect-history-api-fallback-2.0.0.tgz}
    engines: {node: '>=0.8'}

  content-disposition@0.5.4:
    resolution: {integrity: sha1-i4K076yCUSoCuwsdzsnSxejrW/4=, tarball: https://registry.npm.wps.cn/content-disposition/download/content-disposition-0.5.4.tgz}
    engines: {node: '>= 0.6'}

  content-type@1.0.5:
    resolution: {integrity: sha1-i3cxYmVtHRCGeEyPI6VM5tc9eRg=, tarball: https://registry.npm.wps.cn/content-type/download/content-type-1.0.5.tgz}
    engines: {node: '>= 0.6'}

  convert-source-map@1.9.0:
    resolution: {integrity: sha1-f6rmI1P7QhM2bQypg1jSLoNosF8=, tarball: https://registry.npm.wps.cn/convert-source-map/download/convert-source-map-1.9.0.tgz}

  convert-source-map@2.0.0:
    resolution: {integrity: sha1-S1YPZJ/E6RjdCrdc9JYei8iC2Co=, tarball: https://registry.npm.wps.cn/convert-source-map/download/convert-source-map-2.0.0.tgz}

  cookie-signature@1.0.6:
    resolution: {integrity: sha1-4wOogrNCzD7oylE6eZmXNNqzriw=, tarball: https://registry.npm.wps.cn/cookie-signature/download/cookie-signature-1.0.6.tgz}

  cookie@0.7.1:
    resolution: {integrity: sha1-L3PEIULV1c9xMQp0/ErmFnDl28k=, tarball: https://registry.npm.wps.cn/cookie/download/cookie-0.7.1.tgz}
    engines: {node: '>= 0.6'}

  copy-anything@2.0.6:
    resolution: {integrity: sha1-CSRU6pWEp7etVXMGKyqH9ZAPxIA=, tarball: https://registry.npm.wps.cn/copy-anything/download/copy-anything-2.0.6.tgz}

  core-js-compat@3.43.0:
    resolution: {integrity: sha1-BVWHNpxFh5XvMW9l4Kq7gI+xWEA=, tarball: https://registry.npm.wps.cn/core-js-compat/download/core-js-compat-3.43.0.tgz}

  core-js-pure@3.43.0:
    resolution: {integrity: sha1-TfnJScer3oOag5jRaoJ6doVrHww=, tarball: https://registry.npm.wps.cn/core-js-pure/download/core-js-pure-3.43.0.tgz}

  core-js@3.44.0:
    resolution: {integrity: sha1-20/U+geTPB1omMixEqERmpM26Vk=, tarball: https://registry.npm.wps.cn/core-js/download/core-js-3.44.0.tgz}

  core-util-is@1.0.3:
    resolution: {integrity: sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=, tarball: https://registry.npm.wps.cn/core-util-is/download/core-util-is-1.0.3.tgz}

  cosmiconfig-typescript-loader@1.0.9:
    resolution: {integrity: sha1-acUj9+jD2fJ/Vj0Cu+ra8vJyEtM=, tarball: https://registry.npm.wps.cn/cosmiconfig-typescript-loader/download/cosmiconfig-typescript-loader-1.0.9.tgz}
    engines: {node: '>=12', npm: '>=6'}
    peerDependencies:
      '@types/node': '*'
      cosmiconfig: '>=7'
      typescript: '>=3'

  cosmiconfig@6.0.0:
    resolution: {integrity: sha1-2k/uhTxS9rHmk19BwaL8UL1KmYI=, tarball: https://registry.npm.wps.cn/cosmiconfig/download/cosmiconfig-6.0.0.tgz}
    engines: {node: '>=8'}

  cosmiconfig@7.1.0:
    resolution: {integrity: sha1-FEO5r6WWtnAILqRsvY9qYrhGNfY=, tarball: https://registry.npm.wps.cn/cosmiconfig/download/cosmiconfig-7.1.0.tgz}
    engines: {node: '>=10'}

  craco-less@2.0.0:
    resolution: {integrity: sha1-ot8Ywy6X6/APYsPy6kzZcDX19kA=, tarball: https://registry.npm.wps.cn/craco-less/download/craco-less-2.0.0.tgz}
    peerDependencies:
      '@craco/craco': ^6.0.0
      react-scripts: ^5.0.0

  create-require@1.1.1:
    resolution: {integrity: sha1-wdfo8eX2z8n/ZfnNNS03NIdWwzM=, tarball: https://registry.npm.wps.cn/create-require/download/create-require-1.1.1.tgz}

  cross-spawn@7.0.6:
    resolution: {integrity: sha1-ilj+ePANzXDDcEUXWd+/rwPo7p8=, tarball: https://registry.npm.wps.cn/cross-spawn/download/cross-spawn-7.0.6.tgz}
    engines: {node: '>= 8'}

  crypto-random-string@2.0.0:
    resolution: {integrity: sha1-7yp6lm7BEIM4g2m6oC6+rSKbMNU=, tarball: https://registry.npm.wps.cn/crypto-random-string/download/crypto-random-string-2.0.0.tgz}
    engines: {node: '>=8'}

  css-blank-pseudo@3.0.3:
    resolution: {integrity: sha1-NlI7AcEqJdgS3zQ6MsMi0qIyRWE=, tarball: https://registry.npm.wps.cn/css-blank-pseudo/download/css-blank-pseudo-3.0.3.tgz}
    engines: {node: ^12 || ^14 || >=16}
    hasBin: true
    peerDependencies:
      postcss: ^8.4

  css-declaration-sorter@6.4.1:
    resolution: {integrity: sha1-KL6sfCC61/F3W+OnEp1+rkCaOnE=, tarball: https://registry.npm.wps.cn/css-declaration-sorter/download/css-declaration-sorter-6.4.1.tgz}
    engines: {node: ^10 || ^12 || >=14}
    peerDependencies:
      postcss: ^8.0.9

  css-has-pseudo@3.0.4:
    resolution: {integrity: sha1-V/a+kcokLVyQIO4+Ubu1uJ/Hr3M=, tarball: https://registry.npm.wps.cn/css-has-pseudo/download/css-has-pseudo-3.0.4.tgz}
    engines: {node: ^12 || ^14 || >=16}
    hasBin: true
    peerDependencies:
      postcss: ^8.4

  css-loader@6.11.0:
    resolution: {integrity: sha1-M7rjv2Nj0KfCz5AxyWx0T/VNhbo=, tarball: https://registry.npm.wps.cn/css-loader/download/css-loader-6.11.0.tgz}
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      '@rspack/core': 0.x || 1.x
      webpack: ^5.0.0
    peerDependenciesMeta:
      '@rspack/core':
        optional: true
      webpack:
        optional: true

  css-minimizer-webpack-plugin@3.4.1:
    resolution: {integrity: sha1-q3j3gc7ZGBmS/ntuTzQi52Qph48=, tarball: https://registry.npm.wps.cn/css-minimizer-webpack-plugin/download/css-minimizer-webpack-plugin-3.4.1.tgz}
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      '@parcel/css': '*'
      clean-css: '*'
      csso: '*'
      esbuild: '*'
      webpack: ^5.0.0
    peerDependenciesMeta:
      '@parcel/css':
        optional: true
      clean-css:
        optional: true
      csso:
        optional: true
      esbuild:
        optional: true

  css-prefers-color-scheme@6.0.3:
    resolution: {integrity: sha1-yooi5ZksEKW50xUVXnyu5iWQM0k=, tarball: https://registry.npm.wps.cn/css-prefers-color-scheme/download/css-prefers-color-scheme-6.0.3.tgz}
    engines: {node: ^12 || ^14 || >=16}
    hasBin: true
    peerDependencies:
      postcss: ^8.4

  css-select-base-adapter@0.1.1:
    resolution: {integrity: sha1-Oy/0lyzDYquIVhUHqVQIoUMhNdc=, tarball: https://registry.npm.wps.cn/css-select-base-adapter/download/css-select-base-adapter-0.1.1.tgz}

  css-select@2.1.0:
    resolution: {integrity: sha1-ajRlM1ZjWTSoG6ymjQJVQyEF2+8=, tarball: https://registry.npm.wps.cn/css-select/download/css-select-2.1.0.tgz}

  css-select@4.3.0:
    resolution: {integrity: sha1-23EpsoRmYv2GKM/ElquytZ5BUps=, tarball: https://registry.npm.wps.cn/css-select/download/css-select-4.3.0.tgz}

  css-tree@1.0.0-alpha.37:
    resolution: {integrity: sha1-mL69YsTB2flg7DQM+fdSLjBwmiI=, tarball: https://registry.npm.wps.cn/css-tree/download/css-tree-1.0.0-alpha.37.tgz}
    engines: {node: '>=8.0.0'}

  css-tree@1.1.3:
    resolution: {integrity: sha1-60hw+2/XcHMn7JXC/yqwm16NuR0=, tarball: https://registry.npm.wps.cn/css-tree/download/css-tree-1.1.3.tgz}
    engines: {node: '>=8.0.0'}

  css-what@3.4.2:
    resolution: {integrity: sha1-6nAm/LAXd+295SEk4h8yfnrpUOQ=, tarball: https://registry.npm.wps.cn/css-what/download/css-what-3.4.2.tgz}
    engines: {node: '>= 6'}

  css-what@6.2.2:
    resolution: {integrity: sha1-zcyPm2l3cZ/fvR3nrsJKv3Vrneo=, tarball: https://registry.npm.wps.cn/css-what/download/css-what-6.2.2.tgz}
    engines: {node: '>= 6'}

  css.escape@1.5.1:
    resolution: {integrity: sha1-QuJ9T6BK4y+TGktNQZH6nN3ul8s=, tarball: https://registry.npm.wps.cn/css.escape/download/css.escape-1.5.1.tgz}

  cssdb@7.11.2:
    resolution: {integrity: sha1-EnovW5Ru5lM2Glr1Mz6oWjnfWuU=, tarball: https://registry.npm.wps.cn/cssdb/download/cssdb-7.11.2.tgz}

  cssesc@3.0.0:
    resolution: {integrity: sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=, tarball: https://registry.npm.wps.cn/cssesc/download/cssesc-3.0.0.tgz}
    engines: {node: '>=4'}
    hasBin: true

  cssnano-preset-default@5.2.14:
    resolution: {integrity: sha1-MJ3vT3t+FtcaskOAUgkzMNmrRdg=, tarball: https://registry.npm.wps.cn/cssnano-preset-default/download/cssnano-preset-default-5.2.14.tgz}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  cssnano-utils@3.1.0:
    resolution: {integrity: sha1-lWhNCMkVEe38cNJjYzjKN+86aGE=, tarball: https://registry.npm.wps.cn/cssnano-utils/download/cssnano-utils-3.1.0.tgz}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  cssnano@5.1.15:
    resolution: {integrity: sha1-3tZrVIDVEn/LRNrBLqWpg3VRNr8=, tarball: https://registry.npm.wps.cn/cssnano/download/cssnano-5.1.15.tgz}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  csso@4.2.0:
    resolution: {integrity: sha1-6jpWE0bo3J9UbW/r7dUBh884lSk=, tarball: https://registry.npm.wps.cn/csso/download/csso-4.2.0.tgz}
    engines: {node: '>=8.0.0'}

  cssom@0.3.8:
    resolution: {integrity: sha1-nxJ29bK0Y/IRTT8sdSUK+MGjb0o=, tarball: https://registry.npm.wps.cn/cssom/download/cssom-0.3.8.tgz}

  cssom@0.4.4:
    resolution: {integrity: sha1-WmbPk9LQtmHYC/akT7ZfXC5OChA=, tarball: https://registry.npm.wps.cn/cssom/download/cssom-0.4.4.tgz}

  cssstyle@2.3.0:
    resolution: {integrity: sha1-/2ZaDdvcMYZLCWR/NBY0Q9kLCFI=, tarball: https://registry.npm.wps.cn/cssstyle/download/cssstyle-2.3.0.tgz}
    engines: {node: '>=8'}

  csstype@3.1.3:
    resolution: {integrity: sha1-2A/ylNEU+w5qxQD7+FtgE31+/4E=, tarball: https://registry.npm.wps.cn/csstype/download/csstype-3.1.3.tgz}

  damerau-levenshtein@1.0.8:
    resolution: {integrity: sha1-tD0obMvTa8Wy9+1ByvLQq6H4puc=, tarball: https://registry.npm.wps.cn/damerau-levenshtein/download/damerau-levenshtein-1.0.8.tgz}

  data-urls@2.0.0:
    resolution: {integrity: sha1-FWSFpyljqXD11YIar2Qr7yvy25s=, tarball: https://registry.npm.wps.cn/data-urls/download/data-urls-2.0.0.tgz}
    engines: {node: '>=10'}

  data-view-buffer@1.0.2:
    resolution: {integrity: sha1-IRoDupXsr3eYqMcZjXlTYhH4hXA=, tarball: https://registry.npm.wps.cn/data-view-buffer/download/data-view-buffer-1.0.2.tgz}
    engines: {node: '>= 0.4'}

  data-view-byte-length@1.0.2:
    resolution: {integrity: sha1-noD3ylJFPOPpPSWjUxh2fqdwRzU=, tarball: https://registry.npm.wps.cn/data-view-byte-length/download/data-view-byte-length-1.0.2.tgz}
    engines: {node: '>= 0.4'}

  data-view-byte-offset@1.0.1:
    resolution: {integrity: sha1-BoMH+bcat2274QKROJ4CCFZgYZE=, tarball: https://registry.npm.wps.cn/data-view-byte-offset/download/data-view-byte-offset-1.0.1.tgz}
    engines: {node: '>= 0.4'}

  debug@2.6.9:
    resolution: {integrity: sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=, tarball: https://registry.npm.wps.cn/debug/download/debug-2.6.9.tgz}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@3.2.7:
    resolution: {integrity: sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=, tarball: https://registry.npm.wps.cn/debug/download/debug-3.2.7.tgz}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@4.4.1:
    resolution: {integrity: sha1-5ai8bLxMbNPmQwiwaTo9T6VQGJs=, tarball: https://registry.npm.wps.cn/debug/download/debug-4.4.1.tgz}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  decimal.js@10.5.0:
    resolution: {integrity: sha1-DzccfPbEiYzgr7CYNttzzYIBDyI=, tarball: https://registry.npm.wps.cn/decimal.js/download/decimal.js-10.5.0.tgz}

  dedent@0.7.0:
    resolution: {integrity: sha1-JJXduvbrh0q7Dhvp3yLS5aVEMmw=, tarball: https://registry.npm.wps.cn/dedent/download/dedent-0.7.0.tgz}

  deep-equal@2.2.3:
    resolution: {integrity: sha1-r4na+yOjlsfaPoYqvAvifPUdVuE=, tarball: https://registry.npm.wps.cn/deep-equal/download/deep-equal-2.2.3.tgz}
    engines: {node: '>= 0.4'}

  deep-is@0.1.4:
    resolution: {integrity: sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=, tarball: https://registry.npm.wps.cn/deep-is/download/deep-is-0.1.4.tgz}

  deepmerge@4.3.1:
    resolution: {integrity: sha1-RLXyFHzTsA1LVhN2hZZvJv0l3Uo=, tarball: https://registry.npm.wps.cn/deepmerge/download/deepmerge-4.3.1.tgz}
    engines: {node: '>=0.10.0'}

  default-gateway@6.0.3:
    resolution: {integrity: sha1-gZSUyIgFO9t0PtvzQ9bN9/KUOnE=, tarball: https://registry.npm.wps.cn/default-gateway/download/default-gateway-6.0.3.tgz}
    engines: {node: '>= 10'}

  define-data-property@1.1.4:
    resolution: {integrity: sha1-iU3BQbt9MGCuQ2b2oBB+aPvkjF4=, tarball: https://registry.npm.wps.cn/define-data-property/download/define-data-property-1.1.4.tgz}
    engines: {node: '>= 0.4'}

  define-lazy-prop@2.0.0:
    resolution: {integrity: sha1-P3rkIRKbyqrJvHSQXJigAJ7J7n8=, tarball: https://registry.npm.wps.cn/define-lazy-prop/download/define-lazy-prop-2.0.0.tgz}
    engines: {node: '>=8'}

  define-properties@1.2.1:
    resolution: {integrity: sha1-EHgcxhbrlRqAoDS6/Kpzd/avK2w=, tarball: https://registry.npm.wps.cn/define-properties/download/define-properties-1.2.1.tgz}
    engines: {node: '>= 0.4'}

  delayed-stream@1.0.0:
    resolution: {integrity: sha1-3zrhmayt+31ECqrgsp4icrJOxhk=, tarball: https://registry.npm.wps.cn/delayed-stream/download/delayed-stream-1.0.0.tgz}
    engines: {node: '>=0.4.0'}

  depd@1.1.2:
    resolution: {integrity: sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=, tarball: https://registry.npm.wps.cn/depd/download/depd-1.1.2.tgz}
    engines: {node: '>= 0.6'}

  depd@2.0.0:
    resolution: {integrity: sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8=, tarball: https://registry.npm.wps.cn/depd/download/depd-2.0.0.tgz}
    engines: {node: '>= 0.8'}

  destroy@1.2.0:
    resolution: {integrity: sha1-SANzVQmti+VSk0xn32FPlOZvoBU=, tarball: https://registry.npm.wps.cn/destroy/download/destroy-1.2.0.tgz}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}

  detect-newline@3.1.0:
    resolution: {integrity: sha1-V29d/GOuGhkv8ZLYrTr2MImRtlE=, tarball: https://registry.npm.wps.cn/detect-newline/download/detect-newline-3.1.0.tgz}
    engines: {node: '>=8'}

  detect-node@2.1.0:
    resolution: {integrity: sha1-yccHdaScPQO8LAbZpzvlUPl4+LE=, tarball: https://registry.npm.wps.cn/detect-node/download/detect-node-2.1.0.tgz}

  detect-port-alt@1.1.6:
    resolution: {integrity: sha1-JHB96r6TLUo89iEwICfCsmZWgnU=, tarball: https://registry.npm.wps.cn/detect-port-alt/download/detect-port-alt-1.1.6.tgz}
    engines: {node: '>= 4.2.1'}
    hasBin: true

  didyoumean@1.2.2:
    resolution: {integrity: sha1-mJNG/+noObRVXs9WZu3qDT6K0Dc=, tarball: https://registry.npm.wps.cn/didyoumean/download/didyoumean-1.2.2.tgz}

  diff-sequences@27.5.1:
    resolution: {integrity: sha1-6uzA0yf9aMjZZyoeZKuNzLLvUyc=, tarball: https://registry.npm.wps.cn/diff-sequences/download/diff-sequences-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  diff@4.0.2:
    resolution: {integrity: sha1-YPOuy4nV+uUgwRqhnvwruYKq3n0=, tarball: https://registry.npm.wps.cn/diff/download/diff-4.0.2.tgz}
    engines: {node: '>=0.3.1'}

  dir-glob@3.0.1:
    resolution: {integrity: sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=, tarball: https://registry.npm.wps.cn/dir-glob/download/dir-glob-3.0.1.tgz}
    engines: {node: '>=8'}

  dlv@1.1.3:
    resolution: {integrity: sha1-XBmKihFFNZbnUUlNSYdLx3MvLnk=, tarball: https://registry.npm.wps.cn/dlv/download/dlv-1.1.3.tgz}

  dns-packet@5.6.1:
    resolution: {integrity: sha1-roiK1CWp0UeKBnQlarhm3hASzy8=, tarball: https://registry.npm.wps.cn/dns-packet/download/dns-packet-5.6.1.tgz}
    engines: {node: '>=6'}

  doctrine@2.1.0:
    resolution: {integrity: sha1-XNAfwQFiG0LEzX9dGmYkNxbT850=, tarball: https://registry.npm.wps.cn/doctrine/download/doctrine-2.1.0.tgz}
    engines: {node: '>=0.10.0'}

  doctrine@3.0.0:
    resolution: {integrity: sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=, tarball: https://registry.npm.wps.cn/doctrine/download/doctrine-3.0.0.tgz}
    engines: {node: '>=6.0.0'}

  dom-accessibility-api@0.5.16:
    resolution: {integrity: sha1-WnQp5gZus2ZNkR4z+w5F3o6whFM=, tarball: https://registry.npm.wps.cn/dom-accessibility-api/download/dom-accessibility-api-0.5.16.tgz}

  dom-converter@0.2.0:
    resolution: {integrity: sha1-ZyGp2u4uKTaClVtq/kFncWJ7t2g=, tarball: https://registry.npm.wps.cn/dom-converter/download/dom-converter-0.2.0.tgz}

  dom-serializer@0.2.2:
    resolution: {integrity: sha1-GvuB9TNxcXXUeGVd68XjMtn5u1E=, tarball: https://registry.npm.wps.cn/dom-serializer/download/dom-serializer-0.2.2.tgz}

  dom-serializer@1.4.1:
    resolution: {integrity: sha1-3l1Bsa6ikCFdxFptrorc8dMuLTA=, tarball: https://registry.npm.wps.cn/dom-serializer/download/dom-serializer-1.4.1.tgz}

  domelementtype@1.3.1:
    resolution: {integrity: sha1-0EjESzew0Qp/Kj1f7j9DM9eQSB8=, tarball: https://registry.npm.wps.cn/domelementtype/download/domelementtype-1.3.1.tgz}

  domelementtype@2.3.0:
    resolution: {integrity: sha1-XEXo6GmVJiYzHXqrMm0B2vZdWJ0=, tarball: https://registry.npm.wps.cn/domelementtype/download/domelementtype-2.3.0.tgz}

  domexception@2.0.1:
    resolution: {integrity: sha1-+0Su+6eT4VdLCvau0oAdBXUp8wQ=, tarball: https://registry.npm.wps.cn/domexception/download/domexception-2.0.1.tgz}
    engines: {node: '>=8'}
    deprecated: Use your platform's native DOMException instead

  domhandler@4.3.1:
    resolution: {integrity: sha1-jXkgM0FvWdaLwDpap7AYwcqJJ5w=, tarball: https://registry.npm.wps.cn/domhandler/download/domhandler-4.3.1.tgz}
    engines: {node: '>= 4'}

  domutils@1.7.0:
    resolution: {integrity: sha1-Vuo0HoNOBuZ0ivehyyXaZ+qfjCo=, tarball: https://registry.npm.wps.cn/domutils/download/domutils-1.7.0.tgz}

  domutils@2.8.0:
    resolution: {integrity: sha1-RDfe9dtuLR9dbuhZvZXKfQIEgTU=, tarball: https://registry.npm.wps.cn/domutils/download/domutils-2.8.0.tgz}

  dot-case@3.0.4:
    resolution: {integrity: sha1-mytnDQCkMWZ6inW6Kc0bmICc51E=, tarball: https://registry.npm.wps.cn/dot-case/download/dot-case-3.0.4.tgz}

  dotenv-expand@5.1.0:
    resolution: {integrity: sha1-P7rwIL/XlIhAcuomsel5HUWmKfA=, tarball: https://registry.npm.wps.cn/dotenv-expand/download/dotenv-expand-5.1.0.tgz}

  dotenv@10.0.0:
    resolution: {integrity: sha1-PUInuPuV+BCWzdK2ZlP7LHCFuoE=, tarball: https://registry.npm.wps.cn/dotenv/download/dotenv-10.0.0.tgz}
    engines: {node: '>=10'}

  dunder-proto@1.0.1:
    resolution: {integrity: sha1-165mfh3INIL4tw/Q9u78UNow9Yo=, tarball: https://registry.npm.wps.cn/dunder-proto/download/dunder-proto-1.0.1.tgz}
    engines: {node: '>= 0.4'}

  duplexer@0.1.2:
    resolution: {integrity: sha1-Or5DrvODX4rgd9E23c4PJ2sEAOY=, tarball: https://registry.npm.wps.cn/duplexer/download/duplexer-0.1.2.tgz}

  eastasianwidth@0.2.0:
    resolution: {integrity: sha1-aWzi7Aqg5uqTo5f/zySqeEDIJ8s=, tarball: https://registry.npm.wps.cn/eastasianwidth/download/eastasianwidth-0.2.0.tgz}

  ee-first@1.1.1:
    resolution: {integrity: sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=, tarball: https://registry.npm.wps.cn/ee-first/download/ee-first-1.1.1.tgz}

  ejs@3.1.10:
    resolution: {integrity: sha1-aauDWLFOiW+AzDnmIIe4hQDDrDs=, tarball: https://registry.npm.wps.cn/ejs/download/ejs-3.1.10.tgz}
    engines: {node: '>=0.10.0'}
    hasBin: true

  electron-to-chromium@1.5.180:
    resolution: {integrity: sha1-Pk9udJTWNx4BSvF239/UPIpLVt8=, tarball: https://registry.npm.wps.cn/electron-to-chromium/download/electron-to-chromium-1.5.180.tgz}

  emittery@0.10.2:
    resolution: {integrity: sha1-kC7siu24xBk4xG6ThenbfgMYKTM=, tarball: https://registry.npm.wps.cn/emittery/download/emittery-0.10.2.tgz}
    engines: {node: '>=12'}

  emittery@0.8.1:
    resolution: {integrity: sha1-uyPMhtA7MKp1p/c0gZ3uLhunCGA=, tarball: https://registry.npm.wps.cn/emittery/download/emittery-0.8.1.tgz}
    engines: {node: '>=10'}

  emoji-regex@8.0.0:
    resolution: {integrity: sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=, tarball: https://registry.npm.wps.cn/emoji-regex/download/emoji-regex-8.0.0.tgz}

  emoji-regex@9.2.2:
    resolution: {integrity: sha1-hAyIA7DYBH9P8M+WMXazLU7z7XI=, tarball: https://registry.npm.wps.cn/emoji-regex/download/emoji-regex-9.2.2.tgz}

  emojis-list@3.0.0:
    resolution: {integrity: sha1-VXBmIEatKeLpFucariYKvf9Pang=, tarball: https://registry.npm.wps.cn/emojis-list/download/emojis-list-3.0.0.tgz}
    engines: {node: '>= 4'}

  encodeurl@1.0.2:
    resolution: {integrity: sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=, tarball: https://registry.npm.wps.cn/encodeurl/download/encodeurl-1.0.2.tgz}
    engines: {node: '>= 0.8'}

  encodeurl@2.0.0:
    resolution: {integrity: sha1-e46omAd9fkCdOsRUdOo46vCFelg=, tarball: https://registry.npm.wps.cn/encodeurl/download/encodeurl-2.0.0.tgz}
    engines: {node: '>= 0.8'}

  enhanced-resolve@5.18.2:
    resolution: {integrity: sha1-eQPFsy/9SyFD7rS5JHK9aO/9VGQ=, tarball: https://registry.npm.wps.cn/enhanced-resolve/download/enhanced-resolve-5.18.2.tgz}
    engines: {node: '>=10.13.0'}

  entities@2.2.0:
    resolution: {integrity: sha1-CY3JDruD2N/6CJ1VJWs1HTTE2lU=, tarball: https://registry.npm.wps.cn/entities/download/entities-2.2.0.tgz}

  errno@0.1.8:
    resolution: {integrity: sha1-i7Ppx9Rjvkl2/4iPdrSAnrwugR8=, tarball: https://registry.npm.wps.cn/errno/download/errno-0.1.8.tgz}
    hasBin: true

  error-ex@1.3.2:
    resolution: {integrity: sha1-tKxAZIEH/c3PriQvQovqihTU8b8=, tarball: https://registry.npm.wps.cn/error-ex/download/error-ex-1.3.2.tgz}

  error-stack-parser@2.1.4:
    resolution: {integrity: sha1-IpywHNv6hEQL+pGHYoW5RoAYgoY=, tarball: https://registry.npm.wps.cn/error-stack-parser/download/error-stack-parser-2.1.4.tgz}

  es-abstract@1.24.0:
    resolution: {integrity: sha1-xEcy0r6wrMHtYN+ECGnjEG568yg=, tarball: https://registry.npm.wps.cn/es-abstract/download/es-abstract-1.24.0.tgz}
    engines: {node: '>= 0.4'}

  es-array-method-boxes-properly@1.0.0:
    resolution: {integrity: sha1-hz8+hEGN5O4Zxb51KZCy5EcY0J4=, tarball: https://registry.npm.wps.cn/es-array-method-boxes-properly/download/es-array-method-boxes-properly-1.0.0.tgz}

  es-define-property@1.0.1:
    resolution: {integrity: sha1-mD6y+aZyTpMD9hrd8BHHLgngsPo=, tarball: https://registry.npm.wps.cn/es-define-property/download/es-define-property-1.0.1.tgz}
    engines: {node: '>= 0.4'}

  es-errors@1.3.0:
    resolution: {integrity: sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=, tarball: https://registry.npm.wps.cn/es-errors/download/es-errors-1.3.0.tgz}
    engines: {node: '>= 0.4'}

  es-get-iterator@1.1.3:
    resolution: {integrity: sha1-Pvh1I8XUZNQQhLLDycIU8RmXY9Y=, tarball: https://registry.npm.wps.cn/es-get-iterator/download/es-get-iterator-1.1.3.tgz}

  es-iterator-helpers@1.2.1:
    resolution: {integrity: sha1-0d0PWBKQVMCtki5qmh5l7vQ1/nU=, tarball: https://registry.npm.wps.cn/es-iterator-helpers/download/es-iterator-helpers-1.2.1.tgz}
    engines: {node: '>= 0.4'}

  es-module-lexer@1.7.0:
    resolution: {integrity: sha1-kVlgFWGICoXyc0VgqQmbLDHlNyo=, tarball: https://registry.npm.wps.cn/es-module-lexer/download/es-module-lexer-1.7.0.tgz}

  es-object-atoms@1.1.1:
    resolution: {integrity: sha1-HE8sSDcydZfOadLKGQp/3RcjOME=, tarball: https://registry.npm.wps.cn/es-object-atoms/download/es-object-atoms-1.1.1.tgz}
    engines: {node: '>= 0.4'}

  es-set-tostringtag@2.1.0:
    resolution: {integrity: sha1-8x274MGDsAptJutjJcgQwP0YvU0=, tarball: https://registry.npm.wps.cn/es-set-tostringtag/download/es-set-tostringtag-2.1.0.tgz}
    engines: {node: '>= 0.4'}

  es-shim-unscopables@1.1.0:
    resolution: {integrity: sha1-Q43zVSDaxdEF85Q9knVJ6jsA9LU=, tarball: https://registry.npm.wps.cn/es-shim-unscopables/download/es-shim-unscopables-1.1.0.tgz}
    engines: {node: '>= 0.4'}

  es-to-primitive@1.3.0:
    resolution: {integrity: sha1-lsicgsxJ/YeUokg1uj4f+H8hThg=, tarball: https://registry.npm.wps.cn/es-to-primitive/download/es-to-primitive-1.3.0.tgz}
    engines: {node: '>= 0.4'}

  escalade@3.2.0:
    resolution: {integrity: sha1-ARo/aYVroYnf+n3I/M6Z0qh5A+U=, tarball: https://registry.npm.wps.cn/escalade/download/escalade-3.2.0.tgz}
    engines: {node: '>=6'}

  escape-html@1.0.3:
    resolution: {integrity: sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=, tarball: https://registry.npm.wps.cn/escape-html/download/escape-html-1.0.3.tgz}

  escape-string-regexp@1.0.5:
    resolution: {integrity: sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=, tarball: https://registry.npm.wps.cn/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz}
    engines: {node: '>=0.8.0'}

  escape-string-regexp@2.0.0:
    resolution: {integrity: sha1-owME6Z2qMuI7L9IPUbq9B8/8o0Q=, tarball: https://registry.npm.wps.cn/escape-string-regexp/download/escape-string-regexp-2.0.0.tgz}
    engines: {node: '>=8'}

  escape-string-regexp@4.0.0:
    resolution: {integrity: sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=, tarball: https://registry.npm.wps.cn/escape-string-regexp/download/escape-string-regexp-4.0.0.tgz}
    engines: {node: '>=10'}

  escodegen@1.14.3:
    resolution: {integrity: sha1-TnuB+6YVgdyXWC7XjKt/Do1j9QM=, tarball: https://registry.npm.wps.cn/escodegen/download/escodegen-1.14.3.tgz}
    engines: {node: '>=4.0'}
    hasBin: true

  escodegen@2.1.0:
    resolution: {integrity: sha1-upO7t6Q5htKdYEH5n1Ji2nc+Lhc=, tarball: https://registry.npm.wps.cn/escodegen/download/escodegen-2.1.0.tgz}
    engines: {node: '>=6.0'}
    hasBin: true

  eslint-config-react-app@7.0.1:
    resolution: {integrity: sha1-c7o5KZeAAcXIYnTAF+pX61+mRLQ=, tarball: https://registry.npm.wps.cn/eslint-config-react-app/download/eslint-config-react-app-7.0.1.tgz}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      eslint: ^8.0.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  eslint-import-resolver-node@0.3.9:
    resolution: {integrity: sha1-1OqsUrii58PNGQPrAPfgUzVhGKw=, tarball: https://registry.npm.wps.cn/eslint-import-resolver-node/download/eslint-import-resolver-node-0.3.9.tgz}

  eslint-module-utils@2.12.1:
    resolution: {integrity: sha1-920yIL+4PAV2UTWSlatYVOqtdf8=, tarball: https://registry.npm.wps.cn/eslint-module-utils/download/eslint-module-utils-2.12.1.tgz}
    engines: {node: '>=4'}
    peerDependencies:
      '@typescript-eslint/parser': '*'
      eslint: '*'
      eslint-import-resolver-node: '*'
      eslint-import-resolver-typescript: '*'
      eslint-import-resolver-webpack: '*'
    peerDependenciesMeta:
      '@typescript-eslint/parser':
        optional: true
      eslint:
        optional: true
      eslint-import-resolver-node:
        optional: true
      eslint-import-resolver-typescript:
        optional: true
      eslint-import-resolver-webpack:
        optional: true

  eslint-plugin-flowtype@8.0.3:
    resolution: {integrity: sha1-4VV+NxGPJHNKoxIudTagONNKSRI=, tarball: https://registry.npm.wps.cn/eslint-plugin-flowtype/download/eslint-plugin-flowtype-8.0.3.tgz}
    engines: {node: '>=12.0.0'}
    peerDependencies:
      '@babel/plugin-syntax-flow': ^7.14.5
      '@babel/plugin-transform-react-jsx': ^7.14.9
      eslint: ^8.1.0

  eslint-plugin-import@2.32.0:
    resolution: {integrity: sha1-YCtV+qbkyuql6XDBmLXACjdwiYA=, tarball: https://registry.npm.wps.cn/eslint-plugin-import/download/eslint-plugin-import-2.32.0.tgz}
    engines: {node: '>=4'}
    peerDependencies:
      '@typescript-eslint/parser': '*'
      eslint: ^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8 || ^9
    peerDependenciesMeta:
      '@typescript-eslint/parser':
        optional: true

  eslint-plugin-jest@25.7.0:
    resolution: {integrity: sha1-/0rJdSC1OpYYe62cmBTn0A3gmmo=, tarball: https://registry.npm.wps.cn/eslint-plugin-jest/download/eslint-plugin-jest-25.7.0.tgz}
    engines: {node: ^12.13.0 || ^14.15.0 || >=16.0.0}
    peerDependencies:
      '@typescript-eslint/eslint-plugin': ^4.0.0 || ^5.0.0
      eslint: ^6.0.0 || ^7.0.0 || ^8.0.0
      jest: '*'
    peerDependenciesMeta:
      '@typescript-eslint/eslint-plugin':
        optional: true
      jest:
        optional: true

  eslint-plugin-jsx-a11y@6.10.2:
    resolution: {integrity: sha1-0oErsjvxq0Zl8XGOpELoNy5jhIM=, tarball: https://registry.npm.wps.cn/eslint-plugin-jsx-a11y/download/eslint-plugin-jsx-a11y-6.10.2.tgz}
    engines: {node: '>=4.0'}
    peerDependencies:
      eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9

  eslint-plugin-react-hooks@4.6.2:
    resolution: {integrity: sha1-yCnrBsDm9ISz+7hal+V3hPMoxZY=, tarball: https://registry.npm.wps.cn/eslint-plugin-react-hooks/download/eslint-plugin-react-hooks-4.6.2.tgz}
    engines: {node: '>=10'}
    peerDependencies:
      eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0

  eslint-plugin-react@7.37.5:
    resolution: {integrity: sha1-KXVRFHK92hsnKzTXeTNcmw6HcGU=, tarball: https://registry.npm.wps.cn/eslint-plugin-react/download/eslint-plugin-react-7.37.5.tgz}
    engines: {node: '>=4'}
    peerDependencies:
      eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7

  eslint-plugin-testing-library@5.11.1:
    resolution: {integrity: sha1-W0bNrpbUp4kYcRwLR5L5AIjmLSA=, tarball: https://registry.npm.wps.cn/eslint-plugin-testing-library/download/eslint-plugin-testing-library-5.11.1.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0, npm: '>=6'}
    peerDependencies:
      eslint: ^7.5.0 || ^8.0.0

  eslint-scope@5.1.1:
    resolution: {integrity: sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw=, tarball: https://registry.npm.wps.cn/eslint-scope/download/eslint-scope-5.1.1.tgz}
    engines: {node: '>=8.0.0'}

  eslint-scope@7.2.2:
    resolution: {integrity: sha1-3rT5JWM5DzIAaJSvYqItuhxGQj8=, tarball: https://registry.npm.wps.cn/eslint-scope/download/eslint-scope-7.2.2.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  eslint-visitor-keys@2.1.0:
    resolution: {integrity: sha1-9lMoJZMFknOSyTjtROsKXJsr0wM=, tarball: https://registry.npm.wps.cn/eslint-visitor-keys/download/eslint-visitor-keys-2.1.0.tgz}
    engines: {node: '>=10'}

  eslint-visitor-keys@3.4.3:
    resolution: {integrity: sha1-DNcv6FUOPC6uFWqWpN3c0cisWAA=, tarball: https://registry.npm.wps.cn/eslint-visitor-keys/download/eslint-visitor-keys-3.4.3.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  eslint-webpack-plugin@3.2.0:
    resolution: {integrity: sha1-GXjNue3EYeSwGVog2pUM9XmINHw=, tarball: https://registry.npm.wps.cn/eslint-webpack-plugin/download/eslint-webpack-plugin-3.2.0.tgz}
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      eslint: ^7.0.0 || ^8.0.0
      webpack: ^5.0.0

  eslint@8.57.1:
    resolution: {integrity: sha1-ffEJZUq6fju+XI6uUzxeRh08bKk=, tarball: https://registry.npm.wps.cn/eslint/download/eslint-8.57.1.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    deprecated: This version is no longer supported. Please see https://eslint.org/version-support for other options.
    hasBin: true

  espree@9.6.1:
    resolution: {integrity: sha1-oqF7jkNGkKVDLy+AGM5x0zGkjG8=, tarball: https://registry.npm.wps.cn/espree/download/espree-9.6.1.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  esprima@1.2.2:
    resolution: {integrity: sha1-dqD9Zvz+FU/SkmZ9wmQBl1CxZXs=, tarball: https://registry.npm.wps.cn/esprima/download/esprima-1.2.2.tgz}
    engines: {node: '>=0.4.0'}
    hasBin: true

  esprima@4.0.1:
    resolution: {integrity: sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=, tarball: https://registry.npm.wps.cn/esprima/download/esprima-4.0.1.tgz}
    engines: {node: '>=4'}
    hasBin: true

  esquery@1.6.0:
    resolution: {integrity: sha1-kUGSNPgE2FKoLc7sPhbNwiz52uc=, tarball: https://registry.npm.wps.cn/esquery/download/esquery-1.6.0.tgz}
    engines: {node: '>=0.10'}

  esrecurse@4.3.0:
    resolution: {integrity: sha1-eteWTWeauyi+5yzsY3WLHF0smSE=, tarball: https://registry.npm.wps.cn/esrecurse/download/esrecurse-4.3.0.tgz}
    engines: {node: '>=4.0'}

  estraverse@4.3.0:
    resolution: {integrity: sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=, tarball: https://registry.npm.wps.cn/estraverse/download/estraverse-4.3.0.tgz}
    engines: {node: '>=4.0'}

  estraverse@5.3.0:
    resolution: {integrity: sha1-LupSkHAvJquP5TcDcP+GyWXSESM=, tarball: https://registry.npm.wps.cn/estraverse/download/estraverse-5.3.0.tgz}
    engines: {node: '>=4.0'}

  estree-walker@1.0.1:
    resolution: {integrity: sha1-MbxdYSyWtwQQa0d+bdXYqhOMtwA=, tarball: https://registry.npm.wps.cn/estree-walker/download/estree-walker-1.0.1.tgz}

  esutils@2.0.3:
    resolution: {integrity: sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=, tarball: https://registry.npm.wps.cn/esutils/download/esutils-2.0.3.tgz}
    engines: {node: '>=0.10.0'}

  etag@1.8.1:
    resolution: {integrity: sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=, tarball: https://registry.npm.wps.cn/etag/download/etag-1.8.1.tgz}
    engines: {node: '>= 0.6'}

  eventemitter3@4.0.7:
    resolution: {integrity: sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8=, tarball: https://registry.npm.wps.cn/eventemitter3/download/eventemitter3-4.0.7.tgz}

  events@3.3.0:
    resolution: {integrity: sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA=, tarball: https://registry.npm.wps.cn/events/download/events-3.3.0.tgz}
    engines: {node: '>=0.8.x'}

  execa@5.1.1:
    resolution: {integrity: sha1-+ArZy/Qpj3vR1MlVXCHpN0HEEd0=, tarball: https://registry.npm.wps.cn/execa/download/execa-5.1.1.tgz}
    engines: {node: '>=10'}

  exit@0.1.2:
    resolution: {integrity: sha1-BjJjj42HfMghB9MKD/8aF8uhzQw=, tarball: https://registry.npm.wps.cn/exit/download/exit-0.1.2.tgz}
    engines: {node: '>= 0.8.0'}

  expect@27.5.1:
    resolution: {integrity: sha1-g85Z8eW99fnSuUth0gUNtI8/73Q=, tarball: https://registry.npm.wps.cn/expect/download/expect-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  express@4.21.2:
    resolution: {integrity: sha1-zyUOSDYhdOrWzqSlZqvvAWLB7DI=, tarball: https://registry.npm.wps.cn/express/download/express-4.21.2.tgz}
    engines: {node: '>= 0.10.0'}

  fast-deep-equal@3.1.3:
    resolution: {integrity: sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=, tarball: https://registry.npm.wps.cn/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz}

  fast-glob@3.3.3:
    resolution: {integrity: sha1-0G1YXOjbqQoWsFBcVDw8z7OuuBg=, tarball: https://registry.npm.wps.cn/fast-glob/download/fast-glob-3.3.3.tgz}
    engines: {node: '>=8.6.0'}

  fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=, tarball: https://registry.npm.wps.cn/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz}

  fast-levenshtein@2.0.6:
    resolution: {integrity: sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=, tarball: https://registry.npm.wps.cn/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz}

  fast-uri@3.0.6:
    resolution: {integrity: sha1-iPEwt3z66iN41Wv5cN6iElemh0g=, tarball: https://registry.npm.wps.cn/fast-uri/download/fast-uri-3.0.6.tgz}

  fastq@1.19.1:
    resolution: {integrity: sha1-1Q6rqAPIhGqIPBZJKCHrzSzaVfU=, tarball: https://registry.npm.wps.cn/fastq/download/fastq-1.19.1.tgz}

  faye-websocket@0.11.4:
    resolution: {integrity: sha1-fw2Sdc/dhqHJY9yLZfzEUe3Lsdo=, tarball: https://registry.npm.wps.cn/faye-websocket/download/faye-websocket-0.11.4.tgz}
    engines: {node: '>=0.8.0'}

  fb-watchman@2.0.2:
    resolution: {integrity: sha1-6VJO5rXHfp5QAa8PhfOtu4YjJVw=, tarball: https://registry.npm.wps.cn/fb-watchman/download/fb-watchman-2.0.2.tgz}

  file-entry-cache@6.0.1:
    resolution: {integrity: sha1-IRst2WWcsDlLBz5zI6w8kz1SICc=, tarball: https://registry.npm.wps.cn/file-entry-cache/download/file-entry-cache-6.0.1.tgz}
    engines: {node: ^10.12.0 || >=12.0.0}

  file-loader@6.2.0:
    resolution: {integrity: sha1-uu98+OGEDfMl5DkLRISHlIDuvk0=, tarball: https://registry.npm.wps.cn/file-loader/download/file-loader-6.2.0.tgz}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0

  filelist@1.0.4:
    resolution: {integrity: sha1-94l4oelEd1/55i50RCTyFeWDUrU=, tarball: https://registry.npm.wps.cn/filelist/download/filelist-1.0.4.tgz}

  filesize@8.0.7:
    resolution: {integrity: sha1-aV5w2A9ORwEsEy1XoFnoDGtYC9g=, tarball: https://registry.npm.wps.cn/filesize/download/filesize-8.0.7.tgz}
    engines: {node: '>= 0.4.0'}

  fill-range@7.1.1:
    resolution: {integrity: sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=, tarball: https://registry.npm.wps.cn/fill-range/download/fill-range-7.1.1.tgz}
    engines: {node: '>=8'}

  finalhandler@1.3.1:
    resolution: {integrity: sha1-DFdfHR0yTd0do1rX7OPffRkIgBk=, tarball: https://registry.npm.wps.cn/finalhandler/download/finalhandler-1.3.1.tgz}
    engines: {node: '>= 0.8'}

  find-cache-dir@3.3.2:
    resolution: {integrity: sha1-swxbbv8HMHMa6pu9nb7L2AJW1ks=, tarball: https://registry.npm.wps.cn/find-cache-dir/download/find-cache-dir-3.3.2.tgz}
    engines: {node: '>=8'}

  find-up@3.0.0:
    resolution: {integrity: sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=, tarball: https://registry.npm.wps.cn/find-up/download/find-up-3.0.0.tgz}
    engines: {node: '>=6'}

  find-up@4.1.0:
    resolution: {integrity: sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=, tarball: https://registry.npm.wps.cn/find-up/download/find-up-4.1.0.tgz}
    engines: {node: '>=8'}

  find-up@5.0.0:
    resolution: {integrity: sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=, tarball: https://registry.npm.wps.cn/find-up/download/find-up-5.0.0.tgz}
    engines: {node: '>=10'}

  flat-cache@3.2.0:
    resolution: {integrity: sha1-LAwtUEDJmxYydxqdEFclwBFTY+4=, tarball: https://registry.npm.wps.cn/flat-cache/download/flat-cache-3.2.0.tgz}
    engines: {node: ^10.12.0 || >=12.0.0}

  flat@5.0.2:
    resolution: {integrity: sha1-jKb+MyBp/6nTJMMnGYxZglnOskE=, tarball: https://registry.npm.wps.cn/flat/download/flat-5.0.2.tgz}
    hasBin: true

  flatted@3.3.3:
    resolution: {integrity: sha1-Z8j62VRUp8er6/dLt47nSkQCM1g=, tarball: https://registry.npm.wps.cn/flatted/download/flatted-3.3.3.tgz}

  follow-redirects@1.15.9:
    resolution: {integrity: sha1-pgT6EORDv5jKlCKNnuvMLoosjuE=, tarball: https://registry.npm.wps.cn/follow-redirects/download/follow-redirects-1.15.9.tgz}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  for-each@0.3.5:
    resolution: {integrity: sha1-1lBogCeCaSD+6wr3R+57lCGkHUc=, tarball: https://registry.npm.wps.cn/for-each/download/for-each-0.3.5.tgz}
    engines: {node: '>= 0.4'}

  foreground-child@3.3.1:
    resolution: {integrity: sha1-Mujp7Rtoo0l777msK2rfkqY4V28=, tarball: https://registry.npm.wps.cn/foreground-child/download/foreground-child-3.3.1.tgz}
    engines: {node: '>=14'}

  fork-ts-checker-webpack-plugin@6.5.3:
    resolution: {integrity: sha1-7aLv9uIkdqJojRBmFojEf2EbN/M=, tarball: https://registry.npm.wps.cn/fork-ts-checker-webpack-plugin/download/fork-ts-checker-webpack-plugin-6.5.3.tgz}
    engines: {node: '>=10', yarn: '>=1.0.0'}
    peerDependencies:
      eslint: '>= 6'
      typescript: '>= 2.7'
      vue-template-compiler: '*'
      webpack: '>= 4'
    peerDependenciesMeta:
      eslint:
        optional: true
      vue-template-compiler:
        optional: true

  form-data@3.0.3:
    resolution: {integrity: sha1-NJyPLJ2PjwyHnuDrfMDTAAGNawk=, tarball: https://registry.npm.wps.cn/form-data/download/form-data-3.0.3.tgz}
    engines: {node: '>= 6'}

  forwarded@0.2.0:
    resolution: {integrity: sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE=, tarball: https://registry.npm.wps.cn/forwarded/download/forwarded-0.2.0.tgz}
    engines: {node: '>= 0.6'}

  fraction.js@4.3.7:
    resolution: {integrity: sha1-BsoAhRV+Qv2n+ecm55/vxAaIQPc=, tarball: https://registry.npm.wps.cn/fraction.js/download/fraction.js-4.3.7.tgz}

  fresh@0.5.2:
    resolution: {integrity: sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=, tarball: https://registry.npm.wps.cn/fresh/download/fresh-0.5.2.tgz}
    engines: {node: '>= 0.6'}

  fs-extra@10.1.0:
    resolution: {integrity: sha1-Aoc8+8QITd4SfqpfmQXu8jJdGr8=, tarball: https://registry.npm.wps.cn/fs-extra/download/fs-extra-10.1.0.tgz}
    engines: {node: '>=12'}

  fs-extra@9.1.0:
    resolution: {integrity: sha1-WVRGDHZKjaIJS6NVS/g55rmnyG0=, tarball: https://registry.npm.wps.cn/fs-extra/download/fs-extra-9.1.0.tgz}
    engines: {node: '>=10'}

  fs-monkey@1.0.6:
    resolution: {integrity: sha1-jq0IKVPojZks8/+ET6qQeyZ1baI=, tarball: https://registry.npm.wps.cn/fs-monkey/download/fs-monkey-1.0.6.tgz}

  fs.realpath@1.0.0:
    resolution: {integrity: sha1-FQStJSMVjKpA20onh8sBQRmU6k8=, tarball: https://registry.npm.wps.cn/fs.realpath/download/fs.realpath-1.0.0.tgz}

  fsevents@2.3.3:
    resolution: {integrity: sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=, tarball: https://registry.npm.wps.cn/fsevents/download/fsevents-2.3.3.tgz}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  function-bind@1.1.2:
    resolution: {integrity: sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=, tarball: https://registry.npm.wps.cn/function-bind/download/function-bind-1.1.2.tgz}

  function.prototype.name@1.1.8:
    resolution: {integrity: sha1-5o4d97JZpclJ7u+Vzb3lPt/6u3g=, tarball: https://registry.npm.wps.cn/function.prototype.name/download/function.prototype.name-1.1.8.tgz}
    engines: {node: '>= 0.4'}

  functions-have-names@1.2.3:
    resolution: {integrity: sha1-BAT+TuK6L2B/Dg7DyAuumUEzuDQ=, tarball: https://registry.npm.wps.cn/functions-have-names/download/functions-have-names-1.2.3.tgz}

  gensync@1.0.0-beta.2:
    resolution: {integrity: sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=, tarball: https://registry.npm.wps.cn/gensync/download/gensync-1.0.0-beta.2.tgz}
    engines: {node: '>=6.9.0'}

  get-caller-file@2.0.5:
    resolution: {integrity: sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=, tarball: https://registry.npm.wps.cn/get-caller-file/download/get-caller-file-2.0.5.tgz}
    engines: {node: 6.* || 8.* || >= 10.*}

  get-intrinsic@1.3.0:
    resolution: {integrity: sha1-dD8OO2lkqTpUke0b/6rgVNf5jQE=, tarball: https://registry.npm.wps.cn/get-intrinsic/download/get-intrinsic-1.3.0.tgz}
    engines: {node: '>= 0.4'}

  get-own-enumerable-property-symbols@3.0.2:
    resolution: {integrity: sha1-tf3nfyLL4185C04ImSLFC85u9mQ=, tarball: https://registry.npm.wps.cn/get-own-enumerable-property-symbols/download/get-own-enumerable-property-symbols-3.0.2.tgz}

  get-package-type@0.1.0:
    resolution: {integrity: sha1-jeLYA8/0TfO8bEVuZmizbDkm4Ro=, tarball: https://registry.npm.wps.cn/get-package-type/download/get-package-type-0.1.0.tgz}
    engines: {node: '>=8.0.0'}

  get-proto@1.0.1:
    resolution: {integrity: sha1-FQs/J0OGnvPoUewMSdFbHRTQDuE=, tarball: https://registry.npm.wps.cn/get-proto/download/get-proto-1.0.1.tgz}
    engines: {node: '>= 0.4'}

  get-stream@6.0.1:
    resolution: {integrity: sha1-omLY7vZ6ztV8KFKtYWdSakPL97c=, tarball: https://registry.npm.wps.cn/get-stream/download/get-stream-6.0.1.tgz}
    engines: {node: '>=10'}

  get-symbol-description@1.1.0:
    resolution: {integrity: sha1-e91U4L7+j/yfO04gMiDZ8eiBtu4=, tarball: https://registry.npm.wps.cn/get-symbol-description/download/get-symbol-description-1.1.0.tgz}
    engines: {node: '>= 0.4'}

  glob-parent@5.1.2:
    resolution: {integrity: sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=, tarball: https://registry.npm.wps.cn/glob-parent/download/glob-parent-5.1.2.tgz}
    engines: {node: '>= 6'}

  glob-parent@6.0.2:
    resolution: {integrity: sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=, tarball: https://registry.npm.wps.cn/glob-parent/download/glob-parent-6.0.2.tgz}
    engines: {node: '>=10.13.0'}

  glob-to-regexp@0.4.1:
    resolution: {integrity: sha1-x1KXCHyFG5pXi9IX3VmpL1n+VG4=, tarball: https://registry.npm.wps.cn/glob-to-regexp/download/glob-to-regexp-0.4.1.tgz}

  glob@10.4.5:
    resolution: {integrity: sha1-9NnwuQ/9urCcnXf18ptCYlF7CVY=, tarball: https://registry.npm.wps.cn/glob/download/glob-10.4.5.tgz}
    hasBin: true

  glob@7.2.3:
    resolution: {integrity: sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=, tarball: https://registry.npm.wps.cn/glob/download/glob-7.2.3.tgz}
    deprecated: Glob versions prior to v9 are no longer supported

  global-modules@2.0.0:
    resolution: {integrity: sha1-mXYFrSNF8n9RU5vqJldEISFcd4A=, tarball: https://registry.npm.wps.cn/global-modules/download/global-modules-2.0.0.tgz}
    engines: {node: '>=6'}

  global-prefix@3.0.0:
    resolution: {integrity: sha1-/IX3MGTfafUEIfR/iD/luRO6m5c=, tarball: https://registry.npm.wps.cn/global-prefix/download/global-prefix-3.0.0.tgz}
    engines: {node: '>=6'}

  globals@13.24.0:
    resolution: {integrity: sha1-hDKhnXjODB6DOUnDats0VAC7EXE=, tarball: https://registry.npm.wps.cn/globals/download/globals-13.24.0.tgz}
    engines: {node: '>=8'}

  globalthis@1.0.4:
    resolution: {integrity: sha1-dDDtOpddl7+1m8zkH1yruvplEjY=, tarball: https://registry.npm.wps.cn/globalthis/download/globalthis-1.0.4.tgz}
    engines: {node: '>= 0.4'}

  globby@11.1.0:
    resolution: {integrity: sha1-vUvpi7BC+D15b344EZkfvoKg00s=, tarball: https://registry.npm.wps.cn/globby/download/globby-11.1.0.tgz}
    engines: {node: '>=10'}

  gopd@1.2.0:
    resolution: {integrity: sha1-ifVrghe9vIgCvSmd9tfxCB1+UaE=, tarball: https://registry.npm.wps.cn/gopd/download/gopd-1.2.0.tgz}
    engines: {node: '>= 0.4'}

  graceful-fs@4.2.11:
    resolution: {integrity: sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM=, tarball: https://registry.npm.wps.cn/graceful-fs/download/graceful-fs-4.2.11.tgz}

  graphemer@1.4.0:
    resolution: {integrity: sha1-+y8dVeDjoYSa7/yQxPoN1ToOZsY=, tarball: https://registry.npm.wps.cn/graphemer/download/graphemer-1.4.0.tgz}

  gzip-size@6.0.0:
    resolution: {integrity: sha1-BlNn/VDCOcBnHLy61b4+LusQ5GI=, tarball: https://registry.npm.wps.cn/gzip-size/download/gzip-size-6.0.0.tgz}
    engines: {node: '>=10'}

  handle-thing@2.0.1:
    resolution: {integrity: sha1-hX95zjWVgMNA1DCBzGSJcNC7I04=, tarball: https://registry.npm.wps.cn/handle-thing/download/handle-thing-2.0.1.tgz}

  harmony-reflect@1.6.2:
    resolution: {integrity: sha1-Mey9MuZIo00DDYattn1NR1R/5xA=, tarball: https://registry.npm.wps.cn/harmony-reflect/download/harmony-reflect-1.6.2.tgz}

  has-bigints@1.1.0:
    resolution: {integrity: sha1-KGB+llrJZ+A80qLHCiY2oe2tSf4=, tarball: https://registry.npm.wps.cn/has-bigints/download/has-bigints-1.1.0.tgz}
    engines: {node: '>= 0.4'}

  has-flag@3.0.0:
    resolution: {integrity: sha1-tdRU3CGZriJWmfNGfloH87lVuv0=, tarball: https://registry.npm.wps.cn/has-flag/download/has-flag-3.0.0.tgz}
    engines: {node: '>=4'}

  has-flag@4.0.0:
    resolution: {integrity: sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=, tarball: https://registry.npm.wps.cn/has-flag/download/has-flag-4.0.0.tgz}
    engines: {node: '>=8'}

  has-property-descriptors@1.0.2:
    resolution: {integrity: sha1-lj7X0HHce/XwhMW/vg0bYiJYaFQ=, tarball: https://registry.npm.wps.cn/has-property-descriptors/download/has-property-descriptors-1.0.2.tgz}

  has-proto@1.2.0:
    resolution: {integrity: sha1-XeWm6r2V/f/ZgYtDBV6AZeOf6dU=, tarball: https://registry.npm.wps.cn/has-proto/download/has-proto-1.2.0.tgz}
    engines: {node: '>= 0.4'}

  has-symbols@1.1.0:
    resolution: {integrity: sha1-/JxqeDoISVHQuXH+EBjegTcHozg=, tarball: https://registry.npm.wps.cn/has-symbols/download/has-symbols-1.1.0.tgz}
    engines: {node: '>= 0.4'}

  has-tostringtag@1.0.2:
    resolution: {integrity: sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=, tarball: https://registry.npm.wps.cn/has-tostringtag/download/has-tostringtag-1.0.2.tgz}
    engines: {node: '>= 0.4'}

  hasown@2.0.2:
    resolution: {integrity: sha1-AD6vkb563DcuhOxZ3DclLO24AAM=, tarball: https://registry.npm.wps.cn/hasown/download/hasown-2.0.2.tgz}
    engines: {node: '>= 0.4'}

  he@1.2.0:
    resolution: {integrity: sha1-hK5l+n6vsWX922FWauFLrwVmTw8=, tarball: https://registry.npm.wps.cn/he/download/he-1.2.0.tgz}
    hasBin: true

  hoopy@0.1.4:
    resolution: {integrity: sha1-YJIH1mEQADOpqUAq096mdzgcGx0=, tarball: https://registry.npm.wps.cn/hoopy/download/hoopy-0.1.4.tgz}
    engines: {node: '>= 6.0.0'}

  hpack.js@2.1.6:
    resolution: {integrity: sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI=, tarball: https://registry.npm.wps.cn/hpack.js/download/hpack.js-2.1.6.tgz}

  html-encoding-sniffer@2.0.1:
    resolution: {integrity: sha1-QqbcT9M/ACgRduiyN1nKTk+hhfM=, tarball: https://registry.npm.wps.cn/html-encoding-sniffer/download/html-encoding-sniffer-2.0.1.tgz}
    engines: {node: '>=10'}

  html-entities@2.6.0:
    resolution: {integrity: sha1-fGTx6js2gYzK49P7SLaXQgjphPg=, tarball: https://registry.npm.wps.cn/html-entities/download/html-entities-2.6.0.tgz}

  html-escaper@2.0.2:
    resolution: {integrity: sha1-39YAJ9o2o238viNiYsAKWCJoFFM=, tarball: https://registry.npm.wps.cn/html-escaper/download/html-escaper-2.0.2.tgz}

  html-minifier-terser@6.1.0:
    resolution: {integrity: sha1-v8gYk0zAeRj2s2afV3Ts39SPMqs=, tarball: https://registry.npm.wps.cn/html-minifier-terser/download/html-minifier-terser-6.1.0.tgz}
    engines: {node: '>=12'}
    hasBin: true

  html-webpack-plugin@5.6.3:
    resolution: {integrity: sha1-oxFF8P7kGE1Tp5T5UTFH3x5lNoU=, tarball: https://registry.npm.wps.cn/html-webpack-plugin/download/html-webpack-plugin-5.6.3.tgz}
    engines: {node: '>=10.13.0'}
    peerDependencies:
      '@rspack/core': 0.x || 1.x
      webpack: ^5.20.0
    peerDependenciesMeta:
      '@rspack/core':
        optional: true
      webpack:
        optional: true

  htmlparser2@6.1.0:
    resolution: {integrity: sha1-xNditsM3GgXb5l6UrkOp+EX7j7c=, tarball: https://registry.npm.wps.cn/htmlparser2/download/htmlparser2-6.1.0.tgz}

  http-deceiver@1.2.7:
    resolution: {integrity: sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc=, tarball: https://registry.npm.wps.cn/http-deceiver/download/http-deceiver-1.2.7.tgz}

  http-errors@1.6.3:
    resolution: {integrity: sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=, tarball: https://registry.npm.wps.cn/http-errors/download/http-errors-1.6.3.tgz}
    engines: {node: '>= 0.6'}

  http-errors@2.0.0:
    resolution: {integrity: sha1-t3dKFIbvc892Z6ya4IWMASxXudM=, tarball: https://registry.npm.wps.cn/http-errors/download/http-errors-2.0.0.tgz}
    engines: {node: '>= 0.8'}

  http-parser-js@0.5.10:
    resolution: {integrity: sha1-syd71tftVYjiDqc79yT8vkRgkHU=, tarball: https://registry.npm.wps.cn/http-parser-js/download/http-parser-js-0.5.10.tgz}

  http-proxy-agent@4.0.1:
    resolution: {integrity: sha1-ioyO9/WTLM+VPClsqCkblap0qjo=, tarball: https://registry.npm.wps.cn/http-proxy-agent/download/http-proxy-agent-4.0.1.tgz}
    engines: {node: '>= 6'}

  http-proxy-middleware@2.0.9:
    resolution: {integrity: sha1-6eY9aK+qTu49FH85FJq4TAwoFe8=, tarball: https://registry.npm.wps.cn/http-proxy-middleware/download/http-proxy-middleware-2.0.9.tgz}
    engines: {node: '>=12.0.0'}
    peerDependencies:
      '@types/express': ^4.17.13
    peerDependenciesMeta:
      '@types/express':
        optional: true

  http-proxy@1.18.1:
    resolution: {integrity: sha1-QBVB8FNIhLv5UmAzTnL4juOXZUk=, tarball: https://registry.npm.wps.cn/http-proxy/download/http-proxy-1.18.1.tgz}
    engines: {node: '>=8.0.0'}

  https-proxy-agent@5.0.1:
    resolution: {integrity: sha1-xZ7yJKBP6LdU89sAY6Jeow0ABdY=, tarball: https://registry.npm.wps.cn/https-proxy-agent/download/https-proxy-agent-5.0.1.tgz}
    engines: {node: '>= 6'}

  human-signals@2.1.0:
    resolution: {integrity: sha1-3JH8ukLk0G5Kuu0zs+ejwC9RTqA=, tarball: https://registry.npm.wps.cn/human-signals/download/human-signals-2.1.0.tgz}
    engines: {node: '>=10.17.0'}

  iconv-lite@0.4.24:
    resolution: {integrity: sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=, tarball: https://registry.npm.wps.cn/iconv-lite/download/iconv-lite-0.4.24.tgz}
    engines: {node: '>=0.10.0'}

  iconv-lite@0.6.3:
    resolution: {integrity: sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=, tarball: https://registry.npm.wps.cn/iconv-lite/download/iconv-lite-0.6.3.tgz}
    engines: {node: '>=0.10.0'}

  icss-utils@5.1.0:
    resolution: {integrity: sha1-xr5oWKvQE9do6YNmrkfiXViHsa4=, tarball: https://registry.npm.wps.cn/icss-utils/download/icss-utils-5.1.0.tgz}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0

  idb@7.1.1:
    resolution: {integrity: sha1-2RDe2GbTLHztm+/Fv9829XLO1ys=, tarball: https://registry.npm.wps.cn/idb/download/idb-7.1.1.tgz}

  identity-obj-proxy@3.0.0:
    resolution: {integrity: sha1-lNK9qWCERT7zb7xarsN+D3nx/BQ=, tarball: https://registry.npm.wps.cn/identity-obj-proxy/download/identity-obj-proxy-3.0.0.tgz}
    engines: {node: '>=4'}

  ignore@5.3.2:
    resolution: {integrity: sha1-PNQOcp82Q/2HywTlC/DrcivFlvU=, tarball: https://registry.npm.wps.cn/ignore/download/ignore-5.3.2.tgz}
    engines: {node: '>= 4'}

  image-size@0.5.5:
    resolution: {integrity: sha1-Cd/Uq50g4p6xw+gLiZA3jfnjy5w=, tarball: https://registry.npm.wps.cn/image-size/download/image-size-0.5.5.tgz}
    engines: {node: '>=0.10.0'}
    hasBin: true

  immer@9.0.21:
    resolution: {integrity: sha1-HgJeoxpA8k+wZPH+8j6TFJYzAXY=, tarball: https://registry.npm.wps.cn/immer/download/immer-9.0.21.tgz}

  import-fresh@3.3.1:
    resolution: {integrity: sha1-nOy1ZQPAraHydB271lRuSxO1fM8=, tarball: https://registry.npm.wps.cn/import-fresh/download/import-fresh-3.3.1.tgz}
    engines: {node: '>=6'}

  import-local@3.2.0:
    resolution: {integrity: sha1-w9XHRXmMAqb4uJdyarpRABhu4mA=, tarball: https://registry.npm.wps.cn/import-local/download/import-local-3.2.0.tgz}
    engines: {node: '>=8'}
    hasBin: true

  imurmurhash@0.1.4:
    resolution: {integrity: sha1-khi5srkoojixPcT7a21XbyMUU+o=, tarball: https://registry.npm.wps.cn/imurmurhash/download/imurmurhash-0.1.4.tgz}
    engines: {node: '>=0.8.19'}

  indent-string@4.0.0:
    resolution: {integrity: sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE=, tarball: https://registry.npm.wps.cn/indent-string/download/indent-string-4.0.0.tgz}
    engines: {node: '>=8'}

  inflight@1.0.6:
    resolution: {integrity: sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=, tarball: https://registry.npm.wps.cn/inflight/download/inflight-1.0.6.tgz}
    deprecated: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.

  inherits@2.0.3:
    resolution: {integrity: sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=, tarball: https://registry.npm.wps.cn/inherits/download/inherits-2.0.3.tgz}

  inherits@2.0.4:
    resolution: {integrity: sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=, tarball: https://registry.npm.wps.cn/inherits/download/inherits-2.0.4.tgz}

  ini@1.3.8:
    resolution: {integrity: sha1-op2kJbSIBvNHZ6Tvzjlyaa8oQyw=, tarball: https://registry.npm.wps.cn/ini/download/ini-1.3.8.tgz}

  internal-slot@1.1.0:
    resolution: {integrity: sha1-HqyRdilH0vcFa8g42T4TsulgSWE=, tarball: https://registry.npm.wps.cn/internal-slot/download/internal-slot-1.1.0.tgz}
    engines: {node: '>= 0.4'}

  ipaddr.js@1.9.1:
    resolution: {integrity: sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=, tarball: https://registry.npm.wps.cn/ipaddr.js/download/ipaddr.js-1.9.1.tgz}
    engines: {node: '>= 0.10'}

  ipaddr.js@2.2.0:
    resolution: {integrity: sha1-0z+nusKE9N56+UljjJ1oFXxrkug=, tarball: https://registry.npm.wps.cn/ipaddr.js/download/ipaddr.js-2.2.0.tgz}
    engines: {node: '>= 10'}

  is-arguments@1.2.0:
    resolution: {integrity: sha1-rVjGrs9WO3jvK/BN9UDaj119jhs=, tarball: https://registry.npm.wps.cn/is-arguments/download/is-arguments-1.2.0.tgz}
    engines: {node: '>= 0.4'}

  is-array-buffer@3.0.5:
    resolution: {integrity: sha1-ZXQuHmh70sxmYlMGj9hwf+TUQoA=, tarball: https://registry.npm.wps.cn/is-array-buffer/download/is-array-buffer-3.0.5.tgz}
    engines: {node: '>= 0.4'}

  is-arrayish@0.2.1:
    resolution: {integrity: sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=, tarball: https://registry.npm.wps.cn/is-arrayish/download/is-arrayish-0.2.1.tgz}

  is-async-function@2.1.1:
    resolution: {integrity: sha1-PmkBjI4E5ztzh5PQIL/ohLn9NSM=, tarball: https://registry.npm.wps.cn/is-async-function/download/is-async-function-2.1.1.tgz}
    engines: {node: '>= 0.4'}

  is-bigint@1.1.0:
    resolution: {integrity: sha1-3aejRF31ekJYPbQihoLrp8QXBnI=, tarball: https://registry.npm.wps.cn/is-bigint/download/is-bigint-1.1.0.tgz}
    engines: {node: '>= 0.4'}

  is-binary-path@2.1.0:
    resolution: {integrity: sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=, tarball: https://registry.npm.wps.cn/is-binary-path/download/is-binary-path-2.1.0.tgz}
    engines: {node: '>=8'}

  is-boolean-object@1.2.2:
    resolution: {integrity: sha1-cGf0dwmAmjk8cf9bs+E12KkhXZ4=, tarball: https://registry.npm.wps.cn/is-boolean-object/download/is-boolean-object-1.2.2.tgz}
    engines: {node: '>= 0.4'}

  is-callable@1.2.7:
    resolution: {integrity: sha1-O8KoXqdC2eNiBdys3XLKH9xRsFU=, tarball: https://registry.npm.wps.cn/is-callable/download/is-callable-1.2.7.tgz}
    engines: {node: '>= 0.4'}

  is-core-module@2.16.1:
    resolution: {integrity: sha1-KpiAGoSfQ+Kt1kT7trxiKbGaTvQ=, tarball: https://registry.npm.wps.cn/is-core-module/download/is-core-module-2.16.1.tgz}
    engines: {node: '>= 0.4'}

  is-data-view@1.0.2:
    resolution: {integrity: sha1-uuCkG5aImGwhiN2mZX5WuPnmO44=, tarball: https://registry.npm.wps.cn/is-data-view/download/is-data-view-1.0.2.tgz}
    engines: {node: '>= 0.4'}

  is-date-object@1.1.0:
    resolution: {integrity: sha1-rYVUGZb8eqiycpcB0ntzGfldgvc=, tarball: https://registry.npm.wps.cn/is-date-object/download/is-date-object-1.1.0.tgz}
    engines: {node: '>= 0.4'}

  is-docker@2.2.1:
    resolution: {integrity: sha1-M+6r4jz+hvFL3kQIoCwM+4U6zao=, tarball: https://registry.npm.wps.cn/is-docker/download/is-docker-2.2.1.tgz}
    engines: {node: '>=8'}
    hasBin: true

  is-extglob@2.1.1:
    resolution: {integrity: sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=, tarball: https://registry.npm.wps.cn/is-extglob/download/is-extglob-2.1.1.tgz}
    engines: {node: '>=0.10.0'}

  is-finalizationregistry@1.1.1:
    resolution: {integrity: sha1-7v3NxslN3QZ02chYh7+T+USpfJA=, tarball: https://registry.npm.wps.cn/is-finalizationregistry/download/is-finalizationregistry-1.1.1.tgz}
    engines: {node: '>= 0.4'}

  is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=, tarball: https://registry.npm.wps.cn/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz}
    engines: {node: '>=8'}

  is-generator-fn@2.1.0:
    resolution: {integrity: sha1-fRQK3DiarzARqPKipM+m+q3/sRg=, tarball: https://registry.npm.wps.cn/is-generator-fn/download/is-generator-fn-2.1.0.tgz}
    engines: {node: '>=6'}

  is-generator-function@1.1.0:
    resolution: {integrity: sha1-vz7tqTEgE5T1e126KAD5GiODCco=, tarball: https://registry.npm.wps.cn/is-generator-function/download/is-generator-function-1.1.0.tgz}
    engines: {node: '>= 0.4'}

  is-glob@4.0.3:
    resolution: {integrity: sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=, tarball: https://registry.npm.wps.cn/is-glob/download/is-glob-4.0.3.tgz}
    engines: {node: '>=0.10.0'}

  is-map@2.0.3:
    resolution: {integrity: sha1-7elrf+HicLPERl46RlZYdkkm1i4=, tarball: https://registry.npm.wps.cn/is-map/download/is-map-2.0.3.tgz}
    engines: {node: '>= 0.4'}

  is-module@1.0.0:
    resolution: {integrity: sha1-Mlj7afeMFNW4FdZkM2tM/7ZEFZE=, tarball: https://registry.npm.wps.cn/is-module/download/is-module-1.0.0.tgz}

  is-negative-zero@2.0.3:
    resolution: {integrity: sha1-ztkDoCespjgbd3pXQwadc3akl0c=, tarball: https://registry.npm.wps.cn/is-negative-zero/download/is-negative-zero-2.0.3.tgz}
    engines: {node: '>= 0.4'}

  is-number-object@1.1.1:
    resolution: {integrity: sha1-FEsh6VobwUggXcwoFKkTTsQbJUE=, tarball: https://registry.npm.wps.cn/is-number-object/download/is-number-object-1.1.1.tgz}
    engines: {node: '>= 0.4'}

  is-number@7.0.0:
    resolution: {integrity: sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=, tarball: https://registry.npm.wps.cn/is-number/download/is-number-7.0.0.tgz}
    engines: {node: '>=0.12.0'}

  is-obj@1.0.1:
    resolution: {integrity: sha1-PkcprB9f3gJc19g6iW2rn09n2w8=, tarball: https://registry.npm.wps.cn/is-obj/download/is-obj-1.0.1.tgz}
    engines: {node: '>=0.10.0'}

  is-path-inside@3.0.3:
    resolution: {integrity: sha1-0jE2LlOgf/Kw4Op/7QSRYf/RYoM=, tarball: https://registry.npm.wps.cn/is-path-inside/download/is-path-inside-3.0.3.tgz}
    engines: {node: '>=8'}

  is-plain-obj@3.0.0:
    resolution: {integrity: sha1-r28uoUrFpkYYOlu9tbqrvBVq2dc=, tarball: https://registry.npm.wps.cn/is-plain-obj/download/is-plain-obj-3.0.0.tgz}
    engines: {node: '>=10'}

  is-plain-object@2.0.4:
    resolution: {integrity: sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=, tarball: https://registry.npm.wps.cn/is-plain-object/download/is-plain-object-2.0.4.tgz}
    engines: {node: '>=0.10.0'}

  is-potential-custom-element-name@1.0.1:
    resolution: {integrity: sha1-Fx7W8Z46xVQ5Tt94yqBXhKRb67U=, tarball: https://registry.npm.wps.cn/is-potential-custom-element-name/download/is-potential-custom-element-name-1.0.1.tgz}

  is-regex@1.2.1:
    resolution: {integrity: sha1-dtcKPtEO+b5I61d4h9dCBb8MrSI=, tarball: https://registry.npm.wps.cn/is-regex/download/is-regex-1.2.1.tgz}
    engines: {node: '>= 0.4'}

  is-regexp@1.0.0:
    resolution: {integrity: sha1-/S2INUXEa6xaYz57mgnof6LLUGk=, tarball: https://registry.npm.wps.cn/is-regexp/download/is-regexp-1.0.0.tgz}
    engines: {node: '>=0.10.0'}

  is-root@2.1.0:
    resolution: {integrity: sha1-gJ4YEpzxEpZEMCpPhUQDXVGYSpw=, tarball: https://registry.npm.wps.cn/is-root/download/is-root-2.1.0.tgz}
    engines: {node: '>=6'}

  is-set@2.0.3:
    resolution: {integrity: sha1-irIJ6kJGCBQTct7W4MsgDvHZ0B0=, tarball: https://registry.npm.wps.cn/is-set/download/is-set-2.0.3.tgz}
    engines: {node: '>= 0.4'}

  is-shared-array-buffer@1.0.4:
    resolution: {integrity: sha1-m2eES9m38ka6BwjDqT40Jpx3T28=, tarball: https://registry.npm.wps.cn/is-shared-array-buffer/download/is-shared-array-buffer-1.0.4.tgz}
    engines: {node: '>= 0.4'}

  is-stream@2.0.1:
    resolution: {integrity: sha1-+sHj1TuXrVqdCunO8jifWBClwHc=, tarball: https://registry.npm.wps.cn/is-stream/download/is-stream-2.0.1.tgz}
    engines: {node: '>=8'}

  is-string@1.1.1:
    resolution: {integrity: sha1-kuo/PVxbbgOcqGd+WsjQfqdzy7k=, tarball: https://registry.npm.wps.cn/is-string/download/is-string-1.1.1.tgz}
    engines: {node: '>= 0.4'}

  is-symbol@1.1.1:
    resolution: {integrity: sha1-9HdhJ59TLisFpwJKdQbbvtrNBjQ=, tarball: https://registry.npm.wps.cn/is-symbol/download/is-symbol-1.1.1.tgz}
    engines: {node: '>= 0.4'}

  is-typed-array@1.1.15:
    resolution: {integrity: sha1-S/tKRbYc7oOlpG+6d45OjVnAzgs=, tarball: https://registry.npm.wps.cn/is-typed-array/download/is-typed-array-1.1.15.tgz}
    engines: {node: '>= 0.4'}

  is-typedarray@1.0.0:
    resolution: {integrity: sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=, tarball: https://registry.npm.wps.cn/is-typedarray/download/is-typedarray-1.0.0.tgz}

  is-weakmap@2.0.2:
    resolution: {integrity: sha1-v3JhXWSd/l9pkHnFS4PkfRrhnP0=, tarball: https://registry.npm.wps.cn/is-weakmap/download/is-weakmap-2.0.2.tgz}
    engines: {node: '>= 0.4'}

  is-weakref@1.1.1:
    resolution: {integrity: sha1-7qQwGCvo1kF0vZa/+8RvIb8/kpM=, tarball: https://registry.npm.wps.cn/is-weakref/download/is-weakref-1.1.1.tgz}
    engines: {node: '>= 0.4'}

  is-weakset@2.0.4:
    resolution: {integrity: sha1-yfXesLwZBsbW8QJ/KE3fRZJJ2so=, tarball: https://registry.npm.wps.cn/is-weakset/download/is-weakset-2.0.4.tgz}
    engines: {node: '>= 0.4'}

  is-what@3.14.1:
    resolution: {integrity: sha1-4SIvRt3ahd6tD9HJ3xMXYOd3VcE=, tarball: https://registry.npm.wps.cn/is-what/download/is-what-3.14.1.tgz}

  is-wsl@2.2.0:
    resolution: {integrity: sha1-dKTHbnfKn9P5MvKQwX6jJs0VcnE=, tarball: https://registry.npm.wps.cn/is-wsl/download/is-wsl-2.2.0.tgz}
    engines: {node: '>=8'}

  isarray@1.0.0:
    resolution: {integrity: sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=, tarball: https://registry.npm.wps.cn/isarray/download/isarray-1.0.0.tgz}

  isarray@2.0.5:
    resolution: {integrity: sha1-ivHkwSISRMxiRZ+vOJQNTmRKVyM=, tarball: https://registry.npm.wps.cn/isarray/download/isarray-2.0.5.tgz}

  isexe@2.0.0:
    resolution: {integrity: sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=, tarball: https://registry.npm.wps.cn/isexe/download/isexe-2.0.0.tgz}

  isobject@3.0.1:
    resolution: {integrity: sha1-TkMekrEalzFjaqH5yNHMvP2reN8=, tarball: https://registry.npm.wps.cn/isobject/download/isobject-3.0.1.tgz}
    engines: {node: '>=0.10.0'}

  istanbul-lib-coverage@3.2.2:
    resolution: {integrity: sha1-LRZsSwZE1Do58Ev2wu3R5YXzF1Y=, tarball: https://registry.npm.wps.cn/istanbul-lib-coverage/download/istanbul-lib-coverage-3.2.2.tgz}
    engines: {node: '>=8'}

  istanbul-lib-instrument@5.2.1:
    resolution: {integrity: sha1-0QyIhcISVXThwjHKyt+VVnXhzj0=, tarball: https://registry.npm.wps.cn/istanbul-lib-instrument/download/istanbul-lib-instrument-5.2.1.tgz}
    engines: {node: '>=8'}

  istanbul-lib-report@3.0.1:
    resolution: {integrity: sha1-kIMFusmlvRdaxqdEier9D8JEWn0=, tarball: https://registry.npm.wps.cn/istanbul-lib-report/download/istanbul-lib-report-3.0.1.tgz}
    engines: {node: '>=10'}

  istanbul-lib-source-maps@4.0.1:
    resolution: {integrity: sha1-iV86cJ/PujTG3lpCk5Ai8+Q1hVE=, tarball: https://registry.npm.wps.cn/istanbul-lib-source-maps/download/istanbul-lib-source-maps-4.0.1.tgz}
    engines: {node: '>=10'}

  istanbul-reports@3.1.7:
    resolution: {integrity: sha1-2u0SueHcpRjhXAVuHlN+dBKA+gs=, tarball: https://registry.npm.wps.cn/istanbul-reports/download/istanbul-reports-3.1.7.tgz}
    engines: {node: '>=8'}

  iterator.prototype@1.1.5:
    resolution: {integrity: sha1-EslZop3jLeCqO7u4AfTXdwZtrjk=, tarball: https://registry.npm.wps.cn/iterator.prototype/download/iterator.prototype-1.1.5.tgz}
    engines: {node: '>= 0.4'}

  jackspeak@3.4.3:
    resolution: {integrity: sha1-iDOp2Jq0rN5hiJQr0cU7Y5DtWoo=, tarball: https://registry.npm.wps.cn/jackspeak/download/jackspeak-3.4.3.tgz}

  jake@10.9.2:
    resolution: {integrity: sha1-auSH5qaa/sOl4WdiiZa1nzWuK38=, tarball: https://registry.npm.wps.cn/jake/download/jake-10.9.2.tgz}
    engines: {node: '>=10'}
    hasBin: true

  jest-changed-files@27.5.1:
    resolution: {integrity: sha1-o0iu0A7Jv2ccxYpm/L58Pf1qaPU=, tarball: https://registry.npm.wps.cn/jest-changed-files/download/jest-changed-files-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-circus@27.5.1:
    resolution: {integrity: sha1-N6WkRZt79EBuU9Y3tJ0ixl0SXsw=, tarball: https://registry.npm.wps.cn/jest-circus/download/jest-circus-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-cli@27.5.1:
    resolution: {integrity: sha1-J4eUpuZFjqgClUfmxsv2c70wsUU=, tarball: https://registry.npm.wps.cn/jest-cli/download/jest-cli-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}
    hasBin: true
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true

  jest-config@27.5.1:
    resolution: {integrity: sha1-XDh94z3KP5mtY1fd7M2RvzoOSkE=, tarball: https://registry.npm.wps.cn/jest-config/download/jest-config-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}
    peerDependencies:
      ts-node: '>=9.0.0'
    peerDependenciesMeta:
      ts-node:
        optional: true

  jest-diff@27.5.1:
    resolution: {integrity: sha1-oH9QEayeZkPPipWkYrex7PZoDe8=, tarball: https://registry.npm.wps.cn/jest-diff/download/jest-diff-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-docblock@27.5.1:
    resolution: {integrity: sha1-FAkvNkpCxhCNQsM8jPMOBY4l9sA=, tarball: https://registry.npm.wps.cn/jest-docblock/download/jest-docblock-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-each@27.5.1:
    resolution: {integrity: sha1-W8hwFvRe2VB/7W5HAqW0aKWyxE4=, tarball: https://registry.npm.wps.cn/jest-each/download/jest-each-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-environment-jsdom@27.5.1:
    resolution: {integrity: sha1-6pzNH8YQIJZVp3iY+GsrVZUWpUY=, tarball: https://registry.npm.wps.cn/jest-environment-jsdom/download/jest-environment-jsdom-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-environment-node@27.5.1:
    resolution: {integrity: sha1-3tws/lL6trj1cUtICK76hTV6Nl4=, tarball: https://registry.npm.wps.cn/jest-environment-node/download/jest-environment-node-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-get-type@27.5.1:
    resolution: {integrity: sha1-PNYTxQew96zgE99Aehwc1Xi8tPE=, tarball: https://registry.npm.wps.cn/jest-get-type/download/jest-get-type-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-haste-map@27.5.1:
    resolution: {integrity: sha1-n9i9fntPpQLZxhZMVkBRK06BHn8=, tarball: https://registry.npm.wps.cn/jest-haste-map/download/jest-haste-map-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-jasmine2@27.5.1:
    resolution: {integrity: sha1-oDewA070mp89ccQ3WnlvOyMNGsQ=, tarball: https://registry.npm.wps.cn/jest-jasmine2/download/jest-jasmine2-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-leak-detector@27.5.1:
    resolution: {integrity: sha1-bsnVTDV53W4+ZtcONJit+A/eP7g=, tarball: https://registry.npm.wps.cn/jest-leak-detector/download/jest-leak-detector-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-matcher-utils@27.5.1:
    resolution: {integrity: sha1-nAzb2oJFvCLSMxcp0QkTCLQM+Ks=, tarball: https://registry.npm.wps.cn/jest-matcher-utils/download/jest-matcher-utils-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-message-util@27.5.1:
    resolution: {integrity: sha1-vdpygG2hDZ7WQl4Sr/84zRRYts8=, tarball: https://registry.npm.wps.cn/jest-message-util/download/jest-message-util-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-message-util@28.1.3:
    resolution: {integrity: sha1-Iy3vfy4zPx7syQZJtblLAFXnxD0=, tarball: https://registry.npm.wps.cn/jest-message-util/download/jest-message-util-28.1.3.tgz}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}

  jest-mock@27.5.1:
    resolution: {integrity: sha1-GZSDNtSe9NnFICHTSse182/5Z9Y=, tarball: https://registry.npm.wps.cn/jest-mock/download/jest-mock-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-pnp-resolver@1.2.3:
    resolution: {integrity: sha1-kwsVRhZNStWTfVVA5xHU041MrS4=, tarball: https://registry.npm.wps.cn/jest-pnp-resolver/download/jest-pnp-resolver-1.2.3.tgz}
    engines: {node: '>=6'}
    peerDependencies:
      jest-resolve: '*'
    peerDependenciesMeta:
      jest-resolve:
        optional: true

  jest-regex-util@27.5.1:
    resolution: {integrity: sha1-TaFD9+n9HlQtSqaWF7OOSng2W5U=, tarball: https://registry.npm.wps.cn/jest-regex-util/download/jest-regex-util-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-regex-util@28.0.2:
    resolution: {integrity: sha1-r9w3ejsl+26Aglrc92yFTlv0fq0=, tarball: https://registry.npm.wps.cn/jest-regex-util/download/jest-regex-util-28.0.2.tgz}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}

  jest-resolve-dependencies@27.5.1:
    resolution: {integrity: sha1-2BHsyDBecxzIbdeXQe6Y/tBvHag=, tarball: https://registry.npm.wps.cn/jest-resolve-dependencies/download/jest-resolve-dependencies-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-resolve@27.5.1:
    resolution: {integrity: sha1-ovHFoHluwY/p6xU2rDgUwjYXs4Q=, tarball: https://registry.npm.wps.cn/jest-resolve/download/jest-resolve-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-runner@27.5.1:
    resolution: {integrity: sha1-Bxsnwfow2QVAgFxWRaDsFnx7YuU=, tarball: https://registry.npm.wps.cn/jest-runner/download/jest-runner-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-runtime@27.5.1:
    resolution: {integrity: sha1-SJYAPXozT36OSlO6k/ubzT2woa8=, tarball: https://registry.npm.wps.cn/jest-runtime/download/jest-runtime-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-serializer@27.5.1:
    resolution: {integrity: sha1-gUOEEKMOpm/Vf/cwg1Ej3qH7H2Q=, tarball: https://registry.npm.wps.cn/jest-serializer/download/jest-serializer-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-snapshot@27.5.1:
    resolution: {integrity: sha1-tmjVDSPTgFSlG0LEA5yrWa5utqE=, tarball: https://registry.npm.wps.cn/jest-snapshot/download/jest-snapshot-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-util@27.5.1:
    resolution: {integrity: sha1-O6l3Ho4xoLhdpI/gsIkfuGwBwvk=, tarball: https://registry.npm.wps.cn/jest-util/download/jest-util-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-util@28.1.3:
    resolution: {integrity: sha1-9PkyqgB08GeZQyIP+cu6fklwKLA=, tarball: https://registry.npm.wps.cn/jest-util/download/jest-util-28.1.3.tgz}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}

  jest-validate@27.5.1:
    resolution: {integrity: sha1-kZfVTcC9tSJguNtAtGrmaOBN8Gc=, tarball: https://registry.npm.wps.cn/jest-validate/download/jest-validate-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-watch-typeahead@1.1.0:
    resolution: {integrity: sha1-tKaCbfuclCDaL3vJAN5Z2tESZqk=, tarball: https://registry.npm.wps.cn/jest-watch-typeahead/download/jest-watch-typeahead-1.1.0.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      jest: ^27.0.0 || ^28.0.0

  jest-watcher@27.5.1:
    resolution: {integrity: sha1-cb2F+5veOiwuxNw1NDeXHEPGQqI=, tarball: https://registry.npm.wps.cn/jest-watcher/download/jest-watcher-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-watcher@28.1.3:
    resolution: {integrity: sha1-xgI6WboiVeO0xXF5/JQWSz5zq9Q=, tarball: https://registry.npm.wps.cn/jest-watcher/download/jest-watcher-28.1.3.tgz}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}

  jest-worker@26.6.2:
    resolution: {integrity: sha1-f3LLxNZDw2Xie5/XdfnQ6qnHqO0=, tarball: https://registry.npm.wps.cn/jest-worker/download/jest-worker-26.6.2.tgz}
    engines: {node: '>= 10.13.0'}

  jest-worker@27.5.1:
    resolution: {integrity: sha1-jRRvCQDolzsQa29zzB6ajLhvjbA=, tarball: https://registry.npm.wps.cn/jest-worker/download/jest-worker-27.5.1.tgz}
    engines: {node: '>= 10.13.0'}

  jest-worker@28.1.3:
    resolution: {integrity: sha1-fjxM4/oj0btqzLFp5/OW+Y7Uu5g=, tarball: https://registry.npm.wps.cn/jest-worker/download/jest-worker-28.1.3.tgz}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}

  jest@27.5.1:
    resolution: {integrity: sha1-2t8zunCneb56b8MwFYQ7UUlPY/w=, tarball: https://registry.npm.wps.cn/jest/download/jest-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}
    hasBin: true
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true

  jiti@1.21.7:
    resolution: {integrity: sha1-ndgQQ0JKPShFixk9ll8NGKIwC6k=, tarball: https://registry.npm.wps.cn/jiti/download/jiti-1.21.7.tgz}
    hasBin: true

  js-tokens@4.0.0:
    resolution: {integrity: sha1-GSA/tZmR35jjoocFDUZHzerzJJk=, tarball: https://registry.npm.wps.cn/js-tokens/download/js-tokens-4.0.0.tgz}

  js-yaml@3.14.1:
    resolution: {integrity: sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=, tarball: https://registry.npm.wps.cn/js-yaml/download/js-yaml-3.14.1.tgz}
    hasBin: true

  js-yaml@4.1.0:
    resolution: {integrity: sha1-wftl+PUBeQHN0slRhkuhhFihBgI=, tarball: https://registry.npm.wps.cn/js-yaml/download/js-yaml-4.1.0.tgz}
    hasBin: true

  jsdom@16.7.0:
    resolution: {integrity: sha1-kYrnGWVCSxl8gZ+Bg6dU4Yl3txA=, tarball: https://registry.npm.wps.cn/jsdom/download/jsdom-16.7.0.tgz}
    engines: {node: '>=10'}
    peerDependencies:
      canvas: ^2.5.0
    peerDependenciesMeta:
      canvas:
        optional: true

  jsesc@3.0.2:
    resolution: {integrity: sha1-u4sJpll7pCZCXy5KByRcPQC5ND4=, tarball: https://registry.npm.wps.cn/jsesc/download/jsesc-3.0.2.tgz}
    engines: {node: '>=6'}
    hasBin: true

  jsesc@3.1.0:
    resolution: {integrity: sha1-dNM1ojT2ftGZB/2t+sfM+dQJgl0=, tarball: https://registry.npm.wps.cn/jsesc/download/jsesc-3.1.0.tgz}
    engines: {node: '>=6'}
    hasBin: true

  json-buffer@3.0.1:
    resolution: {integrity: sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=, tarball: https://registry.npm.wps.cn/json-buffer/download/json-buffer-3.0.1.tgz}

  json-parse-even-better-errors@2.3.1:
    resolution: {integrity: sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=, tarball: https://registry.npm.wps.cn/json-parse-even-better-errors/download/json-parse-even-better-errors-2.3.1.tgz}

  json-schema-traverse@0.4.1:
    resolution: {integrity: sha1-afaofZUTq4u4/mO9sJecRI5oRmA=, tarball: https://registry.npm.wps.cn/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz}

  json-schema-traverse@1.0.0:
    resolution: {integrity: sha1-rnvLNlard6c7pcSb9lTzjmtoYOI=, tarball: https://registry.npm.wps.cn/json-schema-traverse/download/json-schema-traverse-1.0.0.tgz}

  json-schema@0.4.0:
    resolution: {integrity: sha1-995M9u+rg4666zI2R0y7paGTCrU=, tarball: https://registry.npm.wps.cn/json-schema/download/json-schema-0.4.0.tgz}

  json-stable-stringify-without-jsonify@1.0.1:
    resolution: {integrity: sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=, tarball: https://registry.npm.wps.cn/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz}

  json5@1.0.2:
    resolution: {integrity: sha1-Y9mNYPIbMTt3xNbaGL+mnYDh1ZM=, tarball: https://registry.npm.wps.cn/json5/download/json5-1.0.2.tgz}
    hasBin: true

  json5@2.2.3:
    resolution: {integrity: sha1-eM1vGhm9wStz21rQxh79ZsHikoM=, tarball: https://registry.npm.wps.cn/json5/download/json5-2.2.3.tgz}
    engines: {node: '>=6'}
    hasBin: true

  jsonfile@6.1.0:
    resolution: {integrity: sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=, tarball: https://registry.npm.wps.cn/jsonfile/download/jsonfile-6.1.0.tgz}

  jsonpath@1.1.1:
    resolution: {integrity: sha1-DKHtj7ZbszCSSMydVGbRLVsLmQE=, tarball: https://registry.npm.wps.cn/jsonpath/download/jsonpath-1.1.1.tgz}

  jsonpointer@5.0.1:
    resolution: {integrity: sha1-IRDgrwkA/TdGe1kH7NE6eIShtVk=, tarball: https://registry.npm.wps.cn/jsonpointer/download/jsonpointer-5.0.1.tgz}
    engines: {node: '>=0.10.0'}

  jsx-ast-utils@3.3.5:
    resolution: {integrity: sha1-R2a9BajioRryIr7NGeFVdeUqhTo=, tarball: https://registry.npm.wps.cn/jsx-ast-utils/download/jsx-ast-utils-3.3.5.tgz}
    engines: {node: '>=4.0'}

  keyv@4.5.4:
    resolution: {integrity: sha1-qHmpnilFL5QkOfKkBeOvizHU3pM=, tarball: https://registry.npm.wps.cn/keyv/download/keyv-4.5.4.tgz}

  kind-of@6.0.3:
    resolution: {integrity: sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=, tarball: https://registry.npm.wps.cn/kind-of/download/kind-of-6.0.3.tgz}
    engines: {node: '>=0.10.0'}

  kleur@3.0.3:
    resolution: {integrity: sha1-p5yezIbuHOP6YgbRIWxQHxR/wH4=, tarball: https://registry.npm.wps.cn/kleur/download/kleur-3.0.3.tgz}
    engines: {node: '>=6'}

  klona@2.0.6:
    resolution: {integrity: sha1-hb/7+BnAOy9TJwQSQgpFVe+ILiI=, tarball: https://registry.npm.wps.cn/klona/download/klona-2.0.6.tgz}
    engines: {node: '>= 8'}

  language-subtag-registry@0.3.23:
    resolution: {integrity: sha1-I1KeBNnjt0Z51wFC3z/S627Fcuc=, tarball: https://registry.npm.wps.cn/language-subtag-registry/download/language-subtag-registry-0.3.23.tgz}

  language-tags@1.0.9:
    resolution: {integrity: sha1-H/3NDsD6+0sb5/ixHzBq0PnAh3c=, tarball: https://registry.npm.wps.cn/language-tags/download/language-tags-1.0.9.tgz}
    engines: {node: '>=0.10'}

  launch-editor@2.10.0:
    resolution: {integrity: sha1-XKPt/LlmffHochMQ86QPESfUvEI=, tarball: https://registry.npm.wps.cn/launch-editor/download/launch-editor-2.10.0.tgz}

  less-loader@11.1.4:
    resolution: {integrity: sha1-6KBwhE76775ZuXisr1e50+hozwg=, tarball: https://registry.npm.wps.cn/less-loader/download/less-loader-11.1.4.tgz}
    engines: {node: '>= 14.15.0'}
    peerDependencies:
      less: ^3.5.0 || ^4.0.0
      webpack: ^5.0.0

  less-loader@7.3.0:
    resolution: {integrity: sha1-+dbTbRhznWQgZ6Bftb1wyMYTF+U=, tarball: https://registry.npm.wps.cn/less-loader/download/less-loader-7.3.0.tgz}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      less: ^3.5.0 || ^4.0.0
      webpack: ^4.0.0 || ^5.0.0

  less@4.3.0:
    resolution: {integrity: sha1-7wz8JgqcqAee2NDjUSvaihLILyo=, tarball: https://registry.npm.wps.cn/less/download/less-4.3.0.tgz}
    engines: {node: '>=14'}
    hasBin: true

  leven@3.1.0:
    resolution: {integrity: sha1-d4kd6DQGTMy6gq54QrtrFKE+1/I=, tarball: https://registry.npm.wps.cn/leven/download/leven-3.1.0.tgz}
    engines: {node: '>=6'}

  levn@0.3.0:
    resolution: {integrity: sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=, tarball: https://registry.npm.wps.cn/levn/download/levn-0.3.0.tgz}
    engines: {node: '>= 0.8.0'}

  levn@0.4.1:
    resolution: {integrity: sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=, tarball: https://registry.npm.wps.cn/levn/download/levn-0.4.1.tgz}
    engines: {node: '>= 0.8.0'}

  lilconfig@2.1.0:
    resolution: {integrity: sha1-eOI6yJ67fhv78lsYBD3nVlSOf1I=, tarball: https://registry.npm.wps.cn/lilconfig/download/lilconfig-2.1.0.tgz}
    engines: {node: '>=10'}

  lilconfig@3.1.3:
    resolution: {integrity: sha1-obz9Ylf5WFv1rhTO7rt7VZAl5MQ=, tarball: https://registry.npm.wps.cn/lilconfig/download/lilconfig-3.1.3.tgz}
    engines: {node: '>=14'}

  lines-and-columns@1.2.4:
    resolution: {integrity: sha1-7KKE910pZQeTCdwK2SVauy68FjI=, tarball: https://registry.npm.wps.cn/lines-and-columns/download/lines-and-columns-1.2.4.tgz}

  loader-runner@4.3.0:
    resolution: {integrity: sha1-wbShY7mfYUgwNTsWdV5xSawjFOE=, tarball: https://registry.npm.wps.cn/loader-runner/download/loader-runner-4.3.0.tgz}
    engines: {node: '>=6.11.5'}

  loader-utils@2.0.4:
    resolution: {integrity: sha1-i1yzi1w0qaAY7h/A5qBm0d/MUow=, tarball: https://registry.npm.wps.cn/loader-utils/download/loader-utils-2.0.4.tgz}
    engines: {node: '>=8.9.0'}

  loader-utils@3.3.1:
    resolution: {integrity: sha1-c1uaGf1jZIynrb0xwjJ9/igTBOU=, tarball: https://registry.npm.wps.cn/loader-utils/download/loader-utils-3.3.1.tgz}
    engines: {node: '>= 12.13.0'}

  locate-path@3.0.0:
    resolution: {integrity: sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=, tarball: https://registry.npm.wps.cn/locate-path/download/locate-path-3.0.0.tgz}
    engines: {node: '>=6'}

  locate-path@5.0.0:
    resolution: {integrity: sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=, tarball: https://registry.npm.wps.cn/locate-path/download/locate-path-5.0.0.tgz}
    engines: {node: '>=8'}

  locate-path@6.0.0:
    resolution: {integrity: sha1-VTIeswn+u8WcSAHZMackUqaB0oY=, tarball: https://registry.npm.wps.cn/locate-path/download/locate-path-6.0.0.tgz}
    engines: {node: '>=10'}

  lodash.debounce@4.0.8:
    resolution: {integrity: sha1-gteb/zCmfEAF/9XiUVMArZyk168=, tarball: https://registry.npm.wps.cn/lodash.debounce/download/lodash.debounce-4.0.8.tgz}

  lodash.memoize@4.1.2:
    resolution: {integrity: sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4=, tarball: https://registry.npm.wps.cn/lodash.memoize/download/lodash.memoize-4.1.2.tgz}

  lodash.merge@4.6.2:
    resolution: {integrity: sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=, tarball: https://registry.npm.wps.cn/lodash.merge/download/lodash.merge-4.6.2.tgz}

  lodash.sortby@4.7.0:
    resolution: {integrity: sha1-7dFMgk4sycHgsKG0K7UhBRakJDg=, tarball: https://registry.npm.wps.cn/lodash.sortby/download/lodash.sortby-4.7.0.tgz}

  lodash.uniq@4.5.0:
    resolution: {integrity: sha1-0CJTc662Uq3BvILklFM5qEJ1R3M=, tarball: https://registry.npm.wps.cn/lodash.uniq/download/lodash.uniq-4.5.0.tgz}

  lodash@4.17.21:
    resolution: {integrity: sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=, tarball: https://registry.npm.wps.cn/lodash/download/lodash-4.17.21.tgz}

  loose-envify@1.4.0:
    resolution: {integrity: sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=, tarball: https://registry.npm.wps.cn/loose-envify/download/loose-envify-1.4.0.tgz}
    hasBin: true

  lower-case@2.0.2:
    resolution: {integrity: sha1-b6I3xj29xKgsoP2ILkci3F5jTig=, tarball: https://registry.npm.wps.cn/lower-case/download/lower-case-2.0.2.tgz}

  lru-cache@10.4.3:
    resolution: {integrity: sha1-QQ/IoXtw5ZgBPfJXwkRrfzOD8Rk=, tarball: https://registry.npm.wps.cn/lru-cache/download/lru-cache-10.4.3.tgz}

  lru-cache@5.1.1:
    resolution: {integrity: sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=, tarball: https://registry.npm.wps.cn/lru-cache/download/lru-cache-5.1.1.tgz}

  lz-string@1.5.0:
    resolution: {integrity: sha1-watQ93iHtxJiEgG6n9Tjpu0JmUE=, tarball: https://registry.npm.wps.cn/lz-string/download/lz-string-1.5.0.tgz}
    hasBin: true

  magic-string@0.25.9:
    resolution: {integrity: sha1-3n+fr5HvihyR0CwuUxTIJ3283Rw=, tarball: https://registry.npm.wps.cn/magic-string/download/magic-string-0.25.9.tgz}

  make-dir@2.1.0:
    resolution: {integrity: sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU=, tarball: https://registry.npm.wps.cn/make-dir/download/make-dir-2.1.0.tgz}
    engines: {node: '>=6'}

  make-dir@3.1.0:
    resolution: {integrity: sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8=, tarball: https://registry.npm.wps.cn/make-dir/download/make-dir-3.1.0.tgz}
    engines: {node: '>=8'}

  make-dir@4.0.0:
    resolution: {integrity: sha1-w8IwencSd82WODBfkVwprnQbYU4=, tarball: https://registry.npm.wps.cn/make-dir/download/make-dir-4.0.0.tgz}
    engines: {node: '>=10'}

  make-error@1.3.6:
    resolution: {integrity: sha1-LrLjfqm2fEiR9oShOUeZr0hM96I=, tarball: https://registry.npm.wps.cn/make-error/download/make-error-1.3.6.tgz}

  makeerror@1.0.12:
    resolution: {integrity: sha1-Pl3SB5qC6BLpg8xmEMSiyw6qgBo=, tarball: https://registry.npm.wps.cn/makeerror/download/makeerror-1.0.12.tgz}

  math-intrinsics@1.1.0:
    resolution: {integrity: sha1-oN10voHiqlwvJ+Zc4oNgXuTit/k=, tarball: https://registry.npm.wps.cn/math-intrinsics/download/math-intrinsics-1.1.0.tgz}
    engines: {node: '>= 0.4'}

  mdn-data@2.0.14:
    resolution: {integrity: sha1-cRP8QoGRfWPOKbQ0RvcB5owlulA=, tarball: https://registry.npm.wps.cn/mdn-data/download/mdn-data-2.0.14.tgz}

  mdn-data@2.0.4:
    resolution: {integrity: sha1-aZs8OKxvHXKAkaZGULZdOIUC/Vs=, tarball: https://registry.npm.wps.cn/mdn-data/download/mdn-data-2.0.4.tgz}

  media-typer@0.3.0:
    resolution: {integrity: sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=, tarball: https://registry.npm.wps.cn/media-typer/download/media-typer-0.3.0.tgz}
    engines: {node: '>= 0.6'}

  memfs@3.5.3:
    resolution: {integrity: sha1-2bQP5PjVeIxfiVvagEzQ2e7unzs=, tarball: https://registry.npm.wps.cn/memfs/download/memfs-3.5.3.tgz}
    engines: {node: '>= 4.0.0'}

  merge-descriptors@1.0.3:
    resolution: {integrity: sha1-2AMZpl88eTU1Hlz9rI+TGFBNvtU=, tarball: https://registry.npm.wps.cn/merge-descriptors/download/merge-descriptors-1.0.3.tgz}

  merge-stream@2.0.0:
    resolution: {integrity: sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=, tarball: https://registry.npm.wps.cn/merge-stream/download/merge-stream-2.0.0.tgz}

  merge2@1.4.1:
    resolution: {integrity: sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=, tarball: https://registry.npm.wps.cn/merge2/download/merge2-1.4.1.tgz}
    engines: {node: '>= 8'}

  methods@1.1.2:
    resolution: {integrity: sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=, tarball: https://registry.npm.wps.cn/methods/download/methods-1.1.2.tgz}
    engines: {node: '>= 0.6'}

  micromatch@4.0.8:
    resolution: {integrity: sha1-1m+hjzpHB2eJMgubGvMr2G2fogI=, tarball: https://registry.npm.wps.cn/micromatch/download/micromatch-4.0.8.tgz}
    engines: {node: '>=8.6'}

  mime-db@1.52.0:
    resolution: {integrity: sha1-u6vNwChZ9JhzAchW4zh85exDv3A=, tarball: https://registry.npm.wps.cn/mime-db/download/mime-db-1.52.0.tgz}
    engines: {node: '>= 0.6'}

  mime-db@1.54.0:
    resolution: {integrity: sha1-zds+5PnGRTDf9kAjZmHULLajFPU=, tarball: https://registry.npm.wps.cn/mime-db/download/mime-db-1.54.0.tgz}
    engines: {node: '>= 0.6'}

  mime-types@2.1.35:
    resolution: {integrity: sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=, tarball: https://registry.npm.wps.cn/mime-types/download/mime-types-2.1.35.tgz}
    engines: {node: '>= 0.6'}

  mime@1.6.0:
    resolution: {integrity: sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=, tarball: https://registry.npm.wps.cn/mime/download/mime-1.6.0.tgz}
    engines: {node: '>=4'}
    hasBin: true

  mimic-fn@2.1.0:
    resolution: {integrity: sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=, tarball: https://registry.npm.wps.cn/mimic-fn/download/mimic-fn-2.1.0.tgz}
    engines: {node: '>=6'}

  min-indent@1.0.1:
    resolution: {integrity: sha1-pj9oFnOzBXH76LwlaGrnRu76mGk=, tarball: https://registry.npm.wps.cn/min-indent/download/min-indent-1.0.1.tgz}
    engines: {node: '>=4'}

  mini-css-extract-plugin@2.9.2:
    resolution: {integrity: sha1-lmAxtGiRelRG9MJKgIVLKUdQPFs=, tarball: https://registry.npm.wps.cn/mini-css-extract-plugin/download/mini-css-extract-plugin-2.9.2.tgz}
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      webpack: ^5.0.0

  minimalistic-assert@1.0.1:
    resolution: {integrity: sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc=, tarball: https://registry.npm.wps.cn/minimalistic-assert/download/minimalistic-assert-1.0.1.tgz}

  minimatch@3.1.2:
    resolution: {integrity: sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=, tarball: https://registry.npm.wps.cn/minimatch/download/minimatch-3.1.2.tgz}

  minimatch@5.1.6:
    resolution: {integrity: sha1-HPy4z1Ui6mmVLNKvla4JR38SKpY=, tarball: https://registry.npm.wps.cn/minimatch/download/minimatch-5.1.6.tgz}
    engines: {node: '>=10'}

  minimatch@9.0.5:
    resolution: {integrity: sha1-10+d1rV9g9jpjPuCEzsDl4vJKeU=, tarball: https://registry.npm.wps.cn/minimatch/download/minimatch-9.0.5.tgz}
    engines: {node: '>=16 || 14 >=14.17'}

  minimist@1.2.8:
    resolution: {integrity: sha1-waRk52kzAuCCoHXO4MBXdBrEdyw=, tarball: https://registry.npm.wps.cn/minimist/download/minimist-1.2.8.tgz}

  minipass@7.1.2:
    resolution: {integrity: sha1-k6libOXl5mvU24aEnnUV6SNApwc=, tarball: https://registry.npm.wps.cn/minipass/download/minipass-7.1.2.tgz}
    engines: {node: '>=16 || 14 >=14.17'}

  mkdirp@0.5.6:
    resolution: {integrity: sha1-fe8D0kMtyuS6HWEURcSDlgYiVfY=, tarball: https://registry.npm.wps.cn/mkdirp/download/mkdirp-0.5.6.tgz}
    hasBin: true

  ms@2.0.0:
    resolution: {integrity: sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=, tarball: https://registry.npm.wps.cn/ms/download/ms-2.0.0.tgz}

  ms@2.1.3:
    resolution: {integrity: sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=, tarball: https://registry.npm.wps.cn/ms/download/ms-2.1.3.tgz}

  multicast-dns@7.2.5:
    resolution: {integrity: sha1-d+tGBX9NetvRbZKQ+nKZ9vpkzO0=, tarball: https://registry.npm.wps.cn/multicast-dns/download/multicast-dns-7.2.5.tgz}
    hasBin: true

  mz@2.7.0:
    resolution: {integrity: sha1-lQCAV6Vsr63CvGPd5/n/aVWUjjI=, tarball: https://registry.npm.wps.cn/mz/download/mz-2.7.0.tgz}

  nanoid@3.3.11:
    resolution: {integrity: sha1-T08RLO++MDIC8hmYOBKJNiZtGFs=, tarball: https://registry.npm.wps.cn/nanoid/download/nanoid-3.3.11.tgz}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  natural-compare-lite@1.4.0:
    resolution: {integrity: sha1-F7CVgZiJef3a/gIB6TG6kzyWy7Q=, tarball: https://registry.npm.wps.cn/natural-compare-lite/download/natural-compare-lite-1.4.0.tgz}

  natural-compare@1.4.0:
    resolution: {integrity: sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=, tarball: https://registry.npm.wps.cn/natural-compare/download/natural-compare-1.4.0.tgz}

  needle@3.3.1:
    resolution: {integrity: sha1-Y/da7FgMLnfiCfPzJOLN89Kb0Ek=, tarball: https://registry.npm.wps.cn/needle/download/needle-3.3.1.tgz}
    engines: {node: '>= 4.4.x'}
    hasBin: true

  negotiator@0.6.3:
    resolution: {integrity: sha1-WOMjpy/twNb5zU0x/kn1FHlZDM0=, tarball: https://registry.npm.wps.cn/negotiator/download/negotiator-0.6.3.tgz}
    engines: {node: '>= 0.6'}

  negotiator@0.6.4:
    resolution: {integrity: sha1-d3lI4kUmUcVwtxLdAcI+JicT//c=, tarball: https://registry.npm.wps.cn/negotiator/download/negotiator-0.6.4.tgz}
    engines: {node: '>= 0.6'}

  neo-async@2.6.2:
    resolution: {integrity: sha1-tKr7k+OustgXTKU88WOrfXMIMF8=, tarball: https://registry.npm.wps.cn/neo-async/download/neo-async-2.6.2.tgz}

  no-case@3.0.4:
    resolution: {integrity: sha1-02H9XJgA9VhVGoNp/A3NRmK2Ek0=, tarball: https://registry.npm.wps.cn/no-case/download/no-case-3.0.4.tgz}

  node-forge@1.3.1:
    resolution: {integrity: sha1-vo2iryQ7JBfV9kancGY6krfp3tM=, tarball: https://registry.npm.wps.cn/node-forge/download/node-forge-1.3.1.tgz}
    engines: {node: '>= 6.13.0'}

  node-int64@0.4.0:
    resolution: {integrity: sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=, tarball: https://registry.npm.wps.cn/node-int64/download/node-int64-0.4.0.tgz}

  node-releases@2.0.19:
    resolution: {integrity: sha1-nkRaUpUJUexNF32EOvNwtBHK8xQ=, tarball: https://registry.npm.wps.cn/node-releases/download/node-releases-2.0.19.tgz}

  normalize-path@3.0.0:
    resolution: {integrity: sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=, tarball: https://registry.npm.wps.cn/normalize-path/download/normalize-path-3.0.0.tgz}
    engines: {node: '>=0.10.0'}

  normalize-range@0.1.2:
    resolution: {integrity: sha1-LRDAa9/TEuqXd2laTShDlFa3WUI=, tarball: https://registry.npm.wps.cn/normalize-range/download/normalize-range-0.1.2.tgz}
    engines: {node: '>=0.10.0'}

  normalize-url@6.1.0:
    resolution: {integrity: sha1-QNCIW1Nd7/4/MUe+yHfQX+TFZoo=, tarball: https://registry.npm.wps.cn/normalize-url/download/normalize-url-6.1.0.tgz}
    engines: {node: '>=10'}

  npm-run-path@4.0.1:
    resolution: {integrity: sha1-t+zR5e1T2o43pV4cImnguX7XSOo=, tarball: https://registry.npm.wps.cn/npm-run-path/download/npm-run-path-4.0.1.tgz}
    engines: {node: '>=8'}

  nth-check@1.0.2:
    resolution: {integrity: sha1-sr0pXDfj3VijvwcAN2Zjuk2c8Fw=, tarball: https://registry.npm.wps.cn/nth-check/download/nth-check-1.0.2.tgz}

  nth-check@2.1.1:
    resolution: {integrity: sha1-yeq0KO/842zWuSySS9sADvHx7R0=, tarball: https://registry.npm.wps.cn/nth-check/download/nth-check-2.1.1.tgz}

  nwsapi@2.2.20:
    resolution: {integrity: sha1-IuUyU8Yeew5+k870LIkRVLzKEe8=, tarball: https://registry.npm.wps.cn/nwsapi/download/nwsapi-2.2.20.tgz}

  object-assign@4.1.1:
    resolution: {integrity: sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=, tarball: https://registry.npm.wps.cn/object-assign/download/object-assign-4.1.1.tgz}
    engines: {node: '>=0.10.0'}

  object-hash@3.0.0:
    resolution: {integrity: sha1-c/l/dT57r/wOLMnW4HkHl0Ssguk=, tarball: https://registry.npm.wps.cn/object-hash/download/object-hash-3.0.0.tgz}
    engines: {node: '>= 6'}

  object-inspect@1.13.4:
    resolution: {integrity: sha1-g3UmXiG8IND6WCwi4bE0hdbgAhM=, tarball: https://registry.npm.wps.cn/object-inspect/download/object-inspect-1.13.4.tgz}
    engines: {node: '>= 0.4'}

  object-is@1.1.6:
    resolution: {integrity: sha1-GmpTrtLdj35ndf+HC+pYVFlWqwc=, tarball: https://registry.npm.wps.cn/object-is/download/object-is-1.1.6.tgz}
    engines: {node: '>= 0.4'}

  object-keys@1.1.1:
    resolution: {integrity: sha1-HEfyct8nfzsdrwYWd9nILiMixg4=, tarball: https://registry.npm.wps.cn/object-keys/download/object-keys-1.1.1.tgz}
    engines: {node: '>= 0.4'}

  object.assign@4.1.7:
    resolution: {integrity: sha1-jBTKGkJMalYbC7KiL2b1BJqUXT0=, tarball: https://registry.npm.wps.cn/object.assign/download/object.assign-4.1.7.tgz}
    engines: {node: '>= 0.4'}

  object.entries@1.1.9:
    resolution: {integrity: sha1-5HcKahREr7Yb05+YQBi1vt4l+LM=, tarball: https://registry.npm.wps.cn/object.entries/download/object.entries-1.1.9.tgz}
    engines: {node: '>= 0.4'}

  object.fromentries@2.0.8:
    resolution: {integrity: sha1-9xldipuXvZXLwZmeqTns0aKwDGU=, tarball: https://registry.npm.wps.cn/object.fromentries/download/object.fromentries-2.0.8.tgz}
    engines: {node: '>= 0.4'}

  object.getownpropertydescriptors@2.1.8:
    resolution: {integrity: sha1-Lx/gYG7Bp2WBVMzU9yhQT2lmeSM=, tarball: https://registry.npm.wps.cn/object.getownpropertydescriptors/download/object.getownpropertydescriptors-2.1.8.tgz}
    engines: {node: '>= 0.8'}

  object.groupby@1.0.3:
    resolution: {integrity: sha1-mxJcNiOBKfb3thlUoecXYUjVAC4=, tarball: https://registry.npm.wps.cn/object.groupby/download/object.groupby-1.0.3.tgz}
    engines: {node: '>= 0.4'}

  object.values@1.2.1:
    resolution: {integrity: sha1-3u1SClCAn/f3Wnz9S8ZMegOMYhY=, tarball: https://registry.npm.wps.cn/object.values/download/object.values-1.2.1.tgz}
    engines: {node: '>= 0.4'}

  obuf@1.1.2:
    resolution: {integrity: sha1-Cb6jND1BhZ69RGKS0RydTbYZCE4=, tarball: https://registry.npm.wps.cn/obuf/download/obuf-1.1.2.tgz}

  on-finished@2.4.1:
    resolution: {integrity: sha1-WMjEQRblSEWtV/FKsQsDUzGErD8=, tarball: https://registry.npm.wps.cn/on-finished/download/on-finished-2.4.1.tgz}
    engines: {node: '>= 0.8'}

  on-headers@1.0.2:
    resolution: {integrity: sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8=, tarball: https://registry.npm.wps.cn/on-headers/download/on-headers-1.0.2.tgz}
    engines: {node: '>= 0.8'}

  once@1.4.0:
    resolution: {integrity: sha1-WDsap3WWHUsROsF9nFC6753Xa9E=, tarball: https://registry.npm.wps.cn/once/download/once-1.4.0.tgz}

  onetime@5.1.2:
    resolution: {integrity: sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=, tarball: https://registry.npm.wps.cn/onetime/download/onetime-5.1.2.tgz}
    engines: {node: '>=6'}

  open@8.4.2:
    resolution: {integrity: sha1-W1/+Ko95Pc0qrXPlUMuHtZywhPk=, tarball: https://registry.npm.wps.cn/open/download/open-8.4.2.tgz}
    engines: {node: '>=12'}

  optionator@0.8.3:
    resolution: {integrity: sha1-hPodA2/p08fiHZmIS2ARZ+yPtJU=, tarball: https://registry.npm.wps.cn/optionator/download/optionator-0.8.3.tgz}
    engines: {node: '>= 0.8.0'}

  optionator@0.9.4:
    resolution: {integrity: sha1-fqHBpdkddk+yghOciP4R4YKjpzQ=, tarball: https://registry.npm.wps.cn/optionator/download/optionator-0.9.4.tgz}
    engines: {node: '>= 0.8.0'}

  own-keys@1.0.1:
    resolution: {integrity: sha1-5ABpEKK/kTWFKJZ27r1vOQz1E1g=, tarball: https://registry.npm.wps.cn/own-keys/download/own-keys-1.0.1.tgz}
    engines: {node: '>= 0.4'}

  p-limit@2.3.0:
    resolution: {integrity: sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=, tarball: https://registry.npm.wps.cn/p-limit/download/p-limit-2.3.0.tgz}
    engines: {node: '>=6'}

  p-limit@3.1.0:
    resolution: {integrity: sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=, tarball: https://registry.npm.wps.cn/p-limit/download/p-limit-3.1.0.tgz}
    engines: {node: '>=10'}

  p-locate@3.0.0:
    resolution: {integrity: sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=, tarball: https://registry.npm.wps.cn/p-locate/download/p-locate-3.0.0.tgz}
    engines: {node: '>=6'}

  p-locate@4.1.0:
    resolution: {integrity: sha1-o0KLtwiLOmApL2aRkni3wpetTwc=, tarball: https://registry.npm.wps.cn/p-locate/download/p-locate-4.1.0.tgz}
    engines: {node: '>=8'}

  p-locate@5.0.0:
    resolution: {integrity: sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=, tarball: https://registry.npm.wps.cn/p-locate/download/p-locate-5.0.0.tgz}
    engines: {node: '>=10'}

  p-retry@4.6.2:
    resolution: {integrity: sha1-m6rnGEBX7dThcjHO4EJkEG4JKhY=, tarball: https://registry.npm.wps.cn/p-retry/download/p-retry-4.6.2.tgz}
    engines: {node: '>=8'}

  p-try@2.2.0:
    resolution: {integrity: sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=, tarball: https://registry.npm.wps.cn/p-try/download/p-try-2.2.0.tgz}
    engines: {node: '>=6'}

  package-json-from-dist@1.0.1:
    resolution: {integrity: sha1-TxRxoBCCeob5TP2bByfjbSZ95QU=, tarball: https://registry.npm.wps.cn/package-json-from-dist/download/package-json-from-dist-1.0.1.tgz}

  param-case@3.0.4:
    resolution: {integrity: sha1-fRf+SqEr3jTUp32RrPtiGcqtAcU=, tarball: https://registry.npm.wps.cn/param-case/download/param-case-3.0.4.tgz}

  parent-module@1.0.1:
    resolution: {integrity: sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=, tarball: https://registry.npm.wps.cn/parent-module/download/parent-module-1.0.1.tgz}
    engines: {node: '>=6'}

  parse-json@5.2.0:
    resolution: {integrity: sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=, tarball: https://registry.npm.wps.cn/parse-json/download/parse-json-5.2.0.tgz}
    engines: {node: '>=8'}

  parse-node-version@1.0.1:
    resolution: {integrity: sha1-4rXb7eAOf6m8NjYH9TMn6LBzGJs=, tarball: https://registry.npm.wps.cn/parse-node-version/download/parse-node-version-1.0.1.tgz}
    engines: {node: '>= 0.10'}

  parse5@6.0.1:
    resolution: {integrity: sha1-4aHAhcVps9wIMhGE8Zo5zCf3wws=, tarball: https://registry.npm.wps.cn/parse5/download/parse5-6.0.1.tgz}

  parseurl@1.3.3:
    resolution: {integrity: sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=, tarball: https://registry.npm.wps.cn/parseurl/download/parseurl-1.3.3.tgz}
    engines: {node: '>= 0.8'}

  pascal-case@3.1.2:
    resolution: {integrity: sha1-tI4O8rmOIF58Ha50fQsVCCN2YOs=, tarball: https://registry.npm.wps.cn/pascal-case/download/pascal-case-3.1.2.tgz}

  path-exists@3.0.0:
    resolution: {integrity: sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=, tarball: https://registry.npm.wps.cn/path-exists/download/path-exists-3.0.0.tgz}
    engines: {node: '>=4'}

  path-exists@4.0.0:
    resolution: {integrity: sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=, tarball: https://registry.npm.wps.cn/path-exists/download/path-exists-4.0.0.tgz}
    engines: {node: '>=8'}

  path-is-absolute@1.0.1:
    resolution: {integrity: sha1-F0uSaHNVNP+8es5r9TpanhtcX18=, tarball: https://registry.npm.wps.cn/path-is-absolute/download/path-is-absolute-1.0.1.tgz}
    engines: {node: '>=0.10.0'}

  path-key@3.1.1:
    resolution: {integrity: sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=, tarball: https://registry.npm.wps.cn/path-key/download/path-key-3.1.1.tgz}
    engines: {node: '>=8'}

  path-parse@1.0.7:
    resolution: {integrity: sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=, tarball: https://registry.npm.wps.cn/path-parse/download/path-parse-1.0.7.tgz}

  path-scurry@1.11.1:
    resolution: {integrity: sha1-eWCmaIiFlKByCxKpEdGnQqufEdI=, tarball: https://registry.npm.wps.cn/path-scurry/download/path-scurry-1.11.1.tgz}
    engines: {node: '>=16 || 14 >=14.18'}

  path-to-regexp@0.1.12:
    resolution: {integrity: sha1-1eGhLkeKl21DLvPFjVNLmSMWS7c=, tarball: https://registry.npm.wps.cn/path-to-regexp/download/path-to-regexp-0.1.12.tgz}

  path-type@4.0.0:
    resolution: {integrity: sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=, tarball: https://registry.npm.wps.cn/path-type/download/path-type-4.0.0.tgz}
    engines: {node: '>=8'}

  performance-now@2.1.0:
    resolution: {integrity: sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=, tarball: https://registry.npm.wps.cn/performance-now/download/performance-now-2.1.0.tgz}

  picocolors@0.2.1:
    resolution: {integrity: sha1-VwZw95NkaFHRuhNZlpYqutWHhZ8=, tarball: https://registry.npm.wps.cn/picocolors/download/picocolors-0.2.1.tgz}

  picocolors@1.1.1:
    resolution: {integrity: sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=, tarball: https://registry.npm.wps.cn/picocolors/download/picocolors-1.1.1.tgz}

  picomatch@2.3.1:
    resolution: {integrity: sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=, tarball: https://registry.npm.wps.cn/picomatch/download/picomatch-2.3.1.tgz}
    engines: {node: '>=8.6'}

  pify@2.3.0:
    resolution: {integrity: sha1-7RQaasBDqEnqWISY59yosVMw6Qw=, tarball: https://registry.npm.wps.cn/pify/download/pify-2.3.0.tgz}
    engines: {node: '>=0.10.0'}

  pify@4.0.1:
    resolution: {integrity: sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE=, tarball: https://registry.npm.wps.cn/pify/download/pify-4.0.1.tgz}
    engines: {node: '>=6'}

  pirates@4.0.7:
    resolution: {integrity: sha1-ZDtKGMQlfIplEEtz8wSc6aChXiI=, tarball: https://registry.npm.wps.cn/pirates/download/pirates-4.0.7.tgz}
    engines: {node: '>= 6'}

  pkg-dir@4.2.0:
    resolution: {integrity: sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=, tarball: https://registry.npm.wps.cn/pkg-dir/download/pkg-dir-4.2.0.tgz}
    engines: {node: '>=8'}

  pkg-up@3.1.0:
    resolution: {integrity: sha1-EA7CNcwVDk/UJRlBJZaihRKg3vU=, tarball: https://registry.npm.wps.cn/pkg-up/download/pkg-up-3.1.0.tgz}
    engines: {node: '>=8'}

  possible-typed-array-names@1.1.0:
    resolution: {integrity: sha1-k+NYK8DlQmWG2dB7ee5A/IQd5K4=, tarball: https://registry.npm.wps.cn/possible-typed-array-names/download/possible-typed-array-names-1.1.0.tgz}
    engines: {node: '>= 0.4'}

  postcss-attribute-case-insensitive@5.0.2:
    resolution: {integrity: sha1-A9dhskr8BMCedX6S/1NxaujqJ0E=, tarball: https://registry.npm.wps.cn/postcss-attribute-case-insensitive/download/postcss-attribute-case-insensitive-5.0.2.tgz}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  postcss-browser-comments@4.0.0:
    resolution: {integrity: sha1-vPyGE031gH9dPA7voZHUITa15yo=, tarball: https://registry.npm.wps.cn/postcss-browser-comments/download/postcss-browser-comments-4.0.0.tgz}
    engines: {node: '>=8'}
    peerDependencies:
      browserslist: '>=4'
      postcss: '>=8'

  postcss-calc@8.2.4:
    resolution: {integrity: sha1-d7nCm/y+igf/ZpPchwUIKIiXOaU=, tarball: https://registry.npm.wps.cn/postcss-calc/download/postcss-calc-8.2.4.tgz}
    peerDependencies:
      postcss: ^8.2.2

  postcss-clamp@4.1.0:
    resolution: {integrity: sha1-cmPpWrrdjCuhvZEbC1pcnJPgI2M=, tarball: https://registry.npm.wps.cn/postcss-clamp/download/postcss-clamp-4.1.0.tgz}
    engines: {node: '>=7.6.0'}
    peerDependencies:
      postcss: ^8.4.6

  postcss-color-functional-notation@4.2.4:
    resolution: {integrity: sha1-IakJ6NdFTTYS0WWeRxzkaW8oyuw=, tarball: https://registry.npm.wps.cn/postcss-color-functional-notation/download/postcss-color-functional-notation-4.2.4.tgz}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  postcss-color-hex-alpha@8.0.4:
    resolution: {integrity: sha1-xm4pgPL7waY/WweWYzQM6LVfJaU=, tarball: https://registry.npm.wps.cn/postcss-color-hex-alpha/download/postcss-color-hex-alpha-8.0.4.tgz}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.4

  postcss-color-rebeccapurple@7.1.1:
    resolution: {integrity: sha1-Y/2rkdh468TdS3wCYZoMPWpWztA=, tarball: https://registry.npm.wps.cn/postcss-color-rebeccapurple/download/postcss-color-rebeccapurple-7.1.1.tgz}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  postcss-colormin@5.3.1:
    resolution: {integrity: sha1-hsJ8Ju1roA2Wx54I8/+0GNHRmI8=, tarball: https://registry.npm.wps.cn/postcss-colormin/download/postcss-colormin-5.3.1.tgz}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-convert-values@5.1.3:
    resolution: {integrity: sha1-BJmLubprZaoxA11mmmrzQsX505M=, tarball: https://registry.npm.wps.cn/postcss-convert-values/download/postcss-convert-values-5.1.3.tgz}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-custom-media@8.0.2:
    resolution: {integrity: sha1-yPljft9F/vdhsBTAJM7gE/gFKeo=, tarball: https://registry.npm.wps.cn/postcss-custom-media/download/postcss-custom-media-8.0.2.tgz}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.3

  postcss-custom-properties@12.1.11:
    resolution: {integrity: sha1-0Uu5s5iaxNQKqg4RC0O+Z6x4Rc8=, tarball: https://registry.npm.wps.cn/postcss-custom-properties/download/postcss-custom-properties-12.1.11.tgz}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  postcss-custom-selectors@6.0.3:
    resolution: {integrity: sha1-GrRoTWXzD+0XVSD4LSI9sDNyOdk=, tarball: https://registry.npm.wps.cn/postcss-custom-selectors/download/postcss-custom-selectors-6.0.3.tgz}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.3

  postcss-dir-pseudo-class@6.0.5:
    resolution: {integrity: sha1-K/Md5d52rd7UTgol7PYK6ffHwmw=, tarball: https://registry.npm.wps.cn/postcss-dir-pseudo-class/download/postcss-dir-pseudo-class-6.0.5.tgz}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  postcss-discard-comments@5.1.2:
    resolution: {integrity: sha1-jfXoHSklryeAB1hAwVJvBmDlNpY=, tarball: https://registry.npm.wps.cn/postcss-discard-comments/download/postcss-discard-comments-5.1.2.tgz}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-discard-duplicates@5.1.0:
    resolution: {integrity: sha1-nrT+hFZwak7r1tO3t3fQe60D6Eg=, tarball: https://registry.npm.wps.cn/postcss-discard-duplicates/download/postcss-discard-duplicates-5.1.0.tgz}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-discard-empty@5.1.1:
    resolution: {integrity: sha1-5XdiND/39QP+U/ylU9GNfww2nGw=, tarball: https://registry.npm.wps.cn/postcss-discard-empty/download/postcss-discard-empty-5.1.1.tgz}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-discard-overridden@5.1.0:
    resolution: {integrity: sha1-foxbUzJXR+nZATG7iGNSgvtKJ24=, tarball: https://registry.npm.wps.cn/postcss-discard-overridden/download/postcss-discard-overridden-5.1.0.tgz}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-double-position-gradients@3.1.2:
    resolution: {integrity: sha1-uWMY/bR3vpWZfobt0pxuNVekm5E=, tarball: https://registry.npm.wps.cn/postcss-double-position-gradients/download/postcss-double-position-gradients-3.1.2.tgz}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  postcss-env-function@4.0.6:
    resolution: {integrity: sha1-ey0kyBL1QO1u2kyB9gkEFnIqjno=, tarball: https://registry.npm.wps.cn/postcss-env-function/download/postcss-env-function-4.0.6.tgz}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.4

  postcss-flexbugs-fixes@5.0.2:
    resolution: {integrity: sha1-ICjhRTEwdPyavidst8oU5UAetJ0=, tarball: https://registry.npm.wps.cn/postcss-flexbugs-fixes/download/postcss-flexbugs-fixes-5.0.2.tgz}
    peerDependencies:
      postcss: ^8.1.4

  postcss-focus-visible@6.0.4:
    resolution: {integrity: sha1-UMnqmvoO5lf7dWNfq60l4Y12v54=, tarball: https://registry.npm.wps.cn/postcss-focus-visible/download/postcss-focus-visible-6.0.4.tgz}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.4

  postcss-focus-within@5.0.4:
    resolution: {integrity: sha1-Wx0uxgMZXzNEtxbAt19h5E6NLiA=, tarball: https://registry.npm.wps.cn/postcss-focus-within/download/postcss-focus-within-5.0.4.tgz}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.4

  postcss-font-variant@5.0.0:
    resolution: {integrity: sha1-79WbS36ouwYSfy0DG/u38k0y+mY=, tarball: https://registry.npm.wps.cn/postcss-font-variant/download/postcss-font-variant-5.0.0.tgz}
    peerDependencies:
      postcss: ^8.1.0

  postcss-gap-properties@3.0.5:
    resolution: {integrity: sha1-9+PN3Pc+4Z6UzPfLd3c/lWCqL/8=, tarball: https://registry.npm.wps.cn/postcss-gap-properties/download/postcss-gap-properties-3.0.5.tgz}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  postcss-image-set-function@4.0.7:
    resolution: {integrity: sha1-CDU711bxy/s7bpMYLHgph5EUSB8=, tarball: https://registry.npm.wps.cn/postcss-image-set-function/download/postcss-image-set-function-4.0.7.tgz}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  postcss-import@15.1.0:
    resolution: {integrity: sha1-QcZO2MwOI3NalpizJJ/9v3BK3HA=, tarball: https://registry.npm.wps.cn/postcss-import/download/postcss-import-15.1.0.tgz}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      postcss: ^8.0.0

  postcss-initial@4.0.1:
    resolution: {integrity: sha1-Up9zX3LFckoPswUn32+3rFTX3kI=, tarball: https://registry.npm.wps.cn/postcss-initial/download/postcss-initial-4.0.1.tgz}
    peerDependencies:
      postcss: ^8.0.0

  postcss-js@4.0.1:
    resolution: {integrity: sha1-YVmBhvNwO6sFLxxPfYBfOZG+6dI=, tarball: https://registry.npm.wps.cn/postcss-js/download/postcss-js-4.0.1.tgz}
    engines: {node: ^12 || ^14 || >= 16}
    peerDependencies:
      postcss: ^8.4.21

  postcss-lab-function@4.2.1:
    resolution: {integrity: sha1-b+TAFRAv980n0b1ThVgvZ+vb3Jg=, tarball: https://registry.npm.wps.cn/postcss-lab-function/download/postcss-lab-function-4.2.1.tgz}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  postcss-load-config@4.0.2:
    resolution: {integrity: sha1-cVnc9iYRjTPimfSF1q/kr/fEo+M=, tarball: https://registry.npm.wps.cn/postcss-load-config/download/postcss-load-config-4.0.2.tgz}
    engines: {node: '>= 14'}
    peerDependencies:
      postcss: '>=8.0.9'
      ts-node: '>=9.0.0'
    peerDependenciesMeta:
      postcss:
        optional: true
      ts-node:
        optional: true

  postcss-loader@6.2.1:
    resolution: {integrity: sha1-CJX3NGsXAhA9MP3Gbk1JSpPACO8=, tarball: https://registry.npm.wps.cn/postcss-loader/download/postcss-loader-6.2.1.tgz}
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      postcss: ^7.0.0 || ^8.0.1
      webpack: ^5.0.0

  postcss-logical@5.0.4:
    resolution: {integrity: sha1-7HWx7lRCGswE1ZIVdrfY22sOb3M=, tarball: https://registry.npm.wps.cn/postcss-logical/download/postcss-logical-5.0.4.tgz}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.4

  postcss-media-minmax@5.0.0:
    resolution: {integrity: sha1-cUC93sFz4tbWV+29hVSlV5TipbU=, tarball: https://registry.npm.wps.cn/postcss-media-minmax/download/postcss-media-minmax-5.0.0.tgz}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      postcss: ^8.1.0

  postcss-merge-longhand@5.1.7:
    resolution: {integrity: sha1-JKG99ALZ7w5w9Wjzm9wDRNVo+xY=, tarball: https://registry.npm.wps.cn/postcss-merge-longhand/download/postcss-merge-longhand-5.1.7.tgz}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-merge-rules@5.1.4:
    resolution: {integrity: sha1-Lyb6XKy3WxQC4hN4n2dmrl5AMTw=, tarball: https://registry.npm.wps.cn/postcss-merge-rules/download/postcss-merge-rules-5.1.4.tgz}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-minify-font-values@5.1.0:
    resolution: {integrity: sha1-8d8AFKcmCD0mDTvYXXOF+4nR8Bs=, tarball: https://registry.npm.wps.cn/postcss-minify-font-values/download/postcss-minify-font-values-5.1.0.tgz}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-minify-gradients@5.1.1:
    resolution: {integrity: sha1-8f4bT0mBNKUGgkDC8l1G/NI2uiw=, tarball: https://registry.npm.wps.cn/postcss-minify-gradients/download/postcss-minify-gradients-5.1.1.tgz}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-minify-params@5.1.4:
    resolution: {integrity: sha1-wGpseHEosyCLOMk2TPxAyKpdc1I=, tarball: https://registry.npm.wps.cn/postcss-minify-params/download/postcss-minify-params-5.1.4.tgz}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-minify-selectors@5.2.1:
    resolution: {integrity: sha1-1OfmtGFHuBF+qTJakVqAHV/mVsY=, tarball: https://registry.npm.wps.cn/postcss-minify-selectors/download/postcss-minify-selectors-5.2.1.tgz}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-modules-extract-imports@3.1.0:
    resolution: {integrity: sha1-tEl8uFqcDEtaq+t1m7JejYnxUAI=, tarball: https://registry.npm.wps.cn/postcss-modules-extract-imports/download/postcss-modules-extract-imports-3.1.0.tgz}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0

  postcss-modules-local-by-default@4.2.0:
    resolution: {integrity: sha1-0VD0ODeDHa4l5AhVluhPb11uw2g=, tarball: https://registry.npm.wps.cn/postcss-modules-local-by-default/download/postcss-modules-local-by-default-4.2.0.tgz}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0

  postcss-modules-scope@3.2.1:
    resolution: {integrity: sha1-G7zN3LOY8delEeCi0dBHcYr0B4w=, tarball: https://registry.npm.wps.cn/postcss-modules-scope/download/postcss-modules-scope-3.2.1.tgz}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0

  postcss-modules-values@4.0.0:
    resolution: {integrity: sha1-18Xn5ow7s8myfL9Iyguz/7RgLJw=, tarball: https://registry.npm.wps.cn/postcss-modules-values/download/postcss-modules-values-4.0.0.tgz}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0

  postcss-nested@6.2.0:
    resolution: {integrity: sha1-TC0iq18gucth4sXFkVlQeE0GgTE=, tarball: https://registry.npm.wps.cn/postcss-nested/download/postcss-nested-6.2.0.tgz}
    engines: {node: '>=12.0'}
    peerDependencies:
      postcss: ^8.2.14

  postcss-nesting@10.2.0:
    resolution: {integrity: sha1-CxLODbjt/S2K4Kr4ZCc3C4mIkL4=, tarball: https://registry.npm.wps.cn/postcss-nesting/download/postcss-nesting-10.2.0.tgz}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  postcss-normalize-charset@5.1.0:
    resolution: {integrity: sha1-kwLeCykJS1LCWemyz43Ah5h58O0=, tarball: https://registry.npm.wps.cn/postcss-normalize-charset/download/postcss-normalize-charset-5.1.0.tgz}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-normalize-display-values@5.1.0:
    resolution: {integrity: sha1-cqu65YCBlg6e3XIA/PIauDJcPag=, tarball: https://registry.npm.wps.cn/postcss-normalize-display-values/download/postcss-normalize-display-values-5.1.0.tgz}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-normalize-positions@5.1.1:
    resolution: {integrity: sha1-75cnnYlAh7WTJbRcR/HoY9rvu5I=, tarball: https://registry.npm.wps.cn/postcss-normalize-positions/download/postcss-normalize-positions-5.1.1.tgz}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-normalize-repeat-style@5.1.1:
    resolution: {integrity: sha1-6euWgFIE9HZt9m/QntLhNUVCD7I=, tarball: https://registry.npm.wps.cn/postcss-normalize-repeat-style/download/postcss-normalize-repeat-style-5.1.1.tgz}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-normalize-string@5.1.0:
    resolution: {integrity: sha1-QRlhFp4HMIyCwfjFXz6KM3dX4ig=, tarball: https://registry.npm.wps.cn/postcss-normalize-string/download/postcss-normalize-string-5.1.0.tgz}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-normalize-timing-functions@5.1.0:
    resolution: {integrity: sha1-1WFEEPjwsjiOnyQKpgEbpvUtr7s=, tarball: https://registry.npm.wps.cn/postcss-normalize-timing-functions/download/postcss-normalize-timing-functions-5.1.0.tgz}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-normalize-unicode@5.1.1:
    resolution: {integrity: sha1-9nKX/KP+p/F+DSyqQHaa/Eh6oDA=, tarball: https://registry.npm.wps.cn/postcss-normalize-unicode/download/postcss-normalize-unicode-5.1.1.tgz}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-normalize-url@5.1.0:
    resolution: {integrity: sha1-7Z2IyoLiGr75n3Q0V9NymgQq3Nw=, tarball: https://registry.npm.wps.cn/postcss-normalize-url/download/postcss-normalize-url-5.1.0.tgz}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-normalize-whitespace@5.1.1:
    resolution: {integrity: sha1-CKGg0f+henzG7+HmydqWnMRJPPo=, tarball: https://registry.npm.wps.cn/postcss-normalize-whitespace/download/postcss-normalize-whitespace-5.1.1.tgz}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-normalize@10.0.1:
    resolution: {integrity: sha1-RkaSZ2tSeSoGsGiAoXYnkhZUDdc=, tarball: https://registry.npm.wps.cn/postcss-normalize/download/postcss-normalize-10.0.1.tgz}
    engines: {node: '>= 12'}
    peerDependencies:
      browserslist: '>= 4'
      postcss: '>= 8'

  postcss-opacity-percentage@1.1.3:
    resolution: {integrity: sha1-W4mzVVGlVuIMXSPrUmD7/PUkXaY=, tarball: https://registry.npm.wps.cn/postcss-opacity-percentage/download/postcss-opacity-percentage-1.1.3.tgz}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  postcss-ordered-values@5.1.3:
    resolution: {integrity: sha1-tv0r0Q+TeyPYa8gpxp53Ms526jg=, tarball: https://registry.npm.wps.cn/postcss-ordered-values/download/postcss-ordered-values-5.1.3.tgz}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-overflow-shorthand@3.0.4:
    resolution: {integrity: sha1-ftZIb+xEt28OqxWqSGbNpdVdiT4=, tarball: https://registry.npm.wps.cn/postcss-overflow-shorthand/download/postcss-overflow-shorthand-3.0.4.tgz}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  postcss-page-break@3.0.4:
    resolution: {integrity: sha1-f790HCM2IWIraNQ1ur+3DdjB7l8=, tarball: https://registry.npm.wps.cn/postcss-page-break/download/postcss-page-break-3.0.4.tgz}
    peerDependencies:
      postcss: ^8

  postcss-place@7.0.5:
    resolution: {integrity: sha1-ldv4X9llajpuYOgytYCZFCNphsQ=, tarball: https://registry.npm.wps.cn/postcss-place/download/postcss-place-7.0.5.tgz}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  postcss-preset-env@7.8.3:
    resolution: {integrity: sha1-KlD15hLDFJzHr3VjTiAqWyrU8eI=, tarball: https://registry.npm.wps.cn/postcss-preset-env/download/postcss-preset-env-7.8.3.tgz}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  postcss-pseudo-class-any-link@7.1.6:
    resolution: {integrity: sha1-JpOyIZAtp3LCeN74Wk2aZLbmF6s=, tarball: https://registry.npm.wps.cn/postcss-pseudo-class-any-link/download/postcss-pseudo-class-any-link-7.1.6.tgz}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  postcss-reduce-initial@5.1.2:
    resolution: {integrity: sha1-eYzXez4DPq5xBcGMnTcdmJ4TgtY=, tarball: https://registry.npm.wps.cn/postcss-reduce-initial/download/postcss-reduce-initial-5.1.2.tgz}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-reduce-transforms@5.1.0:
    resolution: {integrity: sha1-Mztw53WLgC890N3+mLscz++Wtuk=, tarball: https://registry.npm.wps.cn/postcss-reduce-transforms/download/postcss-reduce-transforms-5.1.0.tgz}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-replace-overflow-wrap@4.0.0:
    resolution: {integrity: sha1-0t9r7RC0d7+cUvqyjFaLSynKQxk=, tarball: https://registry.npm.wps.cn/postcss-replace-overflow-wrap/download/postcss-replace-overflow-wrap-4.0.0.tgz}
    peerDependencies:
      postcss: ^8.0.3

  postcss-selector-not@6.0.1:
    resolution: {integrity: sha1-jwpwm/fUtFIieT/DRAm+QHU3VW0=, tarball: https://registry.npm.wps.cn/postcss-selector-not/download/postcss-selector-not-6.0.1.tgz}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  postcss-selector-parser@6.1.2:
    resolution: {integrity: sha1-J+y0H7Djtrp6HshP/zR/c0x5Kd4=, tarball: https://registry.npm.wps.cn/postcss-selector-parser/download/postcss-selector-parser-6.1.2.tgz}
    engines: {node: '>=4'}

  postcss-selector-parser@7.1.0:
    resolution: {integrity: sha1-TWr5frpl1zvE2EvLND6GXX3RYmI=, tarball: https://registry.npm.wps.cn/postcss-selector-parser/download/postcss-selector-parser-7.1.0.tgz}
    engines: {node: '>=4'}

  postcss-svgo@5.1.0:
    resolution: {integrity: sha1-CjF0AM7XifIzoogm53Uj8VhX2A0=, tarball: https://registry.npm.wps.cn/postcss-svgo/download/postcss-svgo-5.1.0.tgz}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-unique-selectors@5.1.1:
    resolution: {integrity: sha1-qfJz0erNCemqYIj0sFB7GLG1QbY=, tarball: https://registry.npm.wps.cn/postcss-unique-selectors/download/postcss-unique-selectors-5.1.1.tgz}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-value-parser@4.2.0:
    resolution: {integrity: sha1-cjwJkgg2um0+WvAZ+SvAlxwC5RQ=, tarball: https://registry.npm.wps.cn/postcss-value-parser/download/postcss-value-parser-4.2.0.tgz}

  postcss@7.0.39:
    resolution: {integrity: sha1-liQ3XZZWMOLh8sAqk1yCpZy0gwk=, tarball: https://registry.npm.wps.cn/postcss/download/postcss-7.0.39.tgz}
    engines: {node: '>=6.0.0'}

  postcss@8.5.6:
    resolution: {integrity: sha1-KCUAZhWmGbT2Kp50JswSCzSajzw=, tarball: https://registry.npm.wps.cn/postcss/download/postcss-8.5.6.tgz}
    engines: {node: ^10 || ^12 || >=14}

  prelude-ls@1.1.2:
    resolution: {integrity: sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ=, tarball: https://registry.npm.wps.cn/prelude-ls/download/prelude-ls-1.1.2.tgz}
    engines: {node: '>= 0.8.0'}

  prelude-ls@1.2.1:
    resolution: {integrity: sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=, tarball: https://registry.npm.wps.cn/prelude-ls/download/prelude-ls-1.2.1.tgz}
    engines: {node: '>= 0.8.0'}

  pretty-bytes@5.6.0:
    resolution: {integrity: sha1-NWJW9kOAR3PIL2RyP+eMksYr6us=, tarball: https://registry.npm.wps.cn/pretty-bytes/download/pretty-bytes-5.6.0.tgz}
    engines: {node: '>=6'}

  pretty-error@4.0.0:
    resolution: {integrity: sha1-kKcD9G3XI0rbRtD4SCPp0cuPENY=, tarball: https://registry.npm.wps.cn/pretty-error/download/pretty-error-4.0.0.tgz}

  pretty-format@27.5.1:
    resolution: {integrity: sha1-IYGHn96lGnpYUfs52SD6pj8B2I4=, tarball: https://registry.npm.wps.cn/pretty-format/download/pretty-format-27.5.1.tgz}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  pretty-format@28.1.3:
    resolution: {integrity: sha1-yfuozt+ZzlCWOhGyfZgqmukJcNU=, tarball: https://registry.npm.wps.cn/pretty-format/download/pretty-format-28.1.3.tgz}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}

  process-nextick-args@2.0.1:
    resolution: {integrity: sha1-eCDZsWEgzFXKmud5JoCufbptf+I=, tarball: https://registry.npm.wps.cn/process-nextick-args/download/process-nextick-args-2.0.1.tgz}

  promise@8.3.0:
    resolution: {integrity: sha1-jLMz0e3rYe8jhp+7ik6gJ5q2Dgo=, tarball: https://registry.npm.wps.cn/promise/download/promise-8.3.0.tgz}

  prompts@2.4.2:
    resolution: {integrity: sha1-e1fnOzpIAprRDr1E90sBcipMsGk=, tarball: https://registry.npm.wps.cn/prompts/download/prompts-2.4.2.tgz}
    engines: {node: '>= 6'}

  prop-types@15.8.1:
    resolution: {integrity: sha1-Z9h78aaU9IQ1zzMsJK8QIUoxQLU=, tarball: https://registry.npm.wps.cn/prop-types/download/prop-types-15.8.1.tgz}

  proxy-addr@2.0.7:
    resolution: {integrity: sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU=, tarball: https://registry.npm.wps.cn/proxy-addr/download/proxy-addr-2.0.7.tgz}
    engines: {node: '>= 0.10'}

  prr@1.0.1:
    resolution: {integrity: sha1-0/wRS6BplaRexok/SEzrHXj19HY=, tarball: https://registry.npm.wps.cn/prr/download/prr-1.0.1.tgz}

  psl@1.15.0:
    resolution: {integrity: sha1-vazjGJbx2XzsannoIkiYzpPZdMY=, tarball: https://registry.npm.wps.cn/psl/download/psl-1.15.0.tgz}

  punycode@2.3.1:
    resolution: {integrity: sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=, tarball: https://registry.npm.wps.cn/punycode/download/punycode-2.3.1.tgz}
    engines: {node: '>=6'}

  q@1.5.1:
    resolution: {integrity: sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc=, tarball: https://registry.npm.wps.cn/q/download/q-1.5.1.tgz}
    engines: {node: '>=0.6.0', teleport: '>=0.2.0'}
    deprecated: |-
      You or someone you depend on is using Q, the JavaScript Promise library that gave JavaScript developers strong feelings about promises. They can almost certainly migrate to the native JavaScript promise now. Thank you literally everyone for joining me in this bet against the odds. Be excellent to each other.

      (For a CapTP with native promises, see @endo/eventual-send and @endo/captp)

  qs@6.13.0:
    resolution: {integrity: sha1-bKO9WEOffiRWVXmJl3h7DYilGQY=, tarball: https://registry.npm.wps.cn/qs/download/qs-6.13.0.tgz}
    engines: {node: '>=0.6'}

  querystringify@2.2.0:
    resolution: {integrity: sha1-M0WUG0FTy50ILY7uTNogFqmu9/Y=, tarball: https://registry.npm.wps.cn/querystringify/download/querystringify-2.2.0.tgz}

  queue-microtask@1.2.3:
    resolution: {integrity: sha1-SSkii7xyTfrEPg77BYyve2z7YkM=, tarball: https://registry.npm.wps.cn/queue-microtask/download/queue-microtask-1.2.3.tgz}

  raf@3.4.1:
    resolution: {integrity: sha1-B0LpmkplUvRF1z4+4DKK8P8e3jk=, tarball: https://registry.npm.wps.cn/raf/download/raf-3.4.1.tgz}

  randombytes@2.1.0:
    resolution: {integrity: sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=, tarball: https://registry.npm.wps.cn/randombytes/download/randombytes-2.1.0.tgz}

  range-parser@1.2.1:
    resolution: {integrity: sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=, tarball: https://registry.npm.wps.cn/range-parser/download/range-parser-1.2.1.tgz}
    engines: {node: '>= 0.6'}

  raw-body@2.5.2:
    resolution: {integrity: sha1-mf69g7kOCJdQh+jx+UGaFJNmtoo=, tarball: https://registry.npm.wps.cn/raw-body/download/raw-body-2.5.2.tgz}
    engines: {node: '>= 0.8'}

  react-app-polyfill@3.0.0:
    resolution: {integrity: sha1-lSIeCpvSWeXKaxd8e7HLZ2j2j9c=, tarball: https://registry.npm.wps.cn/react-app-polyfill/download/react-app-polyfill-3.0.0.tgz}
    engines: {node: '>=14'}

  react-dev-utils@12.0.1:
    resolution: {integrity: sha1-upLttKHzeb1GzNa81Oe8OY3zPnM=, tarball: https://registry.npm.wps.cn/react-dev-utils/download/react-dev-utils-12.0.1.tgz}
    engines: {node: '>=14'}
    peerDependencies:
      typescript: '>=2.7'
      webpack: '>=4'
    peerDependenciesMeta:
      typescript:
        optional: true

  react-dom@18.3.1:
    resolution: {integrity: sha1-wiZdeVEbV9R5s90/36UVNklMXLQ=, tarball: https://registry.npm.wps.cn/react-dom/download/react-dom-18.3.1.tgz}
    peerDependencies:
      react: ^18.3.1

  react-error-overlay@6.1.0:
    resolution: {integrity: sha1-IrhiVr6xxYVvCKmiKK24Eh3ZhfI=, tarball: https://registry.npm.wps.cn/react-error-overlay/download/react-error-overlay-6.1.0.tgz}

  react-is@16.13.1:
    resolution: {integrity: sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ=, tarball: https://registry.npm.wps.cn/react-is/download/react-is-16.13.1.tgz}

  react-is@17.0.2:
    resolution: {integrity: sha1-5pHUqOnHiTZWVVOas3J2Kw77VPA=, tarball: https://registry.npm.wps.cn/react-is/download/react-is-17.0.2.tgz}

  react-is@18.3.1:
    resolution: {integrity: sha1-6DVX3BLq5jqZ4AOkY4ix3LtE234=, tarball: https://registry.npm.wps.cn/react-is/download/react-is-18.3.1.tgz}

  react-refresh@0.11.0:
    resolution: {integrity: sha1-dxmLlEcz8PHxqQ55HeRUH58HQEY=, tarball: https://registry.npm.wps.cn/react-refresh/download/react-refresh-0.11.0.tgz}
    engines: {node: '>=0.10.0'}

  react-router-dom@6.30.1:
    resolution: {integrity: sha1-2iWAwnLdthMl5DVHhWa+lWOkojc=, tarball: https://registry.npm.wps.cn/react-router-dom/download/react-router-dom-6.30.1.tgz}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      react: '>=16.8'
      react-dom: '>=16.8'

  react-router@6.30.1:
    resolution: {integrity: sha1-7LO4g8m6bb9dMZ3byZZ0f0q59MM=, tarball: https://registry.npm.wps.cn/react-router/download/react-router-6.30.1.tgz}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      react: '>=16.8'

  react-scripts@5.0.1:
    resolution: {integrity: sha1-YoXb1lqLpuScqNZRzjBkWm2YAAM=, tarball: https://registry.npm.wps.cn/react-scripts/download/react-scripts-5.0.1.tgz}
    engines: {node: '>=14.0.0'}
    hasBin: true
    peerDependencies:
      eslint: '*'
      react: '>= 16'
      typescript: ^3.2.1 || ^4
    peerDependenciesMeta:
      typescript:
        optional: true

  react@18.3.1:
    resolution: {integrity: sha1-SauJIAnFOTNiW9FrJTP8dUyrKJE=, tarball: https://registry.npm.wps.cn/react/download/react-18.3.1.tgz}
    engines: {node: '>=0.10.0'}

  read-cache@1.0.0:
    resolution: {integrity: sha1-5mTvMRYRZsl1HNvo28+GtftY93Q=, tarball: https://registry.npm.wps.cn/read-cache/download/read-cache-1.0.0.tgz}

  readable-stream@2.3.8:
    resolution: {integrity: sha1-kRJegEK7obmIf0k0X2J3Anzovps=, tarball: https://registry.npm.wps.cn/readable-stream/download/readable-stream-2.3.8.tgz}

  readable-stream@3.6.2:
    resolution: {integrity: sha1-VqmzbqllwAxak+8x6xEaDxEFaWc=, tarball: https://registry.npm.wps.cn/readable-stream/download/readable-stream-3.6.2.tgz}
    engines: {node: '>= 6'}

  readdirp@3.6.0:
    resolution: {integrity: sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=, tarball: https://registry.npm.wps.cn/readdirp/download/readdirp-3.6.0.tgz}
    engines: {node: '>=8.10.0'}

  recursive-readdir@2.2.3:
    resolution: {integrity: sha1-5ybzKMDWkVO8q9XDItMZUlI3k3I=, tarball: https://registry.npm.wps.cn/recursive-readdir/download/recursive-readdir-2.2.3.tgz}
    engines: {node: '>=6.0.0'}

  redent@3.0.0:
    resolution: {integrity: sha1-5Ve3mYMWu1PJ8fVvpiY1LGljBZ8=, tarball: https://registry.npm.wps.cn/redent/download/redent-3.0.0.tgz}
    engines: {node: '>=8'}

  reflect.getprototypeof@1.0.10:
    resolution: {integrity: sha1-xikhnnijMW2LYEx2XvaJlpZOe/k=, tarball: https://registry.npm.wps.cn/reflect.getprototypeof/download/reflect.getprototypeof-1.0.10.tgz}
    engines: {node: '>= 0.4'}

  regenerate-unicode-properties@10.2.0:
    resolution: {integrity: sha1-Ym4534w3Izjqm4Ao0fmdw/2cPbA=, tarball: https://registry.npm.wps.cn/regenerate-unicode-properties/download/regenerate-unicode-properties-10.2.0.tgz}
    engines: {node: '>=4'}

  regenerate@1.4.2:
    resolution: {integrity: sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo=, tarball: https://registry.npm.wps.cn/regenerate/download/regenerate-1.4.2.tgz}

  regenerator-runtime@0.13.11:
    resolution: {integrity: sha1-9tyj587sIFkNB62nhWNqkM3KF/k=, tarball: https://registry.npm.wps.cn/regenerator-runtime/download/regenerator-runtime-0.13.11.tgz}

  regex-parser@2.3.1:
    resolution: {integrity: sha1-7j9w5QvdgaIh1QUkLLmpwnWirZE=, tarball: https://registry.npm.wps.cn/regex-parser/download/regex-parser-2.3.1.tgz}

  regexp.prototype.flags@1.5.4:
    resolution: {integrity: sha1-GtbGLUSiWQB+VbOXDgD3Ru+8qhk=, tarball: https://registry.npm.wps.cn/regexp.prototype.flags/download/regexp.prototype.flags-1.5.4.tgz}
    engines: {node: '>= 0.4'}

  regexpu-core@6.2.0:
    resolution: {integrity: sha1-DlGQ155UK/KUlV3Mq64E08fVOCY=, tarball: https://registry.npm.wps.cn/regexpu-core/download/regexpu-core-6.2.0.tgz}
    engines: {node: '>=4'}

  regjsgen@0.8.0:
    resolution: {integrity: sha1-3yP/JuDFswCmRwytFgqdCQw6N6s=, tarball: https://registry.npm.wps.cn/regjsgen/download/regjsgen-0.8.0.tgz}

  regjsparser@0.12.0:
    resolution: {integrity: sha1-DoRt9sZTBYZCk3feVuBHVYOwiNw=, tarball: https://registry.npm.wps.cn/regjsparser/download/regjsparser-0.12.0.tgz}
    hasBin: true

  relateurl@0.2.7:
    resolution: {integrity: sha1-VNvzd+UUQKypCkzSdGANP/LYiKk=, tarball: https://registry.npm.wps.cn/relateurl/download/relateurl-0.2.7.tgz}
    engines: {node: '>= 0.10'}

  renderkid@3.0.0:
    resolution: {integrity: sha1-X9gj5NaVHTc1jsyaWLHwaDa2Joo=, tarball: https://registry.npm.wps.cn/renderkid/download/renderkid-3.0.0.tgz}

  require-directory@2.1.1:
    resolution: {integrity: sha1-jGStX9MNqxyXbiNE/+f3kqam30I=, tarball: https://registry.npm.wps.cn/require-directory/download/require-directory-2.1.1.tgz}
    engines: {node: '>=0.10.0'}

  require-from-string@2.0.2:
    resolution: {integrity: sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk=, tarball: https://registry.npm.wps.cn/require-from-string/download/require-from-string-2.0.2.tgz}
    engines: {node: '>=0.10.0'}

  requires-port@1.0.0:
    resolution: {integrity: sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=, tarball: https://registry.npm.wps.cn/requires-port/download/requires-port-1.0.0.tgz}

  resolve-cwd@3.0.0:
    resolution: {integrity: sha1-DwB18bslRHZs9zumpuKt/ryxPy0=, tarball: https://registry.npm.wps.cn/resolve-cwd/download/resolve-cwd-3.0.0.tgz}
    engines: {node: '>=8'}

  resolve-from@4.0.0:
    resolution: {integrity: sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=, tarball: https://registry.npm.wps.cn/resolve-from/download/resolve-from-4.0.0.tgz}
    engines: {node: '>=4'}

  resolve-from@5.0.0:
    resolution: {integrity: sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=, tarball: https://registry.npm.wps.cn/resolve-from/download/resolve-from-5.0.0.tgz}
    engines: {node: '>=8'}

  resolve-url-loader@4.0.0:
    resolution: {integrity: sha1-1Q1N3HRrsQRoRDFnrPgA3NbDrVc=, tarball: https://registry.npm.wps.cn/resolve-url-loader/download/resolve-url-loader-4.0.0.tgz}
    engines: {node: '>=8.9'}
    peerDependencies:
      rework: 1.0.1
      rework-visit: 1.0.0
    peerDependenciesMeta:
      rework:
        optional: true
      rework-visit:
        optional: true

  resolve.exports@1.1.1:
    resolution: {integrity: sha1-Bc/Vs+32QVcf1G+mCLYQ3anq2Zk=, tarball: https://registry.npm.wps.cn/resolve.exports/download/resolve.exports-1.1.1.tgz}
    engines: {node: '>=10'}

  resolve@1.22.10:
    resolution: {integrity: sha1-tmPoP/sJu/I4aURza6roAwKbizk=, tarball: https://registry.npm.wps.cn/resolve/download/resolve-1.22.10.tgz}
    engines: {node: '>= 0.4'}
    hasBin: true

  resolve@2.0.0-next.5:
    resolution: {integrity: sha1-aw7DEH5nHlK2jNBo7zJxc7kNwDw=, tarball: https://registry.npm.wps.cn/resolve/download/resolve-2.0.0-next.5.tgz}
    hasBin: true

  retry@0.13.1:
    resolution: {integrity: sha1-GFsVh6z2eRnWOzVzSeA1N7JIRlg=, tarball: https://registry.npm.wps.cn/retry/download/retry-0.13.1.tgz}
    engines: {node: '>= 4'}

  reusify@1.1.0:
    resolution: {integrity: sha1-D+E7lSLhRz9RtVjueW4I8R+bSJ8=, tarball: https://registry.npm.wps.cn/reusify/download/reusify-1.1.0.tgz}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}

  rimraf@3.0.2:
    resolution: {integrity: sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=, tarball: https://registry.npm.wps.cn/rimraf/download/rimraf-3.0.2.tgz}
    deprecated: Rimraf versions prior to v4 are no longer supported
    hasBin: true

  rollup-plugin-terser@7.0.2:
    resolution: {integrity: sha1-6Pu6SGmYGy3DWufopQLVxsBNMk0=, tarball: https://registry.npm.wps.cn/rollup-plugin-terser/download/rollup-plugin-terser-7.0.2.tgz}
    deprecated: This package has been deprecated and is no longer maintained. Please use @rollup/plugin-terser
    peerDependencies:
      rollup: ^2.0.0

  rollup@2.79.2:
    resolution: {integrity: sha1-8VDkpdtLEhohp0fXYvcB5en0kJA=, tarball: https://registry.npm.wps.cn/rollup/download/rollup-2.79.2.tgz}
    engines: {node: '>=10.0.0'}
    hasBin: true

  run-parallel@1.2.0:
    resolution: {integrity: sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=, tarball: https://registry.npm.wps.cn/run-parallel/download/run-parallel-1.2.0.tgz}

  safe-array-concat@1.1.3:
    resolution: {integrity: sha1-yeVOxPYDsLu45+UAel7nrs0VOMM=, tarball: https://registry.npm.wps.cn/safe-array-concat/download/safe-array-concat-1.1.3.tgz}
    engines: {node: '>=0.4'}

  safe-buffer@5.1.2:
    resolution: {integrity: sha1-mR7GnSluAxN0fVm9/St0XDX4go0=, tarball: https://registry.npm.wps.cn/safe-buffer/download/safe-buffer-5.1.2.tgz}

  safe-buffer@5.2.1:
    resolution: {integrity: sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=, tarball: https://registry.npm.wps.cn/safe-buffer/download/safe-buffer-5.2.1.tgz}

  safe-push-apply@1.0.0:
    resolution: {integrity: sha1-AYUOmBwWAtOYyFCB82Dk5tA9J/U=, tarball: https://registry.npm.wps.cn/safe-push-apply/download/safe-push-apply-1.0.0.tgz}
    engines: {node: '>= 0.4'}

  safe-regex-test@1.1.0:
    resolution: {integrity: sha1-f4fftnoxUHguqvGFg/9dFxGsEME=, tarball: https://registry.npm.wps.cn/safe-regex-test/download/safe-regex-test-1.1.0.tgz}
    engines: {node: '>= 0.4'}

  safer-buffer@2.1.2:
    resolution: {integrity: sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=, tarball: https://registry.npm.wps.cn/safer-buffer/download/safer-buffer-2.1.2.tgz}

  sanitize.css@13.0.0:
    resolution: {integrity: sha1-JnVVOXSyeWTHVWKt472F15h58XM=, tarball: https://registry.npm.wps.cn/sanitize.css/download/sanitize.css-13.0.0.tgz}

  sass-loader@12.6.0:
    resolution: {integrity: sha1-UUg2LI4s3UuVDzxjrF0W2/7Te8s=, tarball: https://registry.npm.wps.cn/sass-loader/download/sass-loader-12.6.0.tgz}
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      fibers: '>= 3.1.0'
      node-sass: ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0
      sass: ^1.3.0
      sass-embedded: '*'
      webpack: ^5.0.0
    peerDependenciesMeta:
      fibers:
        optional: true
      node-sass:
        optional: true
      sass:
        optional: true
      sass-embedded:
        optional: true

  sax@1.2.4:
    resolution: {integrity: sha1-KBYjTiN4vdxOU1T6tcqold9xANk=, tarball: https://registry.npm.wps.cn/sax/download/sax-1.2.4.tgz}

  sax@1.4.1:
    resolution: {integrity: sha1-RMyJiDd/EmME07P8EBDHM7kp7w8=, tarball: https://registry.npm.wps.cn/sax/download/sax-1.4.1.tgz}

  saxes@5.0.1:
    resolution: {integrity: sha1-7rq5U/o7dgjb6U5drbFciI+maW0=, tarball: https://registry.npm.wps.cn/saxes/download/saxes-5.0.1.tgz}
    engines: {node: '>=10'}

  scheduler@0.23.2:
    resolution: {integrity: sha1-QUumSjsoKJLpRM8hCOzAeNEVzcM=, tarball: https://registry.npm.wps.cn/scheduler/download/scheduler-0.23.2.tgz}

  schema-utils@2.7.0:
    resolution: {integrity: sha1-FxUfdtjq5n+793lgwzxnatn078c=, tarball: https://registry.npm.wps.cn/schema-utils/download/schema-utils-2.7.0.tgz}
    engines: {node: '>= 8.9.0'}

  schema-utils@2.7.1:
    resolution: {integrity: sha1-HKTzLRskxZDCA7jnpQvw6kzTlNc=, tarball: https://registry.npm.wps.cn/schema-utils/download/schema-utils-2.7.1.tgz}
    engines: {node: '>= 8.9.0'}

  schema-utils@3.3.0:
    resolution: {integrity: sha1-9QqIh3w8AWUqFbYirp6Xld96YP4=, tarball: https://registry.npm.wps.cn/schema-utils/download/schema-utils-3.3.0.tgz}
    engines: {node: '>= 10.13.0'}

  schema-utils@4.3.2:
    resolution: {integrity: sha1-DBCHi/SnP9Kx39FLlGKyZ4jIBq4=, tarball: https://registry.npm.wps.cn/schema-utils/download/schema-utils-4.3.2.tgz}
    engines: {node: '>= 10.13.0'}

  select-hose@2.0.0:
    resolution: {integrity: sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo=, tarball: https://registry.npm.wps.cn/select-hose/download/select-hose-2.0.0.tgz}

  selfsigned@2.4.1:
    resolution: {integrity: sha1-Vg2QVlRCo+01tnQDTOxOldzrSuA=, tarball: https://registry.npm.wps.cn/selfsigned/download/selfsigned-2.4.1.tgz}
    engines: {node: '>=10'}

  semver@5.7.2:
    resolution: {integrity: sha1-SNVdtzfDKHzUg14X+hP+rOHEHvg=, tarball: https://registry.npm.wps.cn/semver/download/semver-5.7.2.tgz}
    hasBin: true

  semver@6.3.1:
    resolution: {integrity: sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=, tarball: https://registry.npm.wps.cn/semver/download/semver-6.3.1.tgz}
    hasBin: true

  semver@7.7.2:
    resolution: {integrity: sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=, tarball: https://registry.npm.wps.cn/semver/download/semver-7.7.2.tgz}
    engines: {node: '>=10'}
    hasBin: true

  send@0.19.0:
    resolution: {integrity: sha1-u8WjiMjqbASJZwSdvqwOSj8J1/g=, tarball: https://registry.npm.wps.cn/send/download/send-0.19.0.tgz}
    engines: {node: '>= 0.8.0'}

  serialize-javascript@4.0.0:
    resolution: {integrity: sha1-tSXhI4SJpez8Qq+sw/6Z5mb0sao=, tarball: https://registry.npm.wps.cn/serialize-javascript/download/serialize-javascript-4.0.0.tgz}

  serialize-javascript@6.0.2:
    resolution: {integrity: sha1-3voeBVyDv21Z6oBdjahiJU62psI=, tarball: https://registry.npm.wps.cn/serialize-javascript/download/serialize-javascript-6.0.2.tgz}

  serve-index@1.9.1:
    resolution: {integrity: sha1-03aNabHn2C5c4FD/9bRTvqEqkjk=, tarball: https://registry.npm.wps.cn/serve-index/download/serve-index-1.9.1.tgz}
    engines: {node: '>= 0.8.0'}

  serve-static@1.16.2:
    resolution: {integrity: sha1-tqU0PaR/a90mc4SL9FdUlB6AMpY=, tarball: https://registry.npm.wps.cn/serve-static/download/serve-static-1.16.2.tgz}
    engines: {node: '>= 0.8.0'}

  set-function-length@1.2.2:
    resolution: {integrity: sha1-qscjFBmOrtl1z3eyw7a4gGleVEk=, tarball: https://registry.npm.wps.cn/set-function-length/download/set-function-length-1.2.2.tgz}
    engines: {node: '>= 0.4'}

  set-function-name@2.0.2:
    resolution: {integrity: sha1-FqcFxaDcL15jjKltiozU4cK5CYU=, tarball: https://registry.npm.wps.cn/set-function-name/download/set-function-name-2.0.2.tgz}
    engines: {node: '>= 0.4'}

  set-proto@1.0.0:
    resolution: {integrity: sha1-B2Dbz/MLLX6AH9bhmYPlbaM3Vl4=, tarball: https://registry.npm.wps.cn/set-proto/download/set-proto-1.0.0.tgz}
    engines: {node: '>= 0.4'}

  setprototypeof@1.1.0:
    resolution: {integrity: sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY=, tarball: https://registry.npm.wps.cn/setprototypeof/download/setprototypeof-1.1.0.tgz}

  setprototypeof@1.2.0:
    resolution: {integrity: sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ=, tarball: https://registry.npm.wps.cn/setprototypeof/download/setprototypeof-1.2.0.tgz}

  shallow-clone@3.0.1:
    resolution: {integrity: sha1-jymBrZJTH1UDWwH7IwdppA4C76M=, tarball: https://registry.npm.wps.cn/shallow-clone/download/shallow-clone-3.0.1.tgz}
    engines: {node: '>=8'}

  shebang-command@2.0.0:
    resolution: {integrity: sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=, tarball: https://registry.npm.wps.cn/shebang-command/download/shebang-command-2.0.0.tgz}
    engines: {node: '>=8'}

  shebang-regex@3.0.0:
    resolution: {integrity: sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=, tarball: https://registry.npm.wps.cn/shebang-regex/download/shebang-regex-3.0.0.tgz}
    engines: {node: '>=8'}

  shell-quote@1.8.3:
    resolution: {integrity: sha1-VeQO8zz1xomQI1Oj2M0aZyXwi0s=, tarball: https://registry.npm.wps.cn/shell-quote/download/shell-quote-1.8.3.tgz}
    engines: {node: '>= 0.4'}

  side-channel-list@1.0.0:
    resolution: {integrity: sha1-EMtZhCYxFdO3oOM2WR4pCoMK+K0=, tarball: https://registry.npm.wps.cn/side-channel-list/download/side-channel-list-1.0.0.tgz}
    engines: {node: '>= 0.4'}

  side-channel-map@1.0.1:
    resolution: {integrity: sha1-1rtrN5Asb+9RdOX1M/q0xzKib0I=, tarball: https://registry.npm.wps.cn/side-channel-map/download/side-channel-map-1.0.1.tgz}
    engines: {node: '>= 0.4'}

  side-channel-weakmap@1.0.2:
    resolution: {integrity: sha1-Ed2hnVNo5Azp7CvcH7DsvAeQ7Oo=, tarball: https://registry.npm.wps.cn/side-channel-weakmap/download/side-channel-weakmap-1.0.2.tgz}
    engines: {node: '>= 0.4'}

  side-channel@1.1.0:
    resolution: {integrity: sha1-w/z/nE2pMnhIczNeyXZfqU/2a8k=, tarball: https://registry.npm.wps.cn/side-channel/download/side-channel-1.1.0.tgz}
    engines: {node: '>= 0.4'}

  signal-exit@3.0.7:
    resolution: {integrity: sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=, tarball: https://registry.npm.wps.cn/signal-exit/download/signal-exit-3.0.7.tgz}

  signal-exit@4.1.0:
    resolution: {integrity: sha1-lSGIwcvVRgcOLdIND0HArgUwywQ=, tarball: https://registry.npm.wps.cn/signal-exit/download/signal-exit-4.1.0.tgz}
    engines: {node: '>=14'}

  sisteransi@1.0.5:
    resolution: {integrity: sha1-E01oEpd1ZDfMBcoBNw06elcQde0=, tarball: https://registry.npm.wps.cn/sisteransi/download/sisteransi-1.0.5.tgz}

  slash@3.0.0:
    resolution: {integrity: sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=, tarball: https://registry.npm.wps.cn/slash/download/slash-3.0.0.tgz}
    engines: {node: '>=8'}

  slash@4.0.0:
    resolution: {integrity: sha1-JCI3IXbExsWt214q2oha+YSzlqc=, tarball: https://registry.npm.wps.cn/slash/download/slash-4.0.0.tgz}
    engines: {node: '>=12'}

  sockjs@0.3.24:
    resolution: {integrity: sha1-ybyJlfM6ERvqA5XsMKoyBr21zM4=, tarball: https://registry.npm.wps.cn/sockjs/download/sockjs-0.3.24.tgz}

  source-list-map@2.0.1:
    resolution: {integrity: sha1-OZO9hzv8SEecyp6jpUeDXHwVSzQ=, tarball: https://registry.npm.wps.cn/source-list-map/download/source-list-map-2.0.1.tgz}

  source-map-js@1.2.1:
    resolution: {integrity: sha1-HOVlD93YerwJnto33P8CTCZnrkY=, tarball: https://registry.npm.wps.cn/source-map-js/download/source-map-js-1.2.1.tgz}
    engines: {node: '>=0.10.0'}

  source-map-loader@3.0.2:
    resolution: {integrity: sha1-ryMZL5s0Tapyn2dykzGUzF+lT+4=, tarball: https://registry.npm.wps.cn/source-map-loader/download/source-map-loader-3.0.2.tgz}
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      webpack: ^5.0.0

  source-map-support@0.5.21:
    resolution: {integrity: sha1-BP58f54e0tZiIzwoyys1ufY/bk8=, tarball: https://registry.npm.wps.cn/source-map-support/download/source-map-support-0.5.21.tgz}

  source-map@0.6.1:
    resolution: {integrity: sha1-dHIq8y6WFOnCh6jQu95IteLxomM=, tarball: https://registry.npm.wps.cn/source-map/download/source-map-0.6.1.tgz}
    engines: {node: '>=0.10.0'}

  source-map@0.7.4:
    resolution: {integrity: sha1-qbvnBcnYhG9OCP9nZazw8bCJhlY=, tarball: https://registry.npm.wps.cn/source-map/download/source-map-0.7.4.tgz}
    engines: {node: '>= 8'}

  source-map@0.8.0-beta.0:
    resolution: {integrity: sha1-1MG7QsP37pJfAFknuhBwng0dHxE=, tarball: https://registry.npm.wps.cn/source-map/download/source-map-0.8.0-beta.0.tgz}
    engines: {node: '>= 8'}

  sourcemap-codec@1.4.8:
    resolution: {integrity: sha1-6oBL2UhXQC5pktBaOO8a41qatMQ=, tarball: https://registry.npm.wps.cn/sourcemap-codec/download/sourcemap-codec-1.4.8.tgz}
    deprecated: Please use @jridgewell/sourcemap-codec instead

  spdy-transport@3.0.0:
    resolution: {integrity: sha1-ANSGOmQArXXfkzYaFghgXl3NzzE=, tarball: https://registry.npm.wps.cn/spdy-transport/download/spdy-transport-3.0.0.tgz}

  spdy@4.0.2:
    resolution: {integrity: sha1-t09GYgOj7aRSwCSSuR+56EonZ3s=, tarball: https://registry.npm.wps.cn/spdy/download/spdy-4.0.2.tgz}
    engines: {node: '>=6.0.0'}

  sprintf-js@1.0.3:
    resolution: {integrity: sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=, tarball: https://registry.npm.wps.cn/sprintf-js/download/sprintf-js-1.0.3.tgz}

  stable@0.1.8:
    resolution: {integrity: sha1-g26zyDgv4pNv6vVEYxAXzn1Ho88=, tarball: https://registry.npm.wps.cn/stable/download/stable-0.1.8.tgz}
    deprecated: 'Modern JS already guarantees Array#sort() is a stable sort, so this library is deprecated. See the compatibility table on MDN: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/sort#browser_compatibility'

  stack-utils@2.0.6:
    resolution: {integrity: sha1-qvB0gWnAL8M8gjKrzPkz9Uocw08=, tarball: https://registry.npm.wps.cn/stack-utils/download/stack-utils-2.0.6.tgz}
    engines: {node: '>=10'}

  stackframe@1.3.4:
    resolution: {integrity: sha1-uIGgBMjBSaXo7+831RsW5BKUMxA=, tarball: https://registry.npm.wps.cn/stackframe/download/stackframe-1.3.4.tgz}

  static-eval@2.0.2:
    resolution: {integrity: sha1-LRdZMGsb76aIk4RUxUa3hx+AakI=, tarball: https://registry.npm.wps.cn/static-eval/download/static-eval-2.0.2.tgz}

  statuses@1.5.0:
    resolution: {integrity: sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=, tarball: https://registry.npm.wps.cn/statuses/download/statuses-1.5.0.tgz}
    engines: {node: '>= 0.6'}

  statuses@2.0.1:
    resolution: {integrity: sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M=, tarball: https://registry.npm.wps.cn/statuses/download/statuses-2.0.1.tgz}
    engines: {node: '>= 0.8'}

  stop-iteration-iterator@1.1.0:
    resolution: {integrity: sha1-9IH/cKVI9hJNAxLDqhTL+nqlQq0=, tarball: https://registry.npm.wps.cn/stop-iteration-iterator/download/stop-iteration-iterator-1.1.0.tgz}
    engines: {node: '>= 0.4'}

  string-length@4.0.2:
    resolution: {integrity: sha1-qKjce9XBqCubPIuH4SX2aHG25Xo=, tarball: https://registry.npm.wps.cn/string-length/download/string-length-4.0.2.tgz}
    engines: {node: '>=10'}

  string-length@5.0.1:
    resolution: {integrity: sha1-PWR/SXtujo1B5CL34LI7xTbIOB4=, tarball: https://registry.npm.wps.cn/string-length/download/string-length-5.0.1.tgz}
    engines: {node: '>=12.20'}

  string-natural-compare@3.0.1:
    resolution: {integrity: sha1-ekLVhHRFSWN1no6LeuY9ccHn/fQ=, tarball: https://registry.npm.wps.cn/string-natural-compare/download/string-natural-compare-3.0.1.tgz}

  string-width@4.2.3:
    resolution: {integrity: sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=, tarball: https://registry.npm.wps.cn/string-width/download/string-width-4.2.3.tgz}
    engines: {node: '>=8'}

  string-width@5.1.2:
    resolution: {integrity: sha1-FPja7G2B5yIdKjV+Zoyrc728p5Q=, tarball: https://registry.npm.wps.cn/string-width/download/string-width-5.1.2.tgz}
    engines: {node: '>=12'}

  string.prototype.includes@2.0.1:
    resolution: {integrity: sha1-7O7yEoNkB2GoHb4W1scXGk7ffZI=, tarball: https://registry.npm.wps.cn/string.prototype.includes/download/string.prototype.includes-2.0.1.tgz}
    engines: {node: '>= 0.4'}

  string.prototype.matchall@4.0.12:
    resolution: {integrity: sha1-bIh0DkmtSVaxMyqRHpSVg6J11MA=, tarball: https://registry.npm.wps.cn/string.prototype.matchall/download/string.prototype.matchall-4.0.12.tgz}
    engines: {node: '>= 0.4'}

  string.prototype.repeat@1.0.0:
    resolution: {integrity: sha1-6Qhy7gMIspQ1qiYnX24bdi2u4Bo=, tarball: https://registry.npm.wps.cn/string.prototype.repeat/download/string.prototype.repeat-1.0.0.tgz}

  string.prototype.trim@1.2.10:
    resolution: {integrity: sha1-QLLdXulMlZtNz7HWXOcukNpIDIE=, tarball: https://registry.npm.wps.cn/string.prototype.trim/download/string.prototype.trim-1.2.10.tgz}
    engines: {node: '>= 0.4'}

  string.prototype.trimend@1.0.9:
    resolution: {integrity: sha1-YuJzEnLNKFBBs2WWBU6fZlabaUI=, tarball: https://registry.npm.wps.cn/string.prototype.trimend/download/string.prototype.trimend-1.0.9.tgz}
    engines: {node: '>= 0.4'}

  string.prototype.trimstart@1.0.8:
    resolution: {integrity: sha1-fug03ajHwX7/MRhHK7Nb/tqjTd4=, tarball: https://registry.npm.wps.cn/string.prototype.trimstart/download/string.prototype.trimstart-1.0.8.tgz}
    engines: {node: '>= 0.4'}

  string_decoder@1.1.1:
    resolution: {integrity: sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=, tarball: https://registry.npm.wps.cn/string_decoder/download/string_decoder-1.1.1.tgz}

  string_decoder@1.3.0:
    resolution: {integrity: sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=, tarball: https://registry.npm.wps.cn/string_decoder/download/string_decoder-1.3.0.tgz}

  stringify-object@3.3.0:
    resolution: {integrity: sha1-cDBlrvyhkwDTzoivT1s5VtdVZik=, tarball: https://registry.npm.wps.cn/stringify-object/download/stringify-object-3.3.0.tgz}
    engines: {node: '>=4'}

  strip-ansi@6.0.1:
    resolution: {integrity: sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=, tarball: https://registry.npm.wps.cn/strip-ansi/download/strip-ansi-6.0.1.tgz}
    engines: {node: '>=8'}

  strip-ansi@7.1.0:
    resolution: {integrity: sha1-1bZWjKaJ2FYTcLBwdoXSJDT6/0U=, tarball: https://registry.npm.wps.cn/strip-ansi/download/strip-ansi-7.1.0.tgz}
    engines: {node: '>=12'}

  strip-bom@3.0.0:
    resolution: {integrity: sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=, tarball: https://registry.npm.wps.cn/strip-bom/download/strip-bom-3.0.0.tgz}
    engines: {node: '>=4'}

  strip-bom@4.0.0:
    resolution: {integrity: sha1-nDUFwdtFvO3KPZz3oW9cWqOQGHg=, tarball: https://registry.npm.wps.cn/strip-bom/download/strip-bom-4.0.0.tgz}
    engines: {node: '>=8'}

  strip-comments@2.0.1:
    resolution: {integrity: sha1-StEcP7ysF3pnpArCJMoznKHBups=, tarball: https://registry.npm.wps.cn/strip-comments/download/strip-comments-2.0.1.tgz}
    engines: {node: '>=10'}

  strip-final-newline@2.0.0:
    resolution: {integrity: sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=, tarball: https://registry.npm.wps.cn/strip-final-newline/download/strip-final-newline-2.0.0.tgz}
    engines: {node: '>=6'}

  strip-indent@3.0.0:
    resolution: {integrity: sha1-wy4c7pQLazQyx3G8LFS8znPNMAE=, tarball: https://registry.npm.wps.cn/strip-indent/download/strip-indent-3.0.0.tgz}
    engines: {node: '>=8'}

  strip-json-comments@3.1.1:
    resolution: {integrity: sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=, tarball: https://registry.npm.wps.cn/strip-json-comments/download/strip-json-comments-3.1.1.tgz}
    engines: {node: '>=8'}

  style-loader@3.3.4:
    resolution: {integrity: sha1-8w94bDbbA6RcvVW2pw2TDEeQkOc=, tarball: https://registry.npm.wps.cn/style-loader/download/style-loader-3.3.4.tgz}
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      webpack: ^5.0.0

  stylehacks@5.1.1:
    resolution: {integrity: sha1-eTSjTrWdcVIUn6adbp5W8vw0vMk=, tarball: https://registry.npm.wps.cn/stylehacks/download/stylehacks-5.1.1.tgz}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  sucrase@3.35.0:
    resolution: {integrity: sha1-V/F6PX4Zs22JlfBmedEhvpFK4mM=, tarball: https://registry.npm.wps.cn/sucrase/download/sucrase-3.35.0.tgz}
    engines: {node: '>=16 || 14 >=14.17'}
    hasBin: true

  supports-color@5.5.0:
    resolution: {integrity: sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=, tarball: https://registry.npm.wps.cn/supports-color/download/supports-color-5.5.0.tgz}
    engines: {node: '>=4'}

  supports-color@7.2.0:
    resolution: {integrity: sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=, tarball: https://registry.npm.wps.cn/supports-color/download/supports-color-7.2.0.tgz}
    engines: {node: '>=8'}

  supports-color@8.1.1:
    resolution: {integrity: sha1-zW/BfihQDP9WwbhsCn/UpUpzAFw=, tarball: https://registry.npm.wps.cn/supports-color/download/supports-color-8.1.1.tgz}
    engines: {node: '>=10'}

  supports-hyperlinks@2.3.0:
    resolution: {integrity: sha1-OUNUQ0fB/5CxXv+wP8FK5F7BBiQ=, tarball: https://registry.npm.wps.cn/supports-hyperlinks/download/supports-hyperlinks-2.3.0.tgz}
    engines: {node: '>=8'}

  supports-preserve-symlinks-flag@1.0.0:
    resolution: {integrity: sha1-btpL00SjyUrqN21MwxvHcxEDngk=, tarball: https://registry.npm.wps.cn/supports-preserve-symlinks-flag/download/supports-preserve-symlinks-flag-1.0.0.tgz}
    engines: {node: '>= 0.4'}

  svg-parser@2.0.4:
    resolution: {integrity: sha1-/cLinhOVFzYUC3bLEiyO5mMOtrU=, tarball: https://registry.npm.wps.cn/svg-parser/download/svg-parser-2.0.4.tgz}

  svgo@1.3.2:
    resolution: {integrity: sha1-ttxRHAYzRsnkFbgeQ0ARRbltQWc=, tarball: https://registry.npm.wps.cn/svgo/download/svgo-1.3.2.tgz}
    engines: {node: '>=4.0.0'}
    deprecated: This SVGO version is no longer supported. Upgrade to v2.x.x.
    hasBin: true

  svgo@2.8.0:
    resolution: {integrity: sha1-T/gMzmcQ3CeV8MfHQQHmdkz8zSQ=, tarball: https://registry.npm.wps.cn/svgo/download/svgo-2.8.0.tgz}
    engines: {node: '>=10.13.0'}
    hasBin: true

  symbol-tree@3.2.4:
    resolution: {integrity: sha1-QwY30ki6d+B4iDlR+5qg7tfGP6I=, tarball: https://registry.npm.wps.cn/symbol-tree/download/symbol-tree-3.2.4.tgz}

  tailwindcss@3.4.17:
    resolution: {integrity: sha1-roQGwPlmlqYxx5B2j/MZ1G1eWmM=, tarball: https://registry.npm.wps.cn/tailwindcss/download/tailwindcss-3.4.17.tgz}
    engines: {node: '>=14.0.0'}
    hasBin: true

  tapable@1.1.3:
    resolution: {integrity: sha1-ofzMBrWNth/XpF2i2kT186Pme6I=, tarball: https://registry.npm.wps.cn/tapable/download/tapable-1.1.3.tgz}
    engines: {node: '>=6'}

  tapable@2.2.2:
    resolution: {integrity: sha1-q0mENA0wy5mJpJADLwhtu4tW2HI=, tarball: https://registry.npm.wps.cn/tapable/download/tapable-2.2.2.tgz}
    engines: {node: '>=6'}

  temp-dir@2.0.0:
    resolution: {integrity: sha1-vekrBb3+sVFugEycAK1FF38xMh4=, tarball: https://registry.npm.wps.cn/temp-dir/download/temp-dir-2.0.0.tgz}
    engines: {node: '>=8'}

  tempy@0.6.0:
    resolution: {integrity: sha1-ZeLDWrwG8RJKl/OHsIMDRCveWfM=, tarball: https://registry.npm.wps.cn/tempy/download/tempy-0.6.0.tgz}
    engines: {node: '>=10'}

  terminal-link@2.1.1:
    resolution: {integrity: sha1-FKZKJ6s8Dfkz6lRvulXy0HjtyZQ=, tarball: https://registry.npm.wps.cn/terminal-link/download/terminal-link-2.1.1.tgz}
    engines: {node: '>=8'}

  terser-webpack-plugin@5.3.14:
    resolution: {integrity: sha1-kDHUjlerJ1Z/AqzoXH1pDbZsPgY=, tarball: https://registry.npm.wps.cn/terser-webpack-plugin/download/terser-webpack-plugin-5.3.14.tgz}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      '@swc/core': '*'
      esbuild: '*'
      uglify-js: '*'
      webpack: ^5.1.0
    peerDependenciesMeta:
      '@swc/core':
        optional: true
      esbuild:
        optional: true
      uglify-js:
        optional: true

  terser@5.43.1:
    resolution: {integrity: sha1-iDh/T5eU/xop561h+yky4ltP220=, tarball: https://registry.npm.wps.cn/terser/download/terser-5.43.1.tgz}
    engines: {node: '>=10'}
    hasBin: true

  test-exclude@6.0.0:
    resolution: {integrity: sha1-BKhphmHYBepvopO2y55jrARO8V4=, tarball: https://registry.npm.wps.cn/test-exclude/download/test-exclude-6.0.0.tgz}
    engines: {node: '>=8'}

  text-table@0.2.0:
    resolution: {integrity: sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=, tarball: https://registry.npm.wps.cn/text-table/download/text-table-0.2.0.tgz}

  thenify-all@1.6.0:
    resolution: {integrity: sha1-GhkY1ALY/D+Y+/I02wvMjMEOlyY=, tarball: https://registry.npm.wps.cn/thenify-all/download/thenify-all-1.6.0.tgz}
    engines: {node: '>=0.8'}

  thenify@3.3.1:
    resolution: {integrity: sha1-iTLmhqQGYDigFt2eLKRq3Zg4qV8=, tarball: https://registry.npm.wps.cn/thenify/download/thenify-3.3.1.tgz}

  throat@6.0.2:
    resolution: {integrity: sha1-UaP7teEa5y4s90hh7VyAIPifKf4=, tarball: https://registry.npm.wps.cn/throat/download/throat-6.0.2.tgz}

  thunky@1.1.0:
    resolution: {integrity: sha1-Wrr3FKlAXbBQRzK7zNLO3Z75U30=, tarball: https://registry.npm.wps.cn/thunky/download/thunky-1.1.0.tgz}

  tmpl@1.0.5:
    resolution: {integrity: sha1-hoPguQK7nCDE9ybjwLafNlGMB8w=, tarball: https://registry.npm.wps.cn/tmpl/download/tmpl-1.0.5.tgz}

  to-regex-range@5.0.1:
    resolution: {integrity: sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=, tarball: https://registry.npm.wps.cn/to-regex-range/download/to-regex-range-5.0.1.tgz}
    engines: {node: '>=8.0'}

  toidentifier@1.0.1:
    resolution: {integrity: sha1-O+NDIaiKgg7RvYDfqjPkefu43TU=, tarball: https://registry.npm.wps.cn/toidentifier/download/toidentifier-1.0.1.tgz}
    engines: {node: '>=0.6'}

  tough-cookie@4.1.4:
    resolution: {integrity: sha1-lF8UYbRbWox2ghwz6knDrBksGzY=, tarball: https://registry.npm.wps.cn/tough-cookie/download/tough-cookie-4.1.4.tgz}
    engines: {node: '>=6'}

  tr46@1.0.1:
    resolution: {integrity: sha1-qLE/1r/SSJUZZ0zN5VujaTtwbQk=, tarball: https://registry.npm.wps.cn/tr46/download/tr46-1.0.1.tgz}

  tr46@2.1.0:
    resolution: {integrity: sha1-+oeqgcpdWUHajL8fm3SdyWmk4kA=, tarball: https://registry.npm.wps.cn/tr46/download/tr46-2.1.0.tgz}
    engines: {node: '>=8'}

  tryer@1.0.1:
    resolution: {integrity: sha1-8shUBoALmw90yfdGW4HqrSQSUvg=, tarball: https://registry.npm.wps.cn/tryer/download/tryer-1.0.1.tgz}

  ts-interface-checker@0.1.13:
    resolution: {integrity: sha1-eE/T1nlyK8EDsbS4AwvN212yppk=, tarball: https://registry.npm.wps.cn/ts-interface-checker/download/ts-interface-checker-0.1.13.tgz}

  ts-node@10.9.2:
    resolution: {integrity: sha1-cPAhyeGFvM3Kgg4m3EE4BcEBxx8=, tarball: https://registry.npm.wps.cn/ts-node/download/ts-node-10.9.2.tgz}
    hasBin: true
    peerDependencies:
      '@swc/core': '>=1.2.50'
      '@swc/wasm': '>=1.2.50'
      '@types/node': '*'
      typescript: '>=2.7'
    peerDependenciesMeta:
      '@swc/core':
        optional: true
      '@swc/wasm':
        optional: true

  tsconfig-paths@3.15.0:
    resolution: {integrity: sha1-UpnsYF5VsauyPsk57xXtr0gwcNQ=, tarball: https://registry.npm.wps.cn/tsconfig-paths/download/tsconfig-paths-3.15.0.tgz}

  tslib@1.14.1:
    resolution: {integrity: sha1-zy04vcNKE0vK8QkcQfZhni9nLQA=, tarball: https://registry.npm.wps.cn/tslib/download/tslib-1.14.1.tgz}

  tslib@2.8.1:
    resolution: {integrity: sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=, tarball: https://registry.npm.wps.cn/tslib/download/tslib-2.8.1.tgz}

  tsutils@3.21.0:
    resolution: {integrity: sha1-tIcX05TOpsHglpg+7Vjp1hcVtiM=, tarball: https://registry.npm.wps.cn/tsutils/download/tsutils-3.21.0.tgz}
    engines: {node: '>= 6'}
    peerDependencies:
      typescript: '>=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta'

  type-check@0.3.2:
    resolution: {integrity: sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=, tarball: https://registry.npm.wps.cn/type-check/download/type-check-0.3.2.tgz}
    engines: {node: '>= 0.8.0'}

  type-check@0.4.0:
    resolution: {integrity: sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=, tarball: https://registry.npm.wps.cn/type-check/download/type-check-0.4.0.tgz}
    engines: {node: '>= 0.8.0'}

  type-detect@4.0.8:
    resolution: {integrity: sha1-dkb7XxiHHPu3dJ5pvTmmOI63RQw=, tarball: https://registry.npm.wps.cn/type-detect/download/type-detect-4.0.8.tgz}
    engines: {node: '>=4'}

  type-fest@0.16.0:
    resolution: {integrity: sha1-MkC4kaeLDerpENvrhlU+VSoUiGA=, tarball: https://registry.npm.wps.cn/type-fest/download/type-fest-0.16.0.tgz}
    engines: {node: '>=10'}

  type-fest@0.20.2:
    resolution: {integrity: sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ=, tarball: https://registry.npm.wps.cn/type-fest/download/type-fest-0.20.2.tgz}
    engines: {node: '>=10'}

  type-fest@0.21.3:
    resolution: {integrity: sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc=, tarball: https://registry.npm.wps.cn/type-fest/download/type-fest-0.21.3.tgz}
    engines: {node: '>=10'}

  type-is@1.6.18:
    resolution: {integrity: sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=, tarball: https://registry.npm.wps.cn/type-is/download/type-is-1.6.18.tgz}
    engines: {node: '>= 0.6'}

  typed-array-buffer@1.0.3:
    resolution: {integrity: sha1-pyOVRQpIaewDP9VJNxtHrzou5TY=, tarball: https://registry.npm.wps.cn/typed-array-buffer/download/typed-array-buffer-1.0.3.tgz}
    engines: {node: '>= 0.4'}

  typed-array-byte-length@1.0.3:
    resolution: {integrity: sha1-hAegT314aE89JSqhoUPSt3tBYM4=, tarball: https://registry.npm.wps.cn/typed-array-byte-length/download/typed-array-byte-length-1.0.3.tgz}
    engines: {node: '>= 0.4'}

  typed-array-byte-offset@1.0.4:
    resolution: {integrity: sha1-rjaYuOyRqKuUUBYQiu8A1b/xI1U=, tarball: https://registry.npm.wps.cn/typed-array-byte-offset/download/typed-array-byte-offset-1.0.4.tgz}
    engines: {node: '>= 0.4'}

  typed-array-length@1.0.7:
    resolution: {integrity: sha1-7k3v+YS2S+HhGLDejJyHfVznPT0=, tarball: https://registry.npm.wps.cn/typed-array-length/download/typed-array-length-1.0.7.tgz}
    engines: {node: '>= 0.4'}

  typedarray-to-buffer@3.1.5:
    resolution: {integrity: sha1-qX7nqf9CaRufeD/xvFES/j/KkIA=, tarball: https://registry.npm.wps.cn/typedarray-to-buffer/download/typedarray-to-buffer-3.1.5.tgz}

  typescript@4.9.5:
    resolution: {integrity: sha1-CVl5+bzA0J2jJNWNA86Pg3TL5lo=, tarball: https://registry.npm.wps.cn/typescript/download/typescript-4.9.5.tgz}
    engines: {node: '>=4.2.0'}
    hasBin: true

  unbox-primitive@1.1.0:
    resolution: {integrity: sha1-jZ0snt7qhGDH81AzqIhnlEk00eI=, tarball: https://registry.npm.wps.cn/unbox-primitive/download/unbox-primitive-1.1.0.tgz}
    engines: {node: '>= 0.4'}

  underscore@1.12.1:
    resolution: {integrity: sha1-e7jMmz05fiAc+FUzNtJiVE6tgp4=, tarball: https://registry.npm.wps.cn/underscore/download/underscore-1.12.1.tgz}

  unicode-canonical-property-names-ecmascript@2.0.1:
    resolution: {integrity: sha1-yzFz/kfKdD4ighbko93EyE1ijMI=, tarball: https://registry.npm.wps.cn/unicode-canonical-property-names-ecmascript/download/unicode-canonical-property-names-ecmascript-2.0.1.tgz}
    engines: {node: '>=4'}

  unicode-match-property-ecmascript@2.0.0:
    resolution: {integrity: sha1-VP0W4OyxZ88Ezx91a9zJLrp5dsM=, tarball: https://registry.npm.wps.cn/unicode-match-property-ecmascript/download/unicode-match-property-ecmascript-2.0.0.tgz}
    engines: {node: '>=4'}

  unicode-match-property-value-ecmascript@2.2.0:
    resolution: {integrity: sha1-oEAa7nJxRZj3ObaLEE5P46DLPHE=, tarball: https://registry.npm.wps.cn/unicode-match-property-value-ecmascript/download/unicode-match-property-value-ecmascript-2.2.0.tgz}
    engines: {node: '>=4'}

  unicode-property-aliases-ecmascript@2.1.0:
    resolution: {integrity: sha1-Q9QeO+aYvUk++REHfJsTH4J+jM0=, tarball: https://registry.npm.wps.cn/unicode-property-aliases-ecmascript/download/unicode-property-aliases-ecmascript-2.1.0.tgz}
    engines: {node: '>=4'}

  unique-string@2.0.0:
    resolution: {integrity: sha1-OcZFH4GvsnSd4rIz4/fF6IQ72J0=, tarball: https://registry.npm.wps.cn/unique-string/download/unique-string-2.0.0.tgz}
    engines: {node: '>=8'}

  universalify@0.2.0:
    resolution: {integrity: sha1-ZFF2BWb6hXU0dFqx3elS0bF2G+A=, tarball: https://registry.npm.wps.cn/universalify/download/universalify-0.2.0.tgz}
    engines: {node: '>= 4.0.0'}

  universalify@2.0.1:
    resolution: {integrity: sha1-Fo78IYCWTmOG0GHglN9hr+I5sY0=, tarball: https://registry.npm.wps.cn/universalify/download/universalify-2.0.1.tgz}
    engines: {node: '>= 10.0.0'}

  unpipe@1.0.0:
    resolution: {integrity: sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=, tarball: https://registry.npm.wps.cn/unpipe/download/unpipe-1.0.0.tgz}
    engines: {node: '>= 0.8'}

  unquote@1.1.1:
    resolution: {integrity: sha1-j97XMk7G6IoP+LkF58CYzcCG1UQ=, tarball: https://registry.npm.wps.cn/unquote/download/unquote-1.1.1.tgz}

  upath@1.2.0:
    resolution: {integrity: sha1-j2bbzVWog6za5ECK+LA1pQRMGJQ=, tarball: https://registry.npm.wps.cn/upath/download/upath-1.2.0.tgz}
    engines: {node: '>=4'}

  update-browserslist-db@1.1.3:
    resolution: {integrity: sha1-NIN33SRSFvnnBg/1CxWht0C3VCA=, tarball: https://registry.npm.wps.cn/update-browserslist-db/download/update-browserslist-db-1.1.3.tgz}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'

  uri-js@4.4.1:
    resolution: {integrity: sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=, tarball: https://registry.npm.wps.cn/uri-js/download/uri-js-4.4.1.tgz}

  url-parse@1.5.10:
    resolution: {integrity: sha1-nTwvc2wddd070r5QfcwRHx4uqcE=, tarball: https://registry.npm.wps.cn/url-parse/download/url-parse-1.5.10.tgz}

  util-deprecate@1.0.2:
    resolution: {integrity: sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=, tarball: https://registry.npm.wps.cn/util-deprecate/download/util-deprecate-1.0.2.tgz}

  util.promisify@1.0.1:
    resolution: {integrity: sha1-a693dLgO6w91INi4HQeYKlmruu4=, tarball: https://registry.npm.wps.cn/util.promisify/download/util.promisify-1.0.1.tgz}

  utila@0.4.0:
    resolution: {integrity: sha1-ihagXURWV6Oupe7MWxKk+lN5dyw=, tarball: https://registry.npm.wps.cn/utila/download/utila-0.4.0.tgz}

  utils-merge@1.0.1:
    resolution: {integrity: sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=, tarball: https://registry.npm.wps.cn/utils-merge/download/utils-merge-1.0.1.tgz}
    engines: {node: '>= 0.4.0'}

  uuid@8.3.2:
    resolution: {integrity: sha1-gNW1ztJxu5r2xEXyGhoExgbO++I=, tarball: https://registry.npm.wps.cn/uuid/download/uuid-8.3.2.tgz}
    hasBin: true

  v8-compile-cache-lib@3.0.1:
    resolution: {integrity: sha1-Yzbo1xllyz01obu3hoRFp8BSZL8=, tarball: https://registry.npm.wps.cn/v8-compile-cache-lib/download/v8-compile-cache-lib-3.0.1.tgz}

  v8-to-istanbul@8.1.1:
    resolution: {integrity: sha1-d7dS/Tl14xu875OPhem9HHqNYO0=, tarball: https://registry.npm.wps.cn/v8-to-istanbul/download/v8-to-istanbul-8.1.1.tgz}
    engines: {node: '>=10.12.0'}

  vary@1.1.2:
    resolution: {integrity: sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=, tarball: https://registry.npm.wps.cn/vary/download/vary-1.1.2.tgz}
    engines: {node: '>= 0.8'}

  w3c-hr-time@1.0.2:
    resolution: {integrity: sha1-ConN9cwVgi35w2BUNnaWPgzDCM0=, tarball: https://registry.npm.wps.cn/w3c-hr-time/download/w3c-hr-time-1.0.2.tgz}
    deprecated: Use your platform's native performance.now() and performance.timeOrigin.

  w3c-xmlserializer@2.0.0:
    resolution: {integrity: sha1-PnEEoFt1FGzGD1ZDgLf2g6zxAgo=, tarball: https://registry.npm.wps.cn/w3c-xmlserializer/download/w3c-xmlserializer-2.0.0.tgz}
    engines: {node: '>=10'}

  walker@1.0.8:
    resolution: {integrity: sha1-vUmNtHev5XPcBBhfAR06uKjXZT8=, tarball: https://registry.npm.wps.cn/walker/download/walker-1.0.8.tgz}

  watchpack@2.4.4:
    resolution: {integrity: sha1-RzvacvCFBFPaZCUIHqRvwNdgKUc=, tarball: https://registry.npm.wps.cn/watchpack/download/watchpack-2.4.4.tgz}
    engines: {node: '>=10.13.0'}

  wbuf@1.7.3:
    resolution: {integrity: sha1-wdjRSTFtPqhShIiVy2oL/oh7h98=, tarball: https://registry.npm.wps.cn/wbuf/download/wbuf-1.7.3.tgz}

  web-vitals@2.1.4:
    resolution: {integrity: sha1-dlYxdaR1peg1Jk03NwT53ecYKQw=, tarball: https://registry.npm.wps.cn/web-vitals/download/web-vitals-2.1.4.tgz}

  webidl-conversions@4.0.2:
    resolution: {integrity: sha1-qFWYCx8LazWbodXZ+zmulB+qY60=, tarball: https://registry.npm.wps.cn/webidl-conversions/download/webidl-conversions-4.0.2.tgz}

  webidl-conversions@5.0.0:
    resolution: {integrity: sha1-rlnIoAsSFUOirMZcBDT1ew/BGv8=, tarball: https://registry.npm.wps.cn/webidl-conversions/download/webidl-conversions-5.0.0.tgz}
    engines: {node: '>=8'}

  webidl-conversions@6.1.0:
    resolution: {integrity: sha1-kRG01+qArNQPUnDWZmIa+ni2lRQ=, tarball: https://registry.npm.wps.cn/webidl-conversions/download/webidl-conversions-6.1.0.tgz}
    engines: {node: '>=10.4'}

  webpack-dev-middleware@5.3.4:
    resolution: {integrity: sha1-63s5KBy84Q4QTrK4vytj/OSaNRc=, tarball: https://registry.npm.wps.cn/webpack-dev-middleware/download/webpack-dev-middleware-5.3.4.tgz}
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0

  webpack-dev-server@4.15.2:
    resolution: {integrity: sha1-ngxwpCoBJWCGCtsYaYbaEkgzMXM=, tarball: https://registry.npm.wps.cn/webpack-dev-server/download/webpack-dev-server-4.15.2.tgz}
    engines: {node: '>= 12.13.0'}
    hasBin: true
    peerDependencies:
      webpack: ^4.37.0 || ^5.0.0
      webpack-cli: '*'
    peerDependenciesMeta:
      webpack:
        optional: true
      webpack-cli:
        optional: true

  webpack-manifest-plugin@4.1.1:
    resolution: {integrity: sha1-EPjb9HFP+TohXVpFvMQW2AUG+U8=, tarball: https://registry.npm.wps.cn/webpack-manifest-plugin/download/webpack-manifest-plugin-4.1.1.tgz}
    engines: {node: '>=12.22.0'}
    peerDependencies:
      webpack: ^4.44.2 || ^5.47.0

  webpack-merge@5.10.0:
    resolution: {integrity: sha1-o61ddzJB6caCgDq/Yo1M1iuKQXc=, tarball: https://registry.npm.wps.cn/webpack-merge/download/webpack-merge-5.10.0.tgz}
    engines: {node: '>=10.0.0'}

  webpack-sources@1.4.3:
    resolution: {integrity: sha1-7t2OwLko+/HL/plOItLYkPMwqTM=, tarball: https://registry.npm.wps.cn/webpack-sources/download/webpack-sources-1.4.3.tgz}

  webpack-sources@2.3.1:
    resolution: {integrity: sha1-Vw3grxY5Sf4nIjPCzv4bVvdFEf0=, tarball: https://registry.npm.wps.cn/webpack-sources/download/webpack-sources-2.3.1.tgz}
    engines: {node: '>=10.13.0'}

  webpack-sources@3.3.3:
    resolution: {integrity: sha1-1L9/mQlnXXoHD/FNDvKk88mCxyM=, tarball: https://registry.npm.wps.cn/webpack-sources/download/webpack-sources-3.3.3.tgz}
    engines: {node: '>=10.13.0'}

  webpack@5.100.0:
    resolution: {integrity: sha1-+j7yNbOJab0N7VMpeixEpfCS+PQ=, tarball: https://registry.npm.wps.cn/webpack/download/webpack-5.100.0.tgz}
    engines: {node: '>=10.13.0'}
    hasBin: true
    peerDependencies:
      webpack-cli: '*'
    peerDependenciesMeta:
      webpack-cli:
        optional: true

  websocket-driver@0.7.4:
    resolution: {integrity: sha1-ia1Slbv2S0gKvLox5JU6ynBvV2A=, tarball: https://registry.npm.wps.cn/websocket-driver/download/websocket-driver-0.7.4.tgz}
    engines: {node: '>=0.8.0'}

  websocket-extensions@0.1.4:
    resolution: {integrity: sha1-f4RzvIOd/YdgituV1+sHUhFXikI=, tarball: https://registry.npm.wps.cn/websocket-extensions/download/websocket-extensions-0.1.4.tgz}
    engines: {node: '>=0.8.0'}

  whatwg-encoding@1.0.5:
    resolution: {integrity: sha1-WrrPd3wyFmpR0IXWtPPn0nET3bA=, tarball: https://registry.npm.wps.cn/whatwg-encoding/download/whatwg-encoding-1.0.5.tgz}

  whatwg-fetch@3.6.20:
    resolution: {integrity: sha1-WAzm15H6zskdN8cokJlaC0jTHHA=, tarball: https://registry.npm.wps.cn/whatwg-fetch/download/whatwg-fetch-3.6.20.tgz}

  whatwg-mimetype@2.3.0:
    resolution: {integrity: sha1-PUseAxLSB5h5+Cav8Y2+7KWWD78=, tarball: https://registry.npm.wps.cn/whatwg-mimetype/download/whatwg-mimetype-2.3.0.tgz}

  whatwg-url@7.1.0:
    resolution: {integrity: sha1-wsSS8eymEpiO/T0iZr4bn8YXDQY=, tarball: https://registry.npm.wps.cn/whatwg-url/download/whatwg-url-7.1.0.tgz}

  whatwg-url@8.7.0:
    resolution: {integrity: sha1-ZWp45RD/jzk3vAvL6fXArDWUG3c=, tarball: https://registry.npm.wps.cn/whatwg-url/download/whatwg-url-8.7.0.tgz}
    engines: {node: '>=10'}

  which-boxed-primitive@1.1.1:
    resolution: {integrity: sha1-127Cfff6Fl8Y1YCDdKX+I8KbF24=, tarball: https://registry.npm.wps.cn/which-boxed-primitive/download/which-boxed-primitive-1.1.1.tgz}
    engines: {node: '>= 0.4'}

  which-builtin-type@1.2.1:
    resolution: {integrity: sha1-iRg9obSQerCJprAgKcxdjWV0Jw4=, tarball: https://registry.npm.wps.cn/which-builtin-type/download/which-builtin-type-1.2.1.tgz}
    engines: {node: '>= 0.4'}

  which-collection@1.0.2:
    resolution: {integrity: sha1-Yn73YkOSChB+fOjpYZHevksWwqA=, tarball: https://registry.npm.wps.cn/which-collection/download/which-collection-1.0.2.tgz}
    engines: {node: '>= 0.4'}

  which-typed-array@1.1.19:
    resolution: {integrity: sha1-3wOELocLa4jhF1JKSzZLb8aJ+VY=, tarball: https://registry.npm.wps.cn/which-typed-array/download/which-typed-array-1.1.19.tgz}
    engines: {node: '>= 0.4'}

  which@1.3.1:
    resolution: {integrity: sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=, tarball: https://registry.npm.wps.cn/which/download/which-1.3.1.tgz}
    hasBin: true

  which@2.0.2:
    resolution: {integrity: sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=, tarball: https://registry.npm.wps.cn/which/download/which-2.0.2.tgz}
    engines: {node: '>= 8'}
    hasBin: true

  wildcard@2.0.1:
    resolution: {integrity: sha1-WrENAkhxmJVINrY0n3T/+WHhD2c=, tarball: https://registry.npm.wps.cn/wildcard/download/wildcard-2.0.1.tgz}

  word-wrap@1.2.5:
    resolution: {integrity: sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=, tarball: https://registry.npm.wps.cn/word-wrap/download/word-wrap-1.2.5.tgz}
    engines: {node: '>=0.10.0'}

  workbox-background-sync@6.6.0:
    resolution: {integrity: sha1-BYwwBnLVibHY8tg42UCjcbzQ7Ak=, tarball: https://registry.npm.wps.cn/workbox-background-sync/download/workbox-background-sync-6.6.0.tgz}

  workbox-broadcast-update@6.6.0:
    resolution: {integrity: sha1-CrubY4xf5w4TBRYd922xkm/4PSE=, tarball: https://registry.npm.wps.cn/workbox-broadcast-update/download/workbox-broadcast-update-6.6.0.tgz}

  workbox-build@6.6.0:
    resolution: {integrity: sha1-i7ekuGzalbcqMD2R7l3FvYh3dcE=, tarball: https://registry.npm.wps.cn/workbox-build/download/workbox-build-6.6.0.tgz}
    engines: {node: '>=10.0.0'}

  workbox-cacheable-response@6.6.0:
    resolution: {integrity: sha1-9uRTwdoUkTkrow4QcKEM5OhTlQs=, tarball: https://registry.npm.wps.cn/workbox-cacheable-response/download/workbox-cacheable-response-6.6.0.tgz}
    deprecated: workbox-background-sync@6.6.0

  workbox-core@6.6.0:
    resolution: {integrity: sha1-0PaSnjOOACXUEikTkOdGKl+HlXY=, tarball: https://registry.npm.wps.cn/workbox-core/download/workbox-core-6.6.0.tgz}

  workbox-expiration@6.6.0:
    resolution: {integrity: sha1-IpnIapydzKF+7Ej2isiLhlK8jk4=, tarball: https://registry.npm.wps.cn/workbox-expiration/download/workbox-expiration-6.6.0.tgz}

  workbox-google-analytics@6.6.0:
    resolution: {integrity: sha1-DbmboKCPzWfdz84lXr102D/GG6E=, tarball: https://registry.npm.wps.cn/workbox-google-analytics/download/workbox-google-analytics-6.6.0.tgz}
    deprecated: It is not compatible with newer versions of GA starting with v4, as long as you are using GAv3 it should be ok, but the package is not longer being maintained

  workbox-navigation-preload@6.6.0:
    resolution: {integrity: sha1-Ly27cXjFdioWPdLzsi/9WGcE868=, tarball: https://registry.npm.wps.cn/workbox-navigation-preload/download/workbox-navigation-preload-6.6.0.tgz}

  workbox-precaching@6.6.0:
    resolution: {integrity: sha1-jHAUczFkJ/r61Vniat+0S3Kub/w=, tarball: https://registry.npm.wps.cn/workbox-precaching/download/workbox-precaching-6.6.0.tgz}

  workbox-range-requests@6.6.0:
    resolution: {integrity: sha1-3G6JyhaU5EIHX8f6etnzxGlKhI4=, tarball: https://registry.npm.wps.cn/workbox-range-requests/download/workbox-range-requests-6.6.0.tgz}

  workbox-recipes@6.6.0:
    resolution: {integrity: sha1-vsBtT1YFsS6YPQjMusTnCTJJbGw=, tarball: https://registry.npm.wps.cn/workbox-recipes/download/workbox-recipes-6.6.0.tgz}

  workbox-routing@6.6.0:
    resolution: {integrity: sha1-MBKKZJuEYMVXrQ5wNTB34yVABVg=, tarball: https://registry.npm.wps.cn/workbox-routing/download/workbox-routing-6.6.0.tgz}

  workbox-strategies@6.6.0:
    resolution: {integrity: sha1-cbtgpG66oMUoSRuLUh9aHHs18MU=, tarball: https://registry.npm.wps.cn/workbox-strategies/download/workbox-strategies-6.6.0.tgz}

  workbox-streams@6.6.0:
    resolution: {integrity: sha1-C+G6i0/Gkq71/++oMfXpu+7g/Zw=, tarball: https://registry.npm.wps.cn/workbox-streams/download/workbox-streams-6.6.0.tgz}

  workbox-sw@6.6.0:
    resolution: {integrity: sha1-4H2FqK86SuMRum5F0XnsqkAtSYM=, tarball: https://registry.npm.wps.cn/workbox-sw/download/workbox-sw-6.6.0.tgz}

  workbox-webpack-plugin@6.6.0:
    resolution: {integrity: sha1-Om/CmP+1RtC9FXLzqi9D/Nuc1As=, tarball: https://registry.npm.wps.cn/workbox-webpack-plugin/download/workbox-webpack-plugin-6.6.0.tgz}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      webpack: ^4.4.0 || ^5.9.0

  workbox-window@6.6.0:
    resolution: {integrity: sha1-obc4NA6oMyOXjqmsyPUnySQGVtY=, tarball: https://registry.npm.wps.cn/workbox-window/download/workbox-window-6.6.0.tgz}

  wrap-ansi@7.0.0:
    resolution: {integrity: sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=, tarball: https://registry.npm.wps.cn/wrap-ansi/download/wrap-ansi-7.0.0.tgz}
    engines: {node: '>=10'}

  wrap-ansi@8.1.0:
    resolution: {integrity: sha1-VtwiNo7lcPrOG0mBmXXZuaXq0hQ=, tarball: https://registry.npm.wps.cn/wrap-ansi/download/wrap-ansi-8.1.0.tgz}
    engines: {node: '>=12'}

  wrappy@1.0.2:
    resolution: {integrity: sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=, tarball: https://registry.npm.wps.cn/wrappy/download/wrappy-1.0.2.tgz}

  write-file-atomic@3.0.3:
    resolution: {integrity: sha1-Vr1cWlxwSBzRnFcb05q5ZaXeVug=, tarball: https://registry.npm.wps.cn/write-file-atomic/download/write-file-atomic-3.0.3.tgz}

  ws@7.5.10:
    resolution: {integrity: sha1-WLXCDcKBYz9sGRE/ObNJvYvVWNk=, tarball: https://registry.npm.wps.cn/ws/download/ws-7.5.10.tgz}
    engines: {node: '>=8.3.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: ^5.0.2
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  ws@8.18.3:
    resolution: {integrity: sha1-tWuIq//eYnkcY5FwQAyT3LDJVHI=, tarball: https://registry.npm.wps.cn/ws/download/ws-8.18.3.tgz}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: '>=5.0.2'
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  xml-name-validator@3.0.0:
    resolution: {integrity: sha1-auc+Bt5NjG5H+fsYH3jWSK1FfGo=, tarball: https://registry.npm.wps.cn/xml-name-validator/download/xml-name-validator-3.0.0.tgz}

  xmlchars@2.2.0:
    resolution: {integrity: sha1-Bg/hvLf5x2/ioX24apvDq4lCEMs=, tarball: https://registry.npm.wps.cn/xmlchars/download/xmlchars-2.2.0.tgz}

  y18n@5.0.8:
    resolution: {integrity: sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=, tarball: https://registry.npm.wps.cn/y18n/download/y18n-5.0.8.tgz}
    engines: {node: '>=10'}

  yallist@3.1.1:
    resolution: {integrity: sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=, tarball: https://registry.npm.wps.cn/yallist/download/yallist-3.1.1.tgz}

  yaml@1.10.2:
    resolution: {integrity: sha1-IwHF/78StGfejaIzOkWeKeeSDks=, tarball: https://registry.npm.wps.cn/yaml/download/yaml-1.10.2.tgz}
    engines: {node: '>= 6'}

  yaml@2.8.0:
    resolution: {integrity: sha1-FfjJhmIRvcLTeBoIkORNT6Gl//Y=, tarball: https://registry.npm.wps.cn/yaml/download/yaml-2.8.0.tgz}
    engines: {node: '>= 14.6'}
    hasBin: true

  yargs-parser@20.2.9:
    resolution: {integrity: sha1-LrfcOwKJcY/ClfNidThFxBoMlO4=, tarball: https://registry.npm.wps.cn/yargs-parser/download/yargs-parser-20.2.9.tgz}
    engines: {node: '>=10'}

  yargs@16.2.0:
    resolution: {integrity: sha1-HIK/D2tqZur85+8w43b0mhJHf2Y=, tarball: https://registry.npm.wps.cn/yargs/download/yargs-16.2.0.tgz}
    engines: {node: '>=10'}

  yn@3.1.1:
    resolution: {integrity: sha1-HodAGgnXZ8HV6rJqbkwYUYLS61A=, tarball: https://registry.npm.wps.cn/yn/download/yn-3.1.1.tgz}
    engines: {node: '>=6'}

  yocto-queue@0.1.0:
    resolution: {integrity: sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=, tarball: https://registry.npm.wps.cn/yocto-queue/download/yocto-queue-0.1.0.tgz}
    engines: {node: '>=10'}

snapshots:

  '@adobe/css-tools@4.4.3': {}

  '@alloc/quick-lru@5.2.0': {}

  '@ampproject/remapping@2.3.0':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.12
      '@jridgewell/trace-mapping': 0.3.29

  '@apideck/better-ajv-errors@0.3.6(ajv@8.17.1)':
    dependencies:
      ajv: 8.17.1
      json-schema: 0.4.0
      jsonpointer: 5.0.1
      leven: 3.1.0

  '@babel/code-frame@7.27.1':
    dependencies:
      '@babel/helper-validator-identifier': 7.27.1
      js-tokens: 4.0.0
      picocolors: 1.1.1

  '@babel/compat-data@7.28.0': {}

  '@babel/core@7.28.0':
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@babel/code-frame': 7.27.1
      '@babel/generator': 7.28.0
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-module-transforms': 7.27.3(@babel/core@7.28.0)
      '@babel/helpers': 7.27.6
      '@babel/parser': 7.28.0
      '@babel/template': 7.27.2
      '@babel/traverse': 7.28.0
      '@babel/types': 7.28.0
      convert-source-map: 2.0.0
      debug: 4.4.1
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/eslint-parser@7.28.0(@babel/core@7.28.0)(eslint@8.57.1)':
    dependencies:
      '@babel/core': 7.28.0
      '@nicolo-ribaudo/eslint-scope-5-internals': 5.1.1-v1
      eslint: 8.57.1
      eslint-visitor-keys: 2.1.0
      semver: 6.3.1

  '@babel/generator@7.28.0':
    dependencies:
      '@babel/parser': 7.28.0
      '@babel/types': 7.28.0
      '@jridgewell/gen-mapping': 0.3.12
      '@jridgewell/trace-mapping': 0.3.29
      jsesc: 3.1.0

  '@babel/helper-annotate-as-pure@7.27.3':
    dependencies:
      '@babel/types': 7.28.0

  '@babel/helper-compilation-targets@7.27.2':
    dependencies:
      '@babel/compat-data': 7.28.0
      '@babel/helper-validator-option': 7.27.1
      browserslist: 4.25.1
      lru-cache: 5.1.1
      semver: 6.3.1

  '@babel/helper-create-class-features-plugin@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-annotate-as-pure': 7.27.3
      '@babel/helper-member-expression-to-functions': 7.27.1
      '@babel/helper-optimise-call-expression': 7.27.1
      '@babel/helper-replace-supers': 7.27.1(@babel/core@7.28.0)
      '@babel/helper-skip-transparent-expression-wrappers': 7.27.1
      '@babel/traverse': 7.28.0
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-create-regexp-features-plugin@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-annotate-as-pure': 7.27.3
      regexpu-core: 6.2.0
      semver: 6.3.1

  '@babel/helper-define-polyfill-provider@0.6.5(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-plugin-utils': 7.27.1
      debug: 4.4.1
      lodash.debounce: 4.0.8
      resolve: 1.22.10
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-globals@7.28.0': {}

  '@babel/helper-member-expression-to-functions@7.27.1':
    dependencies:
      '@babel/traverse': 7.28.0
      '@babel/types': 7.28.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-imports@7.27.1':
    dependencies:
      '@babel/traverse': 7.28.0
      '@babel/types': 7.28.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-transforms@7.27.3(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-module-imports': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-optimise-call-expression@7.27.1':
    dependencies:
      '@babel/types': 7.28.0

  '@babel/helper-plugin-utils@7.27.1': {}

  '@babel/helper-remap-async-to-generator@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-annotate-as-pure': 7.27.3
      '@babel/helper-wrap-function': 7.27.1
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-replace-supers@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-member-expression-to-functions': 7.27.1
      '@babel/helper-optimise-call-expression': 7.27.1
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-skip-transparent-expression-wrappers@7.27.1':
    dependencies:
      '@babel/traverse': 7.28.0
      '@babel/types': 7.28.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-string-parser@7.27.1': {}

  '@babel/helper-validator-identifier@7.27.1': {}

  '@babel/helper-validator-option@7.27.1': {}

  '@babel/helper-wrap-function@7.27.1':
    dependencies:
      '@babel/template': 7.27.2
      '@babel/traverse': 7.28.0
      '@babel/types': 7.28.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helpers@7.27.6':
    dependencies:
      '@babel/template': 7.27.2
      '@babel/types': 7.28.0

  '@babel/parser@7.28.0':
    dependencies:
      '@babel/types': 7.28.0

  '@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-bugfix-safari-class-field-initializer-scope@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-skip-transparent-expression-wrappers': 7.27.1
      '@babel/plugin-transform-optional-chaining': 7.27.1(@babel/core@7.28.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-proposal-class-properties@7.18.6(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-class-features-plugin': 7.27.1(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-proposal-decorators@7.28.0(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-class-features-plugin': 7.27.1(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/plugin-syntax-decorators': 7.27.1(@babel/core@7.28.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-proposal-nullish-coalescing-operator@7.18.6(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3(@babel/core@7.28.0)

  '@babel/plugin-proposal-numeric-separator@7.18.6(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/plugin-syntax-numeric-separator': 7.10.4(@babel/core@7.28.0)

  '@babel/plugin-proposal-optional-chaining@7.21.0(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-skip-transparent-expression-wrappers': 7.27.1
      '@babel/plugin-syntax-optional-chaining': 7.8.3(@babel/core@7.28.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-proposal-private-methods@7.18.6(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-class-features-plugin': 7.27.1(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0

  '@babel/plugin-proposal-private-property-in-object@7.21.11(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-annotate-as-pure': 7.27.3
      '@babel/helper-create-class-features-plugin': 7.27.1(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/plugin-syntax-private-property-in-object': 7.14.5(@babel/core@7.28.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-bigint@7.8.3(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-decorators@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-flow@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-import-assertions@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-typescript@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-unicode-sets-regex@7.18.6(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-regexp-features-plugin': 7.27.1(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-arrow-functions@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-async-generator-functions@7.28.0(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-remap-async-to-generator': 7.27.1(@babel/core@7.28.0)
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-async-to-generator@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-module-imports': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-remap-async-to-generator': 7.27.1(@babel/core@7.28.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-block-scoped-functions@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-block-scoping@7.28.0(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-class-properties@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-class-features-plugin': 7.27.1(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-class-static-block@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-class-features-plugin': 7.27.1(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-classes@7.28.0(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-annotate-as-pure': 7.27.3
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-globals': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-replace-supers': 7.27.1(@babel/core@7.28.0)
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-computed-properties@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/template': 7.27.2

  '@babel/plugin-transform-destructuring@7.28.0(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-dotall-regex@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-regexp-features-plugin': 7.27.1(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-duplicate-keys@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-duplicate-named-capturing-groups-regex@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-regexp-features-plugin': 7.27.1(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-dynamic-import@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-explicit-resource-management@7.28.0(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/plugin-transform-destructuring': 7.28.0(@babel/core@7.28.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-exponentiation-operator@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-export-namespace-from@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-flow-strip-types@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/plugin-syntax-flow': 7.27.1(@babel/core@7.28.0)

  '@babel/plugin-transform-for-of@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-skip-transparent-expression-wrappers': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-function-name@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-json-strings@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-literals@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-logical-assignment-operators@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-member-expression-literals@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-modules-amd@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-module-transforms': 7.27.3(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-modules-commonjs@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-module-transforms': 7.27.3(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-modules-systemjs@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-module-transforms': 7.27.3(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-modules-umd@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-module-transforms': 7.27.3(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-named-capturing-groups-regex@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-regexp-features-plugin': 7.27.1(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-new-target@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-nullish-coalescing-operator@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-numeric-separator@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-object-rest-spread@7.28.0(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/plugin-transform-destructuring': 7.28.0(@babel/core@7.28.0)
      '@babel/plugin-transform-parameters': 7.27.7(@babel/core@7.28.0)
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-object-super@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-replace-supers': 7.27.1(@babel/core@7.28.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-optional-catch-binding@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-optional-chaining@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-skip-transparent-expression-wrappers': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-parameters@7.27.7(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-private-methods@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-class-features-plugin': 7.27.1(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-private-property-in-object@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-annotate-as-pure': 7.27.3
      '@babel/helper-create-class-features-plugin': 7.27.1(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-property-literals@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-react-constant-elements@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-react-display-name@7.28.0(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-react-jsx-development@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/plugin-transform-react-jsx': 7.27.1(@babel/core@7.28.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-react-jsx@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-annotate-as-pure': 7.27.3
      '@babel/helper-module-imports': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/plugin-syntax-jsx': 7.27.1(@babel/core@7.28.0)
      '@babel/types': 7.28.0
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-react-pure-annotations@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-annotate-as-pure': 7.27.3
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-regenerator@7.28.0(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-regexp-modifiers@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-regexp-features-plugin': 7.27.1(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-reserved-words@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-runtime@7.28.0(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-module-imports': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
      babel-plugin-polyfill-corejs2: 0.4.14(@babel/core@7.28.0)
      babel-plugin-polyfill-corejs3: 0.13.0(@babel/core@7.28.0)
      babel-plugin-polyfill-regenerator: 0.6.5(@babel/core@7.28.0)
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-shorthand-properties@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-spread@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-skip-transparent-expression-wrappers': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-sticky-regex@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-template-literals@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-typeof-symbol@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-typescript@7.28.0(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-annotate-as-pure': 7.27.3
      '@babel/helper-create-class-features-plugin': 7.27.1(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-skip-transparent-expression-wrappers': 7.27.1
      '@babel/plugin-syntax-typescript': 7.27.1(@babel/core@7.28.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-unicode-escapes@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-unicode-property-regex@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-regexp-features-plugin': 7.27.1(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-unicode-regex@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-regexp-features-plugin': 7.27.1(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-unicode-sets-regex@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-regexp-features-plugin': 7.27.1(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/preset-env@7.28.0(@babel/core@7.28.0)':
    dependencies:
      '@babel/compat-data': 7.28.0
      '@babel/core': 7.28.0
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-validator-option': 7.27.1
      '@babel/plugin-bugfix-firefox-class-in-computed-class-key': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-bugfix-safari-class-field-initializer-scope': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-proposal-private-property-in-object': 7.21.0-placeholder-for-preset-env.2(@babel/core@7.28.0)
      '@babel/plugin-syntax-import-assertions': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-syntax-import-attributes': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-syntax-unicode-sets-regex': 7.18.6(@babel/core@7.28.0)
      '@babel/plugin-transform-arrow-functions': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-async-generator-functions': 7.28.0(@babel/core@7.28.0)
      '@babel/plugin-transform-async-to-generator': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-block-scoped-functions': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-block-scoping': 7.28.0(@babel/core@7.28.0)
      '@babel/plugin-transform-class-properties': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-class-static-block': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-classes': 7.28.0(@babel/core@7.28.0)
      '@babel/plugin-transform-computed-properties': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-destructuring': 7.28.0(@babel/core@7.28.0)
      '@babel/plugin-transform-dotall-regex': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-duplicate-keys': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-duplicate-named-capturing-groups-regex': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-dynamic-import': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-explicit-resource-management': 7.28.0(@babel/core@7.28.0)
      '@babel/plugin-transform-exponentiation-operator': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-export-namespace-from': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-for-of': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-function-name': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-json-strings': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-literals': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-logical-assignment-operators': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-member-expression-literals': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-modules-amd': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-modules-commonjs': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-modules-systemjs': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-modules-umd': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-named-capturing-groups-regex': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-new-target': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-nullish-coalescing-operator': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-numeric-separator': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-object-rest-spread': 7.28.0(@babel/core@7.28.0)
      '@babel/plugin-transform-object-super': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-optional-catch-binding': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-optional-chaining': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-parameters': 7.27.7(@babel/core@7.28.0)
      '@babel/plugin-transform-private-methods': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-private-property-in-object': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-property-literals': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-regenerator': 7.28.0(@babel/core@7.28.0)
      '@babel/plugin-transform-regexp-modifiers': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-reserved-words': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-shorthand-properties': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-spread': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-sticky-regex': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-template-literals': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-typeof-symbol': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-unicode-escapes': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-unicode-property-regex': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-unicode-regex': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-unicode-sets-regex': 7.27.1(@babel/core@7.28.0)
      '@babel/preset-modules': 0.1.6-no-external-plugins(@babel/core@7.28.0)
      babel-plugin-polyfill-corejs2: 0.4.14(@babel/core@7.28.0)
      babel-plugin-polyfill-corejs3: 0.13.0(@babel/core@7.28.0)
      babel-plugin-polyfill-regenerator: 0.6.5(@babel/core@7.28.0)
      core-js-compat: 3.43.0
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/preset-modules@0.1.6-no-external-plugins(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/types': 7.28.0
      esutils: 2.0.3

  '@babel/preset-react@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-validator-option': 7.27.1
      '@babel/plugin-transform-react-display-name': 7.28.0(@babel/core@7.28.0)
      '@babel/plugin-transform-react-jsx': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-react-jsx-development': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-react-pure-annotations': 7.27.1(@babel/core@7.28.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/preset-typescript@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-validator-option': 7.27.1
      '@babel/plugin-syntax-jsx': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-modules-commonjs': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-typescript': 7.28.0(@babel/core@7.28.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/runtime@7.27.6': {}

  '@babel/template@7.27.2':
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/parser': 7.28.0
      '@babel/types': 7.28.0

  '@babel/traverse@7.28.0':
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/generator': 7.28.0
      '@babel/helper-globals': 7.28.0
      '@babel/parser': 7.28.0
      '@babel/template': 7.27.2
      '@babel/types': 7.28.0
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  '@babel/types@7.28.0':
    dependencies:
      '@babel/helper-string-parser': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1

  '@bcoe/v8-coverage@0.2.3': {}

  '@craco/craco@7.1.0(@types/node@16.18.126)(postcss@8.5.6)(react-scripts@5.0.1(@babel/plugin-syntax-flow@7.27.1(@babel/core@7.28.0))(@babel/plugin-transform-react-jsx@7.27.1(@babel/core@7.28.0))(@types/babel__core@7.20.5)(eslint@8.57.1)(react@18.3.1)(ts-node@10.9.2(@types/node@16.18.126)(typescript@4.9.5))(type-fest@0.21.3)(typescript@4.9.5))(typescript@4.9.5)':
    dependencies:
      autoprefixer: 10.4.21(postcss@8.5.6)
      cosmiconfig: 7.1.0
      cosmiconfig-typescript-loader: 1.0.9(@types/node@16.18.126)(cosmiconfig@7.1.0)(typescript@4.9.5)
      cross-spawn: 7.0.6
      lodash: 4.17.21
      react-scripts: 5.0.1(@babel/plugin-syntax-flow@7.27.1(@babel/core@7.28.0))(@babel/plugin-transform-react-jsx@7.27.1(@babel/core@7.28.0))(@types/babel__core@7.20.5)(eslint@8.57.1)(react@18.3.1)(ts-node@10.9.2(@types/node@16.18.126)(typescript@4.9.5))(type-fest@0.21.3)(typescript@4.9.5)
      semver: 7.7.2
      webpack-merge: 5.10.0
    transitivePeerDependencies:
      - '@swc/core'
      - '@swc/wasm'
      - '@types/node'
      - postcss
      - typescript

  '@craco/types@7.1.0(eslint@8.57.1)(postcss@8.5.6)':
    dependencies:
      '@babel/types': 7.28.0
      '@jest/types': 27.5.1
      '@types/eslint': 8.56.12
      autoprefixer: 10.4.21(postcss@8.5.6)
      eslint-webpack-plugin: 3.2.0(eslint@8.57.1)(webpack@5.100.0)
      webpack: 5.100.0
    transitivePeerDependencies:
      - '@swc/core'
      - esbuild
      - eslint
      - postcss
      - uglify-js
      - webpack-cli

  '@cspotcode/source-map-support@0.8.1':
    dependencies:
      '@jridgewell/trace-mapping': 0.3.9

  '@csstools/normalize.css@12.1.1': {}

  '@csstools/postcss-cascade-layers@1.1.1(postcss@8.5.6)':
    dependencies:
      '@csstools/selector-specificity': 2.2.0(postcss-selector-parser@6.1.2)
      postcss: 8.5.6
      postcss-selector-parser: 6.1.2

  '@csstools/postcss-color-function@1.1.1(postcss@8.5.6)':
    dependencies:
      '@csstools/postcss-progressive-custom-properties': 1.3.0(postcss@8.5.6)
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  '@csstools/postcss-font-format-keywords@1.0.1(postcss@8.5.6)':
    dependencies:
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  '@csstools/postcss-hwb-function@1.0.2(postcss@8.5.6)':
    dependencies:
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  '@csstools/postcss-ic-unit@1.0.1(postcss@8.5.6)':
    dependencies:
      '@csstools/postcss-progressive-custom-properties': 1.3.0(postcss@8.5.6)
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  '@csstools/postcss-is-pseudo-class@2.0.7(postcss@8.5.6)':
    dependencies:
      '@csstools/selector-specificity': 2.2.0(postcss-selector-parser@6.1.2)
      postcss: 8.5.6
      postcss-selector-parser: 6.1.2

  '@csstools/postcss-nested-calc@1.0.0(postcss@8.5.6)':
    dependencies:
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  '@csstools/postcss-normalize-display-values@1.0.1(postcss@8.5.6)':
    dependencies:
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  '@csstools/postcss-oklab-function@1.1.1(postcss@8.5.6)':
    dependencies:
      '@csstools/postcss-progressive-custom-properties': 1.3.0(postcss@8.5.6)
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  '@csstools/postcss-progressive-custom-properties@1.3.0(postcss@8.5.6)':
    dependencies:
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  '@csstools/postcss-stepped-value-functions@1.0.1(postcss@8.5.6)':
    dependencies:
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  '@csstools/postcss-text-decoration-shorthand@1.0.0(postcss@8.5.6)':
    dependencies:
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  '@csstools/postcss-trigonometric-functions@1.0.2(postcss@8.5.6)':
    dependencies:
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  '@csstools/postcss-unset-value@1.0.2(postcss@8.5.6)':
    dependencies:
      postcss: 8.5.6

  '@csstools/selector-specificity@2.2.0(postcss-selector-parser@6.1.2)':
    dependencies:
      postcss-selector-parser: 6.1.2

  '@eslint-community/eslint-utils@4.7.0(eslint@8.57.1)':
    dependencies:
      eslint: 8.57.1
      eslint-visitor-keys: 3.4.3

  '@eslint-community/regexpp@4.12.1': {}

  '@eslint/eslintrc@2.1.4':
    dependencies:
      ajv: 6.12.6
      debug: 4.4.1
      espree: 9.6.1
      globals: 13.24.0
      ignore: 5.3.2
      import-fresh: 3.3.1
      js-yaml: 4.1.0
      minimatch: 3.1.2
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color

  '@eslint/js@8.57.1': {}

  '@humanwhocodes/config-array@0.13.0':
    dependencies:
      '@humanwhocodes/object-schema': 2.0.3
      debug: 4.4.1
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color

  '@humanwhocodes/module-importer@1.0.1': {}

  '@humanwhocodes/object-schema@2.0.3': {}

  '@isaacs/cliui@8.0.2':
    dependencies:
      string-width: 5.1.2
      string-width-cjs: string-width@4.2.3
      strip-ansi: 7.1.0
      strip-ansi-cjs: strip-ansi@6.0.1
      wrap-ansi: 8.1.0
      wrap-ansi-cjs: wrap-ansi@7.0.0

  '@istanbuljs/load-nyc-config@1.1.0':
    dependencies:
      camelcase: 5.3.1
      find-up: 4.1.0
      get-package-type: 0.1.0
      js-yaml: 3.14.1
      resolve-from: 5.0.0

  '@istanbuljs/schema@0.1.3': {}

  '@jest/console@27.5.1':
    dependencies:
      '@jest/types': 27.5.1
      '@types/node': 16.18.126
      chalk: 4.1.2
      jest-message-util: 27.5.1
      jest-util: 27.5.1
      slash: 3.0.0

  '@jest/console@28.1.3':
    dependencies:
      '@jest/types': 28.1.3
      '@types/node': 16.18.126
      chalk: 4.1.2
      jest-message-util: 28.1.3
      jest-util: 28.1.3
      slash: 3.0.0

  '@jest/core@27.5.1(ts-node@10.9.2(@types/node@16.18.126)(typescript@4.9.5))':
    dependencies:
      '@jest/console': 27.5.1
      '@jest/reporters': 27.5.1
      '@jest/test-result': 27.5.1
      '@jest/transform': 27.5.1
      '@jest/types': 27.5.1
      '@types/node': 16.18.126
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      emittery: 0.8.1
      exit: 0.1.2
      graceful-fs: 4.2.11
      jest-changed-files: 27.5.1
      jest-config: 27.5.1(ts-node@10.9.2(@types/node@16.18.126)(typescript@4.9.5))
      jest-haste-map: 27.5.1
      jest-message-util: 27.5.1
      jest-regex-util: 27.5.1
      jest-resolve: 27.5.1
      jest-resolve-dependencies: 27.5.1
      jest-runner: 27.5.1
      jest-runtime: 27.5.1
      jest-snapshot: 27.5.1
      jest-util: 27.5.1
      jest-validate: 27.5.1
      jest-watcher: 27.5.1
      micromatch: 4.0.8
      rimraf: 3.0.2
      slash: 3.0.0
      strip-ansi: 6.0.1
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - ts-node
      - utf-8-validate

  '@jest/environment@27.5.1':
    dependencies:
      '@jest/fake-timers': 27.5.1
      '@jest/types': 27.5.1
      '@types/node': 16.18.126
      jest-mock: 27.5.1

  '@jest/fake-timers@27.5.1':
    dependencies:
      '@jest/types': 27.5.1
      '@sinonjs/fake-timers': 8.1.0
      '@types/node': 16.18.126
      jest-message-util: 27.5.1
      jest-mock: 27.5.1
      jest-util: 27.5.1

  '@jest/globals@27.5.1':
    dependencies:
      '@jest/environment': 27.5.1
      '@jest/types': 27.5.1
      expect: 27.5.1

  '@jest/reporters@27.5.1':
    dependencies:
      '@bcoe/v8-coverage': 0.2.3
      '@jest/console': 27.5.1
      '@jest/test-result': 27.5.1
      '@jest/transform': 27.5.1
      '@jest/types': 27.5.1
      '@types/node': 16.18.126
      chalk: 4.1.2
      collect-v8-coverage: 1.0.2
      exit: 0.1.2
      glob: 7.2.3
      graceful-fs: 4.2.11
      istanbul-lib-coverage: 3.2.2
      istanbul-lib-instrument: 5.2.1
      istanbul-lib-report: 3.0.1
      istanbul-lib-source-maps: 4.0.1
      istanbul-reports: 3.1.7
      jest-haste-map: 27.5.1
      jest-resolve: 27.5.1
      jest-util: 27.5.1
      jest-worker: 27.5.1
      slash: 3.0.0
      source-map: 0.6.1
      string-length: 4.0.2
      terminal-link: 2.1.1
      v8-to-istanbul: 8.1.1
    transitivePeerDependencies:
      - supports-color

  '@jest/schemas@28.1.3':
    dependencies:
      '@sinclair/typebox': 0.24.51

  '@jest/source-map@27.5.1':
    dependencies:
      callsites: 3.1.0
      graceful-fs: 4.2.11
      source-map: 0.6.1

  '@jest/test-result@27.5.1':
    dependencies:
      '@jest/console': 27.5.1
      '@jest/types': 27.5.1
      '@types/istanbul-lib-coverage': 2.0.6
      collect-v8-coverage: 1.0.2

  '@jest/test-result@28.1.3':
    dependencies:
      '@jest/console': 28.1.3
      '@jest/types': 28.1.3
      '@types/istanbul-lib-coverage': 2.0.6
      collect-v8-coverage: 1.0.2

  '@jest/test-sequencer@27.5.1':
    dependencies:
      '@jest/test-result': 27.5.1
      graceful-fs: 4.2.11
      jest-haste-map: 27.5.1
      jest-runtime: 27.5.1
    transitivePeerDependencies:
      - supports-color

  '@jest/transform@27.5.1':
    dependencies:
      '@babel/core': 7.28.0
      '@jest/types': 27.5.1
      babel-plugin-istanbul: 6.1.1
      chalk: 4.1.2
      convert-source-map: 1.9.0
      fast-json-stable-stringify: 2.1.0
      graceful-fs: 4.2.11
      jest-haste-map: 27.5.1
      jest-regex-util: 27.5.1
      jest-util: 27.5.1
      micromatch: 4.0.8
      pirates: 4.0.7
      slash: 3.0.0
      source-map: 0.6.1
      write-file-atomic: 3.0.3
    transitivePeerDependencies:
      - supports-color

  '@jest/types@27.5.1':
    dependencies:
      '@types/istanbul-lib-coverage': 2.0.6
      '@types/istanbul-reports': 3.0.4
      '@types/node': 16.18.126
      '@types/yargs': 16.0.9
      chalk: 4.1.2

  '@jest/types@28.1.3':
    dependencies:
      '@jest/schemas': 28.1.3
      '@types/istanbul-lib-coverage': 2.0.6
      '@types/istanbul-reports': 3.0.4
      '@types/node': 16.18.126
      '@types/yargs': 17.0.33
      chalk: 4.1.2

  '@jridgewell/gen-mapping@0.3.12':
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.4
      '@jridgewell/trace-mapping': 0.3.29

  '@jridgewell/resolve-uri@3.1.2': {}

  '@jridgewell/source-map@0.3.6':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.12
      '@jridgewell/trace-mapping': 0.3.29

  '@jridgewell/sourcemap-codec@1.5.4': {}

  '@jridgewell/trace-mapping@0.3.29':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.4

  '@jridgewell/trace-mapping@0.3.9':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.4

  '@leichtgewicht/ip-codec@2.0.5': {}

  '@nicolo-ribaudo/eslint-scope-5-internals@5.1.1-v1':
    dependencies:
      eslint-scope: 5.1.1

  '@nodelib/fs.scandir@2.1.5':
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0

  '@nodelib/fs.stat@2.0.5': {}

  '@nodelib/fs.walk@1.2.8':
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.19.1

  '@pkgjs/parseargs@0.11.0':
    optional: true

  '@pmmmwh/react-refresh-webpack-plugin@0.5.17(react-refresh@0.11.0)(type-fest@0.21.3)(webpack-dev-server@4.15.2(webpack@5.100.0))(webpack@5.100.0)':
    dependencies:
      ansi-html: 0.0.9
      core-js-pure: 3.43.0
      error-stack-parser: 2.1.4
      html-entities: 2.6.0
      loader-utils: 2.0.4
      react-refresh: 0.11.0
      schema-utils: 4.3.2
      source-map: 0.7.4
      webpack: 5.100.0
    optionalDependencies:
      type-fest: 0.21.3
      webpack-dev-server: 4.15.2(webpack@5.100.0)

  '@remix-run/router@1.23.0': {}

  '@rollup/plugin-babel@5.3.1(@babel/core@7.28.0)(@types/babel__core@7.20.5)(rollup@2.79.2)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-module-imports': 7.27.1
      '@rollup/pluginutils': 3.1.0(rollup@2.79.2)
      rollup: 2.79.2
    optionalDependencies:
      '@types/babel__core': 7.20.5
    transitivePeerDependencies:
      - supports-color

  '@rollup/plugin-node-resolve@11.2.1(rollup@2.79.2)':
    dependencies:
      '@rollup/pluginutils': 3.1.0(rollup@2.79.2)
      '@types/resolve': 1.17.1
      builtin-modules: 3.3.0
      deepmerge: 4.3.1
      is-module: 1.0.0
      resolve: 1.22.10
      rollup: 2.79.2

  '@rollup/plugin-replace@2.4.2(rollup@2.79.2)':
    dependencies:
      '@rollup/pluginutils': 3.1.0(rollup@2.79.2)
      magic-string: 0.25.9
      rollup: 2.79.2

  '@rollup/pluginutils@3.1.0(rollup@2.79.2)':
    dependencies:
      '@types/estree': 0.0.39
      estree-walker: 1.0.1
      picomatch: 2.3.1
      rollup: 2.79.2

  '@rtsao/scc@1.1.0': {}

  '@rushstack/eslint-patch@1.12.0': {}

  '@sinclair/typebox@0.24.51': {}

  '@sinonjs/commons@1.8.6':
    dependencies:
      type-detect: 4.0.8

  '@sinonjs/fake-timers@8.1.0':
    dependencies:
      '@sinonjs/commons': 1.8.6

  '@surma/rollup-plugin-off-main-thread@2.2.3':
    dependencies:
      ejs: 3.1.10
      json5: 2.2.3
      magic-string: 0.25.9
      string.prototype.matchall: 4.0.12

  '@svgr/babel-plugin-add-jsx-attribute@5.4.0': {}

  '@svgr/babel-plugin-remove-jsx-attribute@5.4.0': {}

  '@svgr/babel-plugin-remove-jsx-empty-expression@5.0.1': {}

  '@svgr/babel-plugin-replace-jsx-attribute-value@5.0.1': {}

  '@svgr/babel-plugin-svg-dynamic-title@5.4.0': {}

  '@svgr/babel-plugin-svg-em-dimensions@5.4.0': {}

  '@svgr/babel-plugin-transform-react-native-svg@5.4.0': {}

  '@svgr/babel-plugin-transform-svg-component@5.5.0': {}

  '@svgr/babel-preset@5.5.0':
    dependencies:
      '@svgr/babel-plugin-add-jsx-attribute': 5.4.0
      '@svgr/babel-plugin-remove-jsx-attribute': 5.4.0
      '@svgr/babel-plugin-remove-jsx-empty-expression': 5.0.1
      '@svgr/babel-plugin-replace-jsx-attribute-value': 5.0.1
      '@svgr/babel-plugin-svg-dynamic-title': 5.4.0
      '@svgr/babel-plugin-svg-em-dimensions': 5.4.0
      '@svgr/babel-plugin-transform-react-native-svg': 5.4.0
      '@svgr/babel-plugin-transform-svg-component': 5.5.0

  '@svgr/core@5.5.0':
    dependencies:
      '@svgr/plugin-jsx': 5.5.0
      camelcase: 6.3.0
      cosmiconfig: 7.1.0
    transitivePeerDependencies:
      - supports-color

  '@svgr/hast-util-to-babel-ast@5.5.0':
    dependencies:
      '@babel/types': 7.28.0

  '@svgr/plugin-jsx@5.5.0':
    dependencies:
      '@babel/core': 7.28.0
      '@svgr/babel-preset': 5.5.0
      '@svgr/hast-util-to-babel-ast': 5.5.0
      svg-parser: 2.0.4
    transitivePeerDependencies:
      - supports-color

  '@svgr/plugin-svgo@5.5.0':
    dependencies:
      cosmiconfig: 7.1.0
      deepmerge: 4.3.1
      svgo: 1.3.2

  '@svgr/webpack@5.5.0':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/plugin-transform-react-constant-elements': 7.27.1(@babel/core@7.28.0)
      '@babel/preset-env': 7.28.0(@babel/core@7.28.0)
      '@babel/preset-react': 7.27.1(@babel/core@7.28.0)
      '@svgr/core': 5.5.0
      '@svgr/plugin-jsx': 5.5.0
      '@svgr/plugin-svgo': 5.5.0
      loader-utils: 2.0.4
    transitivePeerDependencies:
      - supports-color

  '@testing-library/dom@8.20.1':
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/runtime': 7.27.6
      '@types/aria-query': 5.0.4
      aria-query: 5.1.3
      chalk: 4.1.2
      dom-accessibility-api: 0.5.16
      lz-string: 1.5.0
      pretty-format: 27.5.1

  '@testing-library/jest-dom@5.17.0':
    dependencies:
      '@adobe/css-tools': 4.4.3
      '@babel/runtime': 7.27.6
      '@types/testing-library__jest-dom': 5.14.9
      aria-query: 5.3.2
      chalk: 3.0.0
      css.escape: 1.5.1
      dom-accessibility-api: 0.5.16
      lodash: 4.17.21
      redent: 3.0.0

  '@testing-library/react@13.4.0(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.27.6
      '@testing-library/dom': 8.20.1
      '@types/react-dom': 18.3.7(@types/react@18.3.23)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - '@types/react'

  '@testing-library/user-event@13.5.0(@testing-library/dom@8.20.1)':
    dependencies:
      '@babel/runtime': 7.27.6
      '@testing-library/dom': 8.20.1

  '@tootallnate/once@1.1.2': {}

  '@trysound/sax@0.2.0': {}

  '@tsconfig/node10@1.0.11': {}

  '@tsconfig/node12@1.0.11': {}

  '@tsconfig/node14@1.0.3': {}

  '@tsconfig/node16@1.0.4': {}

  '@types/aria-query@5.0.4': {}

  '@types/babel__core@7.20.5':
    dependencies:
      '@babel/parser': 7.28.0
      '@babel/types': 7.28.0
      '@types/babel__generator': 7.27.0
      '@types/babel__template': 7.4.4
      '@types/babel__traverse': 7.20.7

  '@types/babel__generator@7.27.0':
    dependencies:
      '@babel/types': 7.28.0

  '@types/babel__template@7.4.4':
    dependencies:
      '@babel/parser': 7.28.0
      '@babel/types': 7.28.0

  '@types/babel__traverse@7.20.7':
    dependencies:
      '@babel/types': 7.28.0

  '@types/body-parser@1.19.6':
    dependencies:
      '@types/connect': 3.4.38
      '@types/node': 16.18.126

  '@types/bonjour@3.5.13':
    dependencies:
      '@types/node': 16.18.126

  '@types/connect-history-api-fallback@1.5.4':
    dependencies:
      '@types/express-serve-static-core': 5.0.6
      '@types/node': 16.18.126

  '@types/connect@3.4.38':
    dependencies:
      '@types/node': 16.18.126

  '@types/eslint-scope@3.7.7':
    dependencies:
      '@types/eslint': 8.56.12
      '@types/estree': 1.0.8

  '@types/eslint@8.56.12':
    dependencies:
      '@types/estree': 1.0.8
      '@types/json-schema': 7.0.15

  '@types/estree@0.0.39': {}

  '@types/estree@1.0.8': {}

  '@types/express-serve-static-core@4.19.6':
    dependencies:
      '@types/node': 16.18.126
      '@types/qs': 6.14.0
      '@types/range-parser': 1.2.7
      '@types/send': 0.17.5

  '@types/express-serve-static-core@5.0.6':
    dependencies:
      '@types/node': 16.18.126
      '@types/qs': 6.14.0
      '@types/range-parser': 1.2.7
      '@types/send': 0.17.5

  '@types/express@4.17.23':
    dependencies:
      '@types/body-parser': 1.19.6
      '@types/express-serve-static-core': 4.19.6
      '@types/qs': 6.14.0
      '@types/serve-static': 1.15.8

  '@types/graceful-fs@4.1.9':
    dependencies:
      '@types/node': 16.18.126

  '@types/html-minifier-terser@6.1.0': {}

  '@types/http-errors@2.0.5': {}

  '@types/http-proxy@1.17.16':
    dependencies:
      '@types/node': 16.18.126

  '@types/istanbul-lib-coverage@2.0.6': {}

  '@types/istanbul-lib-report@3.0.3':
    dependencies:
      '@types/istanbul-lib-coverage': 2.0.6

  '@types/istanbul-reports@3.0.4':
    dependencies:
      '@types/istanbul-lib-report': 3.0.3

  '@types/jest@27.5.2':
    dependencies:
      jest-matcher-utils: 27.5.1
      pretty-format: 27.5.1

  '@types/json-schema@7.0.15': {}

  '@types/json5@0.0.29': {}

  '@types/mime@1.3.5': {}

  '@types/node-forge@1.3.11':
    dependencies:
      '@types/node': 16.18.126

  '@types/node@16.18.126': {}

  '@types/parse-json@4.0.2': {}

  '@types/prettier@2.7.3': {}

  '@types/prop-types@15.7.15': {}

  '@types/q@1.5.8': {}

  '@types/qs@6.14.0': {}

  '@types/range-parser@1.2.7': {}

  '@types/react-dom@18.3.7(@types/react@18.3.23)':
    dependencies:
      '@types/react': 18.3.23

  '@types/react@18.3.23':
    dependencies:
      '@types/prop-types': 15.7.15
      csstype: 3.1.3

  '@types/resolve@1.17.1':
    dependencies:
      '@types/node': 16.18.126

  '@types/retry@0.12.0': {}

  '@types/semver@7.7.0': {}

  '@types/send@0.17.5':
    dependencies:
      '@types/mime': 1.3.5
      '@types/node': 16.18.126

  '@types/serve-index@1.9.4':
    dependencies:
      '@types/express': 4.17.23

  '@types/serve-static@1.15.8':
    dependencies:
      '@types/http-errors': 2.0.5
      '@types/node': 16.18.126
      '@types/send': 0.17.5

  '@types/sockjs@0.3.36':
    dependencies:
      '@types/node': 16.18.126

  '@types/stack-utils@2.0.3': {}

  '@types/testing-library__jest-dom@5.14.9':
    dependencies:
      '@types/jest': 27.5.2

  '@types/trusted-types@2.0.7': {}

  '@types/ws@8.18.1':
    dependencies:
      '@types/node': 16.18.126

  '@types/yargs-parser@21.0.3': {}

  '@types/yargs@16.0.9':
    dependencies:
      '@types/yargs-parser': 21.0.3

  '@types/yargs@17.0.33':
    dependencies:
      '@types/yargs-parser': 21.0.3

  '@typescript-eslint/eslint-plugin@5.62.0(@typescript-eslint/parser@5.62.0(eslint@8.57.1)(typescript@4.9.5))(eslint@8.57.1)(typescript@4.9.5)':
    dependencies:
      '@eslint-community/regexpp': 4.12.1
      '@typescript-eslint/parser': 5.62.0(eslint@8.57.1)(typescript@4.9.5)
      '@typescript-eslint/scope-manager': 5.62.0
      '@typescript-eslint/type-utils': 5.62.0(eslint@8.57.1)(typescript@4.9.5)
      '@typescript-eslint/utils': 5.62.0(eslint@8.57.1)(typescript@4.9.5)
      debug: 4.4.1
      eslint: 8.57.1
      graphemer: 1.4.0
      ignore: 5.3.2
      natural-compare-lite: 1.4.0
      semver: 7.7.2
      tsutils: 3.21.0(typescript@4.9.5)
    optionalDependencies:
      typescript: 4.9.5
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/experimental-utils@5.62.0(eslint@8.57.1)(typescript@4.9.5)':
    dependencies:
      '@typescript-eslint/utils': 5.62.0(eslint@8.57.1)(typescript@4.9.5)
      eslint: 8.57.1
    transitivePeerDependencies:
      - supports-color
      - typescript

  '@typescript-eslint/parser@5.62.0(eslint@8.57.1)(typescript@4.9.5)':
    dependencies:
      '@typescript-eslint/scope-manager': 5.62.0
      '@typescript-eslint/types': 5.62.0
      '@typescript-eslint/typescript-estree': 5.62.0(typescript@4.9.5)
      debug: 4.4.1
      eslint: 8.57.1
    optionalDependencies:
      typescript: 4.9.5
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/scope-manager@5.62.0':
    dependencies:
      '@typescript-eslint/types': 5.62.0
      '@typescript-eslint/visitor-keys': 5.62.0

  '@typescript-eslint/type-utils@5.62.0(eslint@8.57.1)(typescript@4.9.5)':
    dependencies:
      '@typescript-eslint/typescript-estree': 5.62.0(typescript@4.9.5)
      '@typescript-eslint/utils': 5.62.0(eslint@8.57.1)(typescript@4.9.5)
      debug: 4.4.1
      eslint: 8.57.1
      tsutils: 3.21.0(typescript@4.9.5)
    optionalDependencies:
      typescript: 4.9.5
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/types@5.62.0': {}

  '@typescript-eslint/typescript-estree@5.62.0(typescript@4.9.5)':
    dependencies:
      '@typescript-eslint/types': 5.62.0
      '@typescript-eslint/visitor-keys': 5.62.0
      debug: 4.4.1
      globby: 11.1.0
      is-glob: 4.0.3
      semver: 7.7.2
      tsutils: 3.21.0(typescript@4.9.5)
    optionalDependencies:
      typescript: 4.9.5
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/utils@5.62.0(eslint@8.57.1)(typescript@4.9.5)':
    dependencies:
      '@eslint-community/eslint-utils': 4.7.0(eslint@8.57.1)
      '@types/json-schema': 7.0.15
      '@types/semver': 7.7.0
      '@typescript-eslint/scope-manager': 5.62.0
      '@typescript-eslint/types': 5.62.0
      '@typescript-eslint/typescript-estree': 5.62.0(typescript@4.9.5)
      eslint: 8.57.1
      eslint-scope: 5.1.1
      semver: 7.7.2
    transitivePeerDependencies:
      - supports-color
      - typescript

  '@typescript-eslint/visitor-keys@5.62.0':
    dependencies:
      '@typescript-eslint/types': 5.62.0
      eslint-visitor-keys: 3.4.3

  '@ungap/structured-clone@1.3.0': {}

  '@webassemblyjs/ast@1.14.1':
    dependencies:
      '@webassemblyjs/helper-numbers': 1.13.2
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2

  '@webassemblyjs/floating-point-hex-parser@1.13.2': {}

  '@webassemblyjs/helper-api-error@1.13.2': {}

  '@webassemblyjs/helper-buffer@1.14.1': {}

  '@webassemblyjs/helper-numbers@1.13.2':
    dependencies:
      '@webassemblyjs/floating-point-hex-parser': 1.13.2
      '@webassemblyjs/helper-api-error': 1.13.2
      '@xtuc/long': 4.2.2

  '@webassemblyjs/helper-wasm-bytecode@1.13.2': {}

  '@webassemblyjs/helper-wasm-section@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-buffer': 1.14.1
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/wasm-gen': 1.14.1

  '@webassemblyjs/ieee754@1.13.2':
    dependencies:
      '@xtuc/ieee754': 1.2.0

  '@webassemblyjs/leb128@1.13.2':
    dependencies:
      '@xtuc/long': 4.2.2

  '@webassemblyjs/utf8@1.13.2': {}

  '@webassemblyjs/wasm-edit@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-buffer': 1.14.1
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/helper-wasm-section': 1.14.1
      '@webassemblyjs/wasm-gen': 1.14.1
      '@webassemblyjs/wasm-opt': 1.14.1
      '@webassemblyjs/wasm-parser': 1.14.1
      '@webassemblyjs/wast-printer': 1.14.1

  '@webassemblyjs/wasm-gen@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/ieee754': 1.13.2
      '@webassemblyjs/leb128': 1.13.2
      '@webassemblyjs/utf8': 1.13.2

  '@webassemblyjs/wasm-opt@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-buffer': 1.14.1
      '@webassemblyjs/wasm-gen': 1.14.1
      '@webassemblyjs/wasm-parser': 1.14.1

  '@webassemblyjs/wasm-parser@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-api-error': 1.13.2
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/ieee754': 1.13.2
      '@webassemblyjs/leb128': 1.13.2
      '@webassemblyjs/utf8': 1.13.2

  '@webassemblyjs/wast-printer@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@xtuc/long': 4.2.2

  '@xtuc/ieee754@1.2.0': {}

  '@xtuc/long@4.2.2': {}

  abab@2.0.6: {}

  accepts@1.3.8:
    dependencies:
      mime-types: 2.1.35
      negotiator: 0.6.3

  acorn-globals@6.0.0:
    dependencies:
      acorn: 7.4.1
      acorn-walk: 7.2.0

  acorn-import-phases@1.0.3(acorn@8.15.0):
    dependencies:
      acorn: 8.15.0

  acorn-jsx@5.3.2(acorn@8.15.0):
    dependencies:
      acorn: 8.15.0

  acorn-walk@7.2.0: {}

  acorn-walk@8.3.4:
    dependencies:
      acorn: 8.15.0

  acorn@7.4.1: {}

  acorn@8.15.0: {}

  address@1.2.2: {}

  adjust-sourcemap-loader@4.0.0:
    dependencies:
      loader-utils: 2.0.4
      regex-parser: 2.3.1

  agent-base@6.0.2:
    dependencies:
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  ajv-formats@2.1.1(ajv@8.17.1):
    optionalDependencies:
      ajv: 8.17.1

  ajv-keywords@3.5.2(ajv@6.12.6):
    dependencies:
      ajv: 6.12.6

  ajv-keywords@5.1.0(ajv@8.17.1):
    dependencies:
      ajv: 8.17.1
      fast-deep-equal: 3.1.3

  ajv@6.12.6:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  ajv@8.17.1:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-uri: 3.0.6
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2

  ansi-escapes@4.3.2:
    dependencies:
      type-fest: 0.21.3

  ansi-html-community@0.0.8: {}

  ansi-html@0.0.9: {}

  ansi-regex@5.0.1: {}

  ansi-regex@6.1.0: {}

  ansi-styles@3.2.1:
    dependencies:
      color-convert: 1.9.3

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  ansi-styles@5.2.0: {}

  ansi-styles@6.2.1: {}

  any-promise@1.3.0: {}

  anymatch@3.1.3:
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1

  arg@4.1.3: {}

  arg@5.0.2: {}

  argparse@1.0.10:
    dependencies:
      sprintf-js: 1.0.3

  argparse@2.0.1: {}

  aria-query@5.1.3:
    dependencies:
      deep-equal: 2.2.3

  aria-query@5.3.2: {}

  array-buffer-byte-length@1.0.2:
    dependencies:
      call-bound: 1.0.4
      is-array-buffer: 3.0.5

  array-flatten@1.1.1: {}

  array-includes@3.1.9:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      is-string: 1.1.1
      math-intrinsics: 1.1.0

  array-union@2.1.0: {}

  array.prototype.findlast@1.2.5:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      es-shim-unscopables: 1.1.0

  array.prototype.findlastindex@1.2.6:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      es-shim-unscopables: 1.1.0

  array.prototype.flat@1.3.3:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-shim-unscopables: 1.1.0

  array.prototype.flatmap@1.3.3:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-shim-unscopables: 1.1.0

  array.prototype.reduce@1.0.8:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-array-method-boxes-properly: 1.0.0
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      is-string: 1.1.1

  array.prototype.tosorted@1.1.4:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      es-shim-unscopables: 1.1.0

  arraybuffer.prototype.slice@1.0.4:
    dependencies:
      array-buffer-byte-length: 1.0.2
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      is-array-buffer: 3.0.5

  asap@2.0.6: {}

  ast-types-flow@0.0.8: {}

  async-function@1.0.0: {}

  async@3.2.6: {}

  asynckit@0.4.0: {}

  at-least-node@1.0.0: {}

  autoprefixer@10.4.21(postcss@8.5.6):
    dependencies:
      browserslist: 4.25.1
      caniuse-lite: 1.0.30001727
      fraction.js: 4.3.7
      normalize-range: 0.1.2
      picocolors: 1.1.1
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  available-typed-arrays@1.0.7:
    dependencies:
      possible-typed-array-names: 1.1.0

  axe-core@4.10.3: {}

  axobject-query@4.1.0: {}

  babel-jest@27.5.1(@babel/core@7.28.0):
    dependencies:
      '@babel/core': 7.28.0
      '@jest/transform': 27.5.1
      '@jest/types': 27.5.1
      '@types/babel__core': 7.20.5
      babel-plugin-istanbul: 6.1.1
      babel-preset-jest: 27.5.1(@babel/core@7.28.0)
      chalk: 4.1.2
      graceful-fs: 4.2.11
      slash: 3.0.0
    transitivePeerDependencies:
      - supports-color

  babel-loader@8.4.1(@babel/core@7.28.0)(webpack@5.100.0):
    dependencies:
      '@babel/core': 7.28.0
      find-cache-dir: 3.3.2
      loader-utils: 2.0.4
      make-dir: 3.1.0
      schema-utils: 2.7.1
      webpack: 5.100.0

  babel-plugin-istanbul@6.1.1:
    dependencies:
      '@babel/helper-plugin-utils': 7.27.1
      '@istanbuljs/load-nyc-config': 1.1.0
      '@istanbuljs/schema': 0.1.3
      istanbul-lib-instrument: 5.2.1
      test-exclude: 6.0.0
    transitivePeerDependencies:
      - supports-color

  babel-plugin-jest-hoist@27.5.1:
    dependencies:
      '@babel/template': 7.27.2
      '@babel/types': 7.28.0
      '@types/babel__core': 7.20.5
      '@types/babel__traverse': 7.20.7

  babel-plugin-macros@3.1.0:
    dependencies:
      '@babel/runtime': 7.27.6
      cosmiconfig: 7.1.0
      resolve: 1.22.10

  babel-plugin-named-asset-import@0.3.8(@babel/core@7.28.0):
    dependencies:
      '@babel/core': 7.28.0

  babel-plugin-polyfill-corejs2@0.4.14(@babel/core@7.28.0):
    dependencies:
      '@babel/compat-data': 7.28.0
      '@babel/core': 7.28.0
      '@babel/helper-define-polyfill-provider': 0.6.5(@babel/core@7.28.0)
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  babel-plugin-polyfill-corejs3@0.13.0(@babel/core@7.28.0):
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-define-polyfill-provider': 0.6.5(@babel/core@7.28.0)
      core-js-compat: 3.43.0
    transitivePeerDependencies:
      - supports-color

  babel-plugin-polyfill-regenerator@0.6.5(@babel/core@7.28.0):
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-define-polyfill-provider': 0.6.5(@babel/core@7.28.0)
    transitivePeerDependencies:
      - supports-color

  babel-plugin-transform-react-remove-prop-types@0.4.24: {}

  babel-preset-current-node-syntax@1.1.0(@babel/core@7.28.0):
    dependencies:
      '@babel/core': 7.28.0
      '@babel/plugin-syntax-async-generators': 7.8.4(@babel/core@7.28.0)
      '@babel/plugin-syntax-bigint': 7.8.3(@babel/core@7.28.0)
      '@babel/plugin-syntax-class-properties': 7.12.13(@babel/core@7.28.0)
      '@babel/plugin-syntax-class-static-block': 7.14.5(@babel/core@7.28.0)
      '@babel/plugin-syntax-import-attributes': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-syntax-import-meta': 7.10.4(@babel/core@7.28.0)
      '@babel/plugin-syntax-json-strings': 7.8.3(@babel/core@7.28.0)
      '@babel/plugin-syntax-logical-assignment-operators': 7.10.4(@babel/core@7.28.0)
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3(@babel/core@7.28.0)
      '@babel/plugin-syntax-numeric-separator': 7.10.4(@babel/core@7.28.0)
      '@babel/plugin-syntax-object-rest-spread': 7.8.3(@babel/core@7.28.0)
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3(@babel/core@7.28.0)
      '@babel/plugin-syntax-optional-chaining': 7.8.3(@babel/core@7.28.0)
      '@babel/plugin-syntax-private-property-in-object': 7.14.5(@babel/core@7.28.0)
      '@babel/plugin-syntax-top-level-await': 7.14.5(@babel/core@7.28.0)

  babel-preset-jest@27.5.1(@babel/core@7.28.0):
    dependencies:
      '@babel/core': 7.28.0
      babel-plugin-jest-hoist: 27.5.1
      babel-preset-current-node-syntax: 1.1.0(@babel/core@7.28.0)

  babel-preset-react-app@10.1.0:
    dependencies:
      '@babel/core': 7.28.0
      '@babel/plugin-proposal-class-properties': 7.18.6(@babel/core@7.28.0)
      '@babel/plugin-proposal-decorators': 7.28.0(@babel/core@7.28.0)
      '@babel/plugin-proposal-nullish-coalescing-operator': 7.18.6(@babel/core@7.28.0)
      '@babel/plugin-proposal-numeric-separator': 7.18.6(@babel/core@7.28.0)
      '@babel/plugin-proposal-optional-chaining': 7.21.0(@babel/core@7.28.0)
      '@babel/plugin-proposal-private-methods': 7.18.6(@babel/core@7.28.0)
      '@babel/plugin-proposal-private-property-in-object': 7.21.11(@babel/core@7.28.0)
      '@babel/plugin-transform-flow-strip-types': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-react-display-name': 7.28.0(@babel/core@7.28.0)
      '@babel/plugin-transform-runtime': 7.28.0(@babel/core@7.28.0)
      '@babel/preset-env': 7.28.0(@babel/core@7.28.0)
      '@babel/preset-react': 7.27.1(@babel/core@7.28.0)
      '@babel/preset-typescript': 7.27.1(@babel/core@7.28.0)
      '@babel/runtime': 7.27.6
      babel-plugin-macros: 3.1.0
      babel-plugin-transform-react-remove-prop-types: 0.4.24
    transitivePeerDependencies:
      - supports-color

  balanced-match@1.0.2: {}

  batch@0.6.1: {}

  bfj@7.1.0:
    dependencies:
      bluebird: 3.7.2
      check-types: 11.2.3
      hoopy: 0.1.4
      jsonpath: 1.1.1
      tryer: 1.0.1

  big.js@5.2.2: {}

  binary-extensions@2.3.0: {}

  bluebird@3.7.2: {}

  body-parser@1.20.3:
    dependencies:
      bytes: 3.1.2
      content-type: 1.0.5
      debug: 2.6.9
      depd: 2.0.0
      destroy: 1.2.0
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      on-finished: 2.4.1
      qs: 6.13.0
      raw-body: 2.5.2
      type-is: 1.6.18
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color

  bonjour-service@1.3.0:
    dependencies:
      fast-deep-equal: 3.1.3
      multicast-dns: 7.2.5

  boolbase@1.0.0: {}

  brace-expansion@1.1.12:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  brace-expansion@2.0.2:
    dependencies:
      balanced-match: 1.0.2

  braces@3.0.3:
    dependencies:
      fill-range: 7.1.1

  browser-process-hrtime@1.0.0: {}

  browserslist@4.25.1:
    dependencies:
      caniuse-lite: 1.0.30001727
      electron-to-chromium: 1.5.180
      node-releases: 2.0.19
      update-browserslist-db: 1.1.3(browserslist@4.25.1)

  bser@2.1.1:
    dependencies:
      node-int64: 0.4.0

  buffer-from@1.1.2: {}

  builtin-modules@3.3.0: {}

  bytes@3.1.2: {}

  call-bind-apply-helpers@1.0.2:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2

  call-bind@1.0.8:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      get-intrinsic: 1.3.0
      set-function-length: 1.2.2

  call-bound@1.0.4:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      get-intrinsic: 1.3.0

  callsites@3.1.0: {}

  camel-case@4.1.2:
    dependencies:
      pascal-case: 3.1.2
      tslib: 2.8.1

  camelcase-css@2.0.1: {}

  camelcase@5.3.1: {}

  camelcase@6.3.0: {}

  caniuse-api@3.0.0:
    dependencies:
      browserslist: 4.25.1
      caniuse-lite: 1.0.30001727
      lodash.memoize: 4.1.2
      lodash.uniq: 4.5.0

  caniuse-lite@1.0.30001727: {}

  case-sensitive-paths-webpack-plugin@2.4.0: {}

  chalk@2.4.2:
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0

  chalk@3.0.0:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  chalk@4.1.2:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  char-regex@1.0.2: {}

  char-regex@2.0.2: {}

  check-types@11.2.3: {}

  chokidar@3.6.0:
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.3
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3

  chrome-trace-event@1.0.4: {}

  ci-info@3.9.0: {}

  cjs-module-lexer@1.4.3: {}

  classnames@2.5.1: {}

  clean-css@5.3.3:
    dependencies:
      source-map: 0.6.1

  cliui@7.0.4:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0

  clone-deep@4.0.1:
    dependencies:
      is-plain-object: 2.0.4
      kind-of: 6.0.3
      shallow-clone: 3.0.1

  co@4.6.0: {}

  coa@2.0.2:
    dependencies:
      '@types/q': 1.5.8
      chalk: 2.4.2
      q: 1.5.1

  collect-v8-coverage@1.0.2: {}

  color-convert@1.9.3:
    dependencies:
      color-name: 1.1.3

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.3: {}

  color-name@1.1.4: {}

  colord@2.9.3: {}

  colorette@2.0.20: {}

  combined-stream@1.0.8:
    dependencies:
      delayed-stream: 1.0.0

  commander@2.20.3: {}

  commander@4.1.1: {}

  commander@7.2.0: {}

  commander@8.3.0: {}

  common-tags@1.8.2: {}

  commondir@1.0.1: {}

  compressible@2.0.18:
    dependencies:
      mime-db: 1.54.0

  compression@1.8.0:
    dependencies:
      bytes: 3.1.2
      compressible: 2.0.18
      debug: 2.6.9
      negotiator: 0.6.4
      on-headers: 1.0.2
      safe-buffer: 5.2.1
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color

  concat-map@0.0.1: {}

  confusing-browser-globals@1.0.11: {}

  connect-history-api-fallback@2.0.0: {}

  content-disposition@0.5.4:
    dependencies:
      safe-buffer: 5.2.1

  content-type@1.0.5: {}

  convert-source-map@1.9.0: {}

  convert-source-map@2.0.0: {}

  cookie-signature@1.0.6: {}

  cookie@0.7.1: {}

  copy-anything@2.0.6:
    dependencies:
      is-what: 3.14.1

  core-js-compat@3.43.0:
    dependencies:
      browserslist: 4.25.1

  core-js-pure@3.43.0: {}

  core-js@3.44.0: {}

  core-util-is@1.0.3: {}

  cosmiconfig-typescript-loader@1.0.9(@types/node@16.18.126)(cosmiconfig@7.1.0)(typescript@4.9.5):
    dependencies:
      '@types/node': 16.18.126
      cosmiconfig: 7.1.0
      ts-node: 10.9.2(@types/node@16.18.126)(typescript@4.9.5)
      typescript: 4.9.5
    transitivePeerDependencies:
      - '@swc/core'
      - '@swc/wasm'

  cosmiconfig@6.0.0:
    dependencies:
      '@types/parse-json': 4.0.2
      import-fresh: 3.3.1
      parse-json: 5.2.0
      path-type: 4.0.0
      yaml: 1.10.2

  cosmiconfig@7.1.0:
    dependencies:
      '@types/parse-json': 4.0.2
      import-fresh: 3.3.1
      parse-json: 5.2.0
      path-type: 4.0.0
      yaml: 1.10.2

  craco-less@2.0.0(@craco/craco@7.1.0(@types/node@16.18.126)(postcss@8.5.6)(react-scripts@5.0.1(@babel/plugin-syntax-flow@7.27.1(@babel/core@7.28.0))(@babel/plugin-transform-react-jsx@7.27.1(@babel/core@7.28.0))(@types/babel__core@7.20.5)(eslint@8.57.1)(react@18.3.1)(ts-node@10.9.2(@types/node@16.18.126)(typescript@4.9.5))(type-fest@0.21.3)(typescript@4.9.5))(typescript@4.9.5))(react-scripts@5.0.1(@babel/plugin-syntax-flow@7.27.1(@babel/core@7.28.0))(@babel/plugin-transform-react-jsx@7.27.1(@babel/core@7.28.0))(@types/babel__core@7.20.5)(eslint@8.57.1)(react@18.3.1)(ts-node@10.9.2(@types/node@16.18.126)(typescript@4.9.5))(type-fest@0.21.3)(typescript@4.9.5))(webpack@5.100.0):
    dependencies:
      '@craco/craco': 7.1.0(@types/node@16.18.126)(postcss@8.5.6)(react-scripts@5.0.1(@babel/plugin-syntax-flow@7.27.1(@babel/core@7.28.0))(@babel/plugin-transform-react-jsx@7.27.1(@babel/core@7.28.0))(@types/babel__core@7.20.5)(eslint@8.57.1)(react@18.3.1)(ts-node@10.9.2(@types/node@16.18.126)(typescript@4.9.5))(type-fest@0.21.3)(typescript@4.9.5))(typescript@4.9.5)
      less: 4.3.0
      less-loader: 7.3.0(less@4.3.0)(webpack@5.100.0)
      react-scripts: 5.0.1(@babel/plugin-syntax-flow@7.27.1(@babel/core@7.28.0))(@babel/plugin-transform-react-jsx@7.27.1(@babel/core@7.28.0))(@types/babel__core@7.20.5)(eslint@8.57.1)(react@18.3.1)(ts-node@10.9.2(@types/node@16.18.126)(typescript@4.9.5))(type-fest@0.21.3)(typescript@4.9.5)
    transitivePeerDependencies:
      - webpack

  create-require@1.1.1: {}

  cross-spawn@7.0.6:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  crypto-random-string@2.0.0: {}

  css-blank-pseudo@3.0.3(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-selector-parser: 6.1.2

  css-declaration-sorter@6.4.1(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6

  css-has-pseudo@3.0.4(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-selector-parser: 6.1.2

  css-loader@6.11.0(webpack@5.100.0):
    dependencies:
      icss-utils: 5.1.0(postcss@8.5.6)
      postcss: 8.5.6
      postcss-modules-extract-imports: 3.1.0(postcss@8.5.6)
      postcss-modules-local-by-default: 4.2.0(postcss@8.5.6)
      postcss-modules-scope: 3.2.1(postcss@8.5.6)
      postcss-modules-values: 4.0.0(postcss@8.5.6)
      postcss-value-parser: 4.2.0
      semver: 7.7.2
    optionalDependencies:
      webpack: 5.100.0

  css-minimizer-webpack-plugin@3.4.1(webpack@5.100.0):
    dependencies:
      cssnano: 5.1.15(postcss@8.5.6)
      jest-worker: 27.5.1
      postcss: 8.5.6
      schema-utils: 4.3.2
      serialize-javascript: 6.0.2
      source-map: 0.6.1
      webpack: 5.100.0

  css-prefers-color-scheme@6.0.3(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6

  css-select-base-adapter@0.1.1: {}

  css-select@2.1.0:
    dependencies:
      boolbase: 1.0.0
      css-what: 3.4.2
      domutils: 1.7.0
      nth-check: 1.0.2

  css-select@4.3.0:
    dependencies:
      boolbase: 1.0.0
      css-what: 6.2.2
      domhandler: 4.3.1
      domutils: 2.8.0
      nth-check: 2.1.1

  css-tree@1.0.0-alpha.37:
    dependencies:
      mdn-data: 2.0.4
      source-map: 0.6.1

  css-tree@1.1.3:
    dependencies:
      mdn-data: 2.0.14
      source-map: 0.6.1

  css-what@3.4.2: {}

  css-what@6.2.2: {}

  css.escape@1.5.1: {}

  cssdb@7.11.2: {}

  cssesc@3.0.0: {}

  cssnano-preset-default@5.2.14(postcss@8.5.6):
    dependencies:
      css-declaration-sorter: 6.4.1(postcss@8.5.6)
      cssnano-utils: 3.1.0(postcss@8.5.6)
      postcss: 8.5.6
      postcss-calc: 8.2.4(postcss@8.5.6)
      postcss-colormin: 5.3.1(postcss@8.5.6)
      postcss-convert-values: 5.1.3(postcss@8.5.6)
      postcss-discard-comments: 5.1.2(postcss@8.5.6)
      postcss-discard-duplicates: 5.1.0(postcss@8.5.6)
      postcss-discard-empty: 5.1.1(postcss@8.5.6)
      postcss-discard-overridden: 5.1.0(postcss@8.5.6)
      postcss-merge-longhand: 5.1.7(postcss@8.5.6)
      postcss-merge-rules: 5.1.4(postcss@8.5.6)
      postcss-minify-font-values: 5.1.0(postcss@8.5.6)
      postcss-minify-gradients: 5.1.1(postcss@8.5.6)
      postcss-minify-params: 5.1.4(postcss@8.5.6)
      postcss-minify-selectors: 5.2.1(postcss@8.5.6)
      postcss-normalize-charset: 5.1.0(postcss@8.5.6)
      postcss-normalize-display-values: 5.1.0(postcss@8.5.6)
      postcss-normalize-positions: 5.1.1(postcss@8.5.6)
      postcss-normalize-repeat-style: 5.1.1(postcss@8.5.6)
      postcss-normalize-string: 5.1.0(postcss@8.5.6)
      postcss-normalize-timing-functions: 5.1.0(postcss@8.5.6)
      postcss-normalize-unicode: 5.1.1(postcss@8.5.6)
      postcss-normalize-url: 5.1.0(postcss@8.5.6)
      postcss-normalize-whitespace: 5.1.1(postcss@8.5.6)
      postcss-ordered-values: 5.1.3(postcss@8.5.6)
      postcss-reduce-initial: 5.1.2(postcss@8.5.6)
      postcss-reduce-transforms: 5.1.0(postcss@8.5.6)
      postcss-svgo: 5.1.0(postcss@8.5.6)
      postcss-unique-selectors: 5.1.1(postcss@8.5.6)

  cssnano-utils@3.1.0(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6

  cssnano@5.1.15(postcss@8.5.6):
    dependencies:
      cssnano-preset-default: 5.2.14(postcss@8.5.6)
      lilconfig: 2.1.0
      postcss: 8.5.6
      yaml: 1.10.2

  csso@4.2.0:
    dependencies:
      css-tree: 1.1.3

  cssom@0.3.8: {}

  cssom@0.4.4: {}

  cssstyle@2.3.0:
    dependencies:
      cssom: 0.3.8

  csstype@3.1.3: {}

  damerau-levenshtein@1.0.8: {}

  data-urls@2.0.0:
    dependencies:
      abab: 2.0.6
      whatwg-mimetype: 2.3.0
      whatwg-url: 8.7.0

  data-view-buffer@1.0.2:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-data-view: 1.0.2

  data-view-byte-length@1.0.2:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-data-view: 1.0.2

  data-view-byte-offset@1.0.1:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-data-view: 1.0.2

  debug@2.6.9:
    dependencies:
      ms: 2.0.0

  debug@3.2.7:
    dependencies:
      ms: 2.1.3

  debug@4.4.1:
    dependencies:
      ms: 2.1.3

  decimal.js@10.5.0: {}

  dedent@0.7.0: {}

  deep-equal@2.2.3:
    dependencies:
      array-buffer-byte-length: 1.0.2
      call-bind: 1.0.8
      es-get-iterator: 1.1.3
      get-intrinsic: 1.3.0
      is-arguments: 1.2.0
      is-array-buffer: 3.0.5
      is-date-object: 1.1.0
      is-regex: 1.2.1
      is-shared-array-buffer: 1.0.4
      isarray: 2.0.5
      object-is: 1.1.6
      object-keys: 1.1.1
      object.assign: 4.1.7
      regexp.prototype.flags: 1.5.4
      side-channel: 1.1.0
      which-boxed-primitive: 1.1.1
      which-collection: 1.0.2
      which-typed-array: 1.1.19

  deep-is@0.1.4: {}

  deepmerge@4.3.1: {}

  default-gateway@6.0.3:
    dependencies:
      execa: 5.1.1

  define-data-property@1.1.4:
    dependencies:
      es-define-property: 1.0.1
      es-errors: 1.3.0
      gopd: 1.2.0

  define-lazy-prop@2.0.0: {}

  define-properties@1.2.1:
    dependencies:
      define-data-property: 1.1.4
      has-property-descriptors: 1.0.2
      object-keys: 1.1.1

  delayed-stream@1.0.0: {}

  depd@1.1.2: {}

  depd@2.0.0: {}

  destroy@1.2.0: {}

  detect-newline@3.1.0: {}

  detect-node@2.1.0: {}

  detect-port-alt@1.1.6:
    dependencies:
      address: 1.2.2
      debug: 2.6.9
    transitivePeerDependencies:
      - supports-color

  didyoumean@1.2.2: {}

  diff-sequences@27.5.1: {}

  diff@4.0.2: {}

  dir-glob@3.0.1:
    dependencies:
      path-type: 4.0.0

  dlv@1.1.3: {}

  dns-packet@5.6.1:
    dependencies:
      '@leichtgewicht/ip-codec': 2.0.5

  doctrine@2.1.0:
    dependencies:
      esutils: 2.0.3

  doctrine@3.0.0:
    dependencies:
      esutils: 2.0.3

  dom-accessibility-api@0.5.16: {}

  dom-converter@0.2.0:
    dependencies:
      utila: 0.4.0

  dom-serializer@0.2.2:
    dependencies:
      domelementtype: 2.3.0
      entities: 2.2.0

  dom-serializer@1.4.1:
    dependencies:
      domelementtype: 2.3.0
      domhandler: 4.3.1
      entities: 2.2.0

  domelementtype@1.3.1: {}

  domelementtype@2.3.0: {}

  domexception@2.0.1:
    dependencies:
      webidl-conversions: 5.0.0

  domhandler@4.3.1:
    dependencies:
      domelementtype: 2.3.0

  domutils@1.7.0:
    dependencies:
      dom-serializer: 0.2.2
      domelementtype: 1.3.1

  domutils@2.8.0:
    dependencies:
      dom-serializer: 1.4.1
      domelementtype: 2.3.0
      domhandler: 4.3.1

  dot-case@3.0.4:
    dependencies:
      no-case: 3.0.4
      tslib: 2.8.1

  dotenv-expand@5.1.0: {}

  dotenv@10.0.0: {}

  dunder-proto@1.0.1:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-errors: 1.3.0
      gopd: 1.2.0

  duplexer@0.1.2: {}

  eastasianwidth@0.2.0: {}

  ee-first@1.1.1: {}

  ejs@3.1.10:
    dependencies:
      jake: 10.9.2

  electron-to-chromium@1.5.180: {}

  emittery@0.10.2: {}

  emittery@0.8.1: {}

  emoji-regex@8.0.0: {}

  emoji-regex@9.2.2: {}

  emojis-list@3.0.0: {}

  encodeurl@1.0.2: {}

  encodeurl@2.0.0: {}

  enhanced-resolve@5.18.2:
    dependencies:
      graceful-fs: 4.2.11
      tapable: 2.2.2

  entities@2.2.0: {}

  errno@0.1.8:
    dependencies:
      prr: 1.0.1
    optional: true

  error-ex@1.3.2:
    dependencies:
      is-arrayish: 0.2.1

  error-stack-parser@2.1.4:
    dependencies:
      stackframe: 1.3.4

  es-abstract@1.24.0:
    dependencies:
      array-buffer-byte-length: 1.0.2
      arraybuffer.prototype.slice: 1.0.4
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      call-bound: 1.0.4
      data-view-buffer: 1.0.2
      data-view-byte-length: 1.0.2
      data-view-byte-offset: 1.0.1
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      es-set-tostringtag: 2.1.0
      es-to-primitive: 1.3.0
      function.prototype.name: 1.1.8
      get-intrinsic: 1.3.0
      get-proto: 1.0.1
      get-symbol-description: 1.1.0
      globalthis: 1.0.4
      gopd: 1.2.0
      has-property-descriptors: 1.0.2
      has-proto: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      internal-slot: 1.1.0
      is-array-buffer: 3.0.5
      is-callable: 1.2.7
      is-data-view: 1.0.2
      is-negative-zero: 2.0.3
      is-regex: 1.2.1
      is-set: 2.0.3
      is-shared-array-buffer: 1.0.4
      is-string: 1.1.1
      is-typed-array: 1.1.15
      is-weakref: 1.1.1
      math-intrinsics: 1.1.0
      object-inspect: 1.13.4
      object-keys: 1.1.1
      object.assign: 4.1.7
      own-keys: 1.0.1
      regexp.prototype.flags: 1.5.4
      safe-array-concat: 1.1.3
      safe-push-apply: 1.0.0
      safe-regex-test: 1.1.0
      set-proto: 1.0.0
      stop-iteration-iterator: 1.1.0
      string.prototype.trim: 1.2.10
      string.prototype.trimend: 1.0.9
      string.prototype.trimstart: 1.0.8
      typed-array-buffer: 1.0.3
      typed-array-byte-length: 1.0.3
      typed-array-byte-offset: 1.0.4
      typed-array-length: 1.0.7
      unbox-primitive: 1.1.0
      which-typed-array: 1.1.19

  es-array-method-boxes-properly@1.0.0: {}

  es-define-property@1.0.1: {}

  es-errors@1.3.0: {}

  es-get-iterator@1.1.3:
    dependencies:
      call-bind: 1.0.8
      get-intrinsic: 1.3.0
      has-symbols: 1.1.0
      is-arguments: 1.2.0
      is-map: 2.0.3
      is-set: 2.0.3
      is-string: 1.1.1
      isarray: 2.0.5
      stop-iteration-iterator: 1.1.0

  es-iterator-helpers@1.2.1:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      es-set-tostringtag: 2.1.0
      function-bind: 1.1.2
      get-intrinsic: 1.3.0
      globalthis: 1.0.4
      gopd: 1.2.0
      has-property-descriptors: 1.0.2
      has-proto: 1.2.0
      has-symbols: 1.1.0
      internal-slot: 1.1.0
      iterator.prototype: 1.1.5
      safe-array-concat: 1.1.3

  es-module-lexer@1.7.0: {}

  es-object-atoms@1.1.1:
    dependencies:
      es-errors: 1.3.0

  es-set-tostringtag@2.1.0:
    dependencies:
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  es-shim-unscopables@1.1.0:
    dependencies:
      hasown: 2.0.2

  es-to-primitive@1.3.0:
    dependencies:
      is-callable: 1.2.7
      is-date-object: 1.1.0
      is-symbol: 1.1.1

  escalade@3.2.0: {}

  escape-html@1.0.3: {}

  escape-string-regexp@1.0.5: {}

  escape-string-regexp@2.0.0: {}

  escape-string-regexp@4.0.0: {}

  escodegen@1.14.3:
    dependencies:
      esprima: 4.0.1
      estraverse: 4.3.0
      esutils: 2.0.3
      optionator: 0.8.3
    optionalDependencies:
      source-map: 0.6.1

  escodegen@2.1.0:
    dependencies:
      esprima: 4.0.1
      estraverse: 5.3.0
      esutils: 2.0.3
    optionalDependencies:
      source-map: 0.6.1

  eslint-config-react-app@7.0.1(@babel/plugin-syntax-flow@7.27.1(@babel/core@7.28.0))(@babel/plugin-transform-react-jsx@7.27.1(@babel/core@7.28.0))(eslint@8.57.1)(jest@27.5.1(ts-node@10.9.2(@types/node@16.18.126)(typescript@4.9.5)))(typescript@4.9.5):
    dependencies:
      '@babel/core': 7.28.0
      '@babel/eslint-parser': 7.28.0(@babel/core@7.28.0)(eslint@8.57.1)
      '@rushstack/eslint-patch': 1.12.0
      '@typescript-eslint/eslint-plugin': 5.62.0(@typescript-eslint/parser@5.62.0(eslint@8.57.1)(typescript@4.9.5))(eslint@8.57.1)(typescript@4.9.5)
      '@typescript-eslint/parser': 5.62.0(eslint@8.57.1)(typescript@4.9.5)
      babel-preset-react-app: 10.1.0
      confusing-browser-globals: 1.0.11
      eslint: 8.57.1
      eslint-plugin-flowtype: 8.0.3(@babel/plugin-syntax-flow@7.27.1(@babel/core@7.28.0))(@babel/plugin-transform-react-jsx@7.27.1(@babel/core@7.28.0))(eslint@8.57.1)
      eslint-plugin-import: 2.32.0(@typescript-eslint/parser@5.62.0(eslint@8.57.1)(typescript@4.9.5))(eslint@8.57.1)
      eslint-plugin-jest: 25.7.0(@typescript-eslint/eslint-plugin@5.62.0(@typescript-eslint/parser@5.62.0(eslint@8.57.1)(typescript@4.9.5))(eslint@8.57.1)(typescript@4.9.5))(eslint@8.57.1)(jest@27.5.1(ts-node@10.9.2(@types/node@16.18.126)(typescript@4.9.5)))(typescript@4.9.5)
      eslint-plugin-jsx-a11y: 6.10.2(eslint@8.57.1)
      eslint-plugin-react: 7.37.5(eslint@8.57.1)
      eslint-plugin-react-hooks: 4.6.2(eslint@8.57.1)
      eslint-plugin-testing-library: 5.11.1(eslint@8.57.1)(typescript@4.9.5)
    optionalDependencies:
      typescript: 4.9.5
    transitivePeerDependencies:
      - '@babel/plugin-syntax-flow'
      - '@babel/plugin-transform-react-jsx'
      - eslint-import-resolver-typescript
      - eslint-import-resolver-webpack
      - jest
      - supports-color

  eslint-import-resolver-node@0.3.9:
    dependencies:
      debug: 3.2.7
      is-core-module: 2.16.1
      resolve: 1.22.10
    transitivePeerDependencies:
      - supports-color

  eslint-module-utils@2.12.1(@typescript-eslint/parser@5.62.0(eslint@8.57.1)(typescript@4.9.5))(eslint-import-resolver-node@0.3.9)(eslint@8.57.1):
    dependencies:
      debug: 3.2.7
    optionalDependencies:
      '@typescript-eslint/parser': 5.62.0(eslint@8.57.1)(typescript@4.9.5)
      eslint: 8.57.1
      eslint-import-resolver-node: 0.3.9
    transitivePeerDependencies:
      - supports-color

  eslint-plugin-flowtype@8.0.3(@babel/plugin-syntax-flow@7.27.1(@babel/core@7.28.0))(@babel/plugin-transform-react-jsx@7.27.1(@babel/core@7.28.0))(eslint@8.57.1):
    dependencies:
      '@babel/plugin-syntax-flow': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-react-jsx': 7.27.1(@babel/core@7.28.0)
      eslint: 8.57.1
      lodash: 4.17.21
      string-natural-compare: 3.0.1

  eslint-plugin-import@2.32.0(@typescript-eslint/parser@5.62.0(eslint@8.57.1)(typescript@4.9.5))(eslint@8.57.1):
    dependencies:
      '@rtsao/scc': 1.1.0
      array-includes: 3.1.9
      array.prototype.findlastindex: 1.2.6
      array.prototype.flat: 1.3.3
      array.prototype.flatmap: 1.3.3
      debug: 3.2.7
      doctrine: 2.1.0
      eslint: 8.57.1
      eslint-import-resolver-node: 0.3.9
      eslint-module-utils: 2.12.1(@typescript-eslint/parser@5.62.0(eslint@8.57.1)(typescript@4.9.5))(eslint-import-resolver-node@0.3.9)(eslint@8.57.1)
      hasown: 2.0.2
      is-core-module: 2.16.1
      is-glob: 4.0.3
      minimatch: 3.1.2
      object.fromentries: 2.0.8
      object.groupby: 1.0.3
      object.values: 1.2.1
      semver: 6.3.1
      string.prototype.trimend: 1.0.9
      tsconfig-paths: 3.15.0
    optionalDependencies:
      '@typescript-eslint/parser': 5.62.0(eslint@8.57.1)(typescript@4.9.5)
    transitivePeerDependencies:
      - eslint-import-resolver-typescript
      - eslint-import-resolver-webpack
      - supports-color

  eslint-plugin-jest@25.7.0(@typescript-eslint/eslint-plugin@5.62.0(@typescript-eslint/parser@5.62.0(eslint@8.57.1)(typescript@4.9.5))(eslint@8.57.1)(typescript@4.9.5))(eslint@8.57.1)(jest@27.5.1(ts-node@10.9.2(@types/node@16.18.126)(typescript@4.9.5)))(typescript@4.9.5):
    dependencies:
      '@typescript-eslint/experimental-utils': 5.62.0(eslint@8.57.1)(typescript@4.9.5)
      eslint: 8.57.1
    optionalDependencies:
      '@typescript-eslint/eslint-plugin': 5.62.0(@typescript-eslint/parser@5.62.0(eslint@8.57.1)(typescript@4.9.5))(eslint@8.57.1)(typescript@4.9.5)
      jest: 27.5.1(ts-node@10.9.2(@types/node@16.18.126)(typescript@4.9.5))
    transitivePeerDependencies:
      - supports-color
      - typescript

  eslint-plugin-jsx-a11y@6.10.2(eslint@8.57.1):
    dependencies:
      aria-query: 5.3.2
      array-includes: 3.1.9
      array.prototype.flatmap: 1.3.3
      ast-types-flow: 0.0.8
      axe-core: 4.10.3
      axobject-query: 4.1.0
      damerau-levenshtein: 1.0.8
      emoji-regex: 9.2.2
      eslint: 8.57.1
      hasown: 2.0.2
      jsx-ast-utils: 3.3.5
      language-tags: 1.0.9
      minimatch: 3.1.2
      object.fromentries: 2.0.8
      safe-regex-test: 1.1.0
      string.prototype.includes: 2.0.1

  eslint-plugin-react-hooks@4.6.2(eslint@8.57.1):
    dependencies:
      eslint: 8.57.1

  eslint-plugin-react@7.37.5(eslint@8.57.1):
    dependencies:
      array-includes: 3.1.9
      array.prototype.findlast: 1.2.5
      array.prototype.flatmap: 1.3.3
      array.prototype.tosorted: 1.1.4
      doctrine: 2.1.0
      es-iterator-helpers: 1.2.1
      eslint: 8.57.1
      estraverse: 5.3.0
      hasown: 2.0.2
      jsx-ast-utils: 3.3.5
      minimatch: 3.1.2
      object.entries: 1.1.9
      object.fromentries: 2.0.8
      object.values: 1.2.1
      prop-types: 15.8.1
      resolve: 2.0.0-next.5
      semver: 6.3.1
      string.prototype.matchall: 4.0.12
      string.prototype.repeat: 1.0.0

  eslint-plugin-testing-library@5.11.1(eslint@8.57.1)(typescript@4.9.5):
    dependencies:
      '@typescript-eslint/utils': 5.62.0(eslint@8.57.1)(typescript@4.9.5)
      eslint: 8.57.1
    transitivePeerDependencies:
      - supports-color
      - typescript

  eslint-scope@5.1.1:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 4.3.0

  eslint-scope@7.2.2:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0

  eslint-visitor-keys@2.1.0: {}

  eslint-visitor-keys@3.4.3: {}

  eslint-webpack-plugin@3.2.0(eslint@8.57.1)(webpack@5.100.0):
    dependencies:
      '@types/eslint': 8.56.12
      eslint: 8.57.1
      jest-worker: 28.1.3
      micromatch: 4.0.8
      normalize-path: 3.0.0
      schema-utils: 4.3.2
      webpack: 5.100.0

  eslint@8.57.1:
    dependencies:
      '@eslint-community/eslint-utils': 4.7.0(eslint@8.57.1)
      '@eslint-community/regexpp': 4.12.1
      '@eslint/eslintrc': 2.1.4
      '@eslint/js': 8.57.1
      '@humanwhocodes/config-array': 0.13.0
      '@humanwhocodes/module-importer': 1.0.1
      '@nodelib/fs.walk': 1.2.8
      '@ungap/structured-clone': 1.3.0
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.6
      debug: 4.4.1
      doctrine: 3.0.0
      escape-string-regexp: 4.0.0
      eslint-scope: 7.2.2
      eslint-visitor-keys: 3.4.3
      espree: 9.6.1
      esquery: 1.6.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 6.0.1
      find-up: 5.0.0
      glob-parent: 6.0.2
      globals: 13.24.0
      graphemer: 1.4.0
      ignore: 5.3.2
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      is-path-inside: 3.0.3
      js-yaml: 4.1.0
      json-stable-stringify-without-jsonify: 1.0.1
      levn: 0.4.1
      lodash.merge: 4.6.2
      minimatch: 3.1.2
      natural-compare: 1.4.0
      optionator: 0.9.4
      strip-ansi: 6.0.1
      text-table: 0.2.0
    transitivePeerDependencies:
      - supports-color

  espree@9.6.1:
    dependencies:
      acorn: 8.15.0
      acorn-jsx: 5.3.2(acorn@8.15.0)
      eslint-visitor-keys: 3.4.3

  esprima@1.2.2: {}

  esprima@4.0.1: {}

  esquery@1.6.0:
    dependencies:
      estraverse: 5.3.0

  esrecurse@4.3.0:
    dependencies:
      estraverse: 5.3.0

  estraverse@4.3.0: {}

  estraverse@5.3.0: {}

  estree-walker@1.0.1: {}

  esutils@2.0.3: {}

  etag@1.8.1: {}

  eventemitter3@4.0.7: {}

  events@3.3.0: {}

  execa@5.1.1:
    dependencies:
      cross-spawn: 7.0.6
      get-stream: 6.0.1
      human-signals: 2.1.0
      is-stream: 2.0.1
      merge-stream: 2.0.0
      npm-run-path: 4.0.1
      onetime: 5.1.2
      signal-exit: 3.0.7
      strip-final-newline: 2.0.0

  exit@0.1.2: {}

  expect@27.5.1:
    dependencies:
      '@jest/types': 27.5.1
      jest-get-type: 27.5.1
      jest-matcher-utils: 27.5.1
      jest-message-util: 27.5.1

  express@4.21.2:
    dependencies:
      accepts: 1.3.8
      array-flatten: 1.1.1
      body-parser: 1.20.3
      content-disposition: 0.5.4
      content-type: 1.0.5
      cookie: 0.7.1
      cookie-signature: 1.0.6
      debug: 2.6.9
      depd: 2.0.0
      encodeurl: 2.0.0
      escape-html: 1.0.3
      etag: 1.8.1
      finalhandler: 1.3.1
      fresh: 0.5.2
      http-errors: 2.0.0
      merge-descriptors: 1.0.3
      methods: 1.1.2
      on-finished: 2.4.1
      parseurl: 1.3.3
      path-to-regexp: 0.1.12
      proxy-addr: 2.0.7
      qs: 6.13.0
      range-parser: 1.2.1
      safe-buffer: 5.2.1
      send: 0.19.0
      serve-static: 1.16.2
      setprototypeof: 1.2.0
      statuses: 2.0.1
      type-is: 1.6.18
      utils-merge: 1.0.1
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color

  fast-deep-equal@3.1.3: {}

  fast-glob@3.3.3:
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.8

  fast-json-stable-stringify@2.1.0: {}

  fast-levenshtein@2.0.6: {}

  fast-uri@3.0.6: {}

  fastq@1.19.1:
    dependencies:
      reusify: 1.1.0

  faye-websocket@0.11.4:
    dependencies:
      websocket-driver: 0.7.4

  fb-watchman@2.0.2:
    dependencies:
      bser: 2.1.1

  file-entry-cache@6.0.1:
    dependencies:
      flat-cache: 3.2.0

  file-loader@6.2.0(webpack@5.100.0):
    dependencies:
      loader-utils: 2.0.4
      schema-utils: 3.3.0
      webpack: 5.100.0

  filelist@1.0.4:
    dependencies:
      minimatch: 5.1.6

  filesize@8.0.7: {}

  fill-range@7.1.1:
    dependencies:
      to-regex-range: 5.0.1

  finalhandler@1.3.1:
    dependencies:
      debug: 2.6.9
      encodeurl: 2.0.0
      escape-html: 1.0.3
      on-finished: 2.4.1
      parseurl: 1.3.3
      statuses: 2.0.1
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color

  find-cache-dir@3.3.2:
    dependencies:
      commondir: 1.0.1
      make-dir: 3.1.0
      pkg-dir: 4.2.0

  find-up@3.0.0:
    dependencies:
      locate-path: 3.0.0

  find-up@4.1.0:
    dependencies:
      locate-path: 5.0.0
      path-exists: 4.0.0

  find-up@5.0.0:
    dependencies:
      locate-path: 6.0.0
      path-exists: 4.0.0

  flat-cache@3.2.0:
    dependencies:
      flatted: 3.3.3
      keyv: 4.5.4
      rimraf: 3.0.2

  flat@5.0.2: {}

  flatted@3.3.3: {}

  follow-redirects@1.15.9: {}

  for-each@0.3.5:
    dependencies:
      is-callable: 1.2.7

  foreground-child@3.3.1:
    dependencies:
      cross-spawn: 7.0.6
      signal-exit: 4.1.0

  fork-ts-checker-webpack-plugin@6.5.3(eslint@8.57.1)(typescript@4.9.5)(webpack@5.100.0):
    dependencies:
      '@babel/code-frame': 7.27.1
      '@types/json-schema': 7.0.15
      chalk: 4.1.2
      chokidar: 3.6.0
      cosmiconfig: 6.0.0
      deepmerge: 4.3.1
      fs-extra: 9.1.0
      glob: 7.2.3
      memfs: 3.5.3
      minimatch: 3.1.2
      schema-utils: 2.7.0
      semver: 7.7.2
      tapable: 1.1.3
      typescript: 4.9.5
      webpack: 5.100.0
    optionalDependencies:
      eslint: 8.57.1

  form-data@3.0.3:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      es-set-tostringtag: 2.1.0
      mime-types: 2.1.35

  forwarded@0.2.0: {}

  fraction.js@4.3.7: {}

  fresh@0.5.2: {}

  fs-extra@10.1.0:
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 6.1.0
      universalify: 2.0.1

  fs-extra@9.1.0:
    dependencies:
      at-least-node: 1.0.0
      graceful-fs: 4.2.11
      jsonfile: 6.1.0
      universalify: 2.0.1

  fs-monkey@1.0.6: {}

  fs.realpath@1.0.0: {}

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  function.prototype.name@1.1.8:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      functions-have-names: 1.2.3
      hasown: 2.0.2
      is-callable: 1.2.7

  functions-have-names@1.2.3: {}

  gensync@1.0.0-beta.2: {}

  get-caller-file@2.0.5: {}

  get-intrinsic@1.3.0:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      function-bind: 1.1.2
      get-proto: 1.0.1
      gopd: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      math-intrinsics: 1.1.0

  get-own-enumerable-property-symbols@3.0.2: {}

  get-package-type@0.1.0: {}

  get-proto@1.0.1:
    dependencies:
      dunder-proto: 1.0.1
      es-object-atoms: 1.1.1

  get-stream@6.0.1: {}

  get-symbol-description@1.1.0:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3

  glob-parent@6.0.2:
    dependencies:
      is-glob: 4.0.3

  glob-to-regexp@0.4.1: {}

  glob@10.4.5:
    dependencies:
      foreground-child: 3.3.1
      jackspeak: 3.4.3
      minimatch: 9.0.5
      minipass: 7.1.2
      package-json-from-dist: 1.0.1
      path-scurry: 1.11.1

  glob@7.2.3:
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  global-modules@2.0.0:
    dependencies:
      global-prefix: 3.0.0

  global-prefix@3.0.0:
    dependencies:
      ini: 1.3.8
      kind-of: 6.0.3
      which: 1.3.1

  globals@13.24.0:
    dependencies:
      type-fest: 0.20.2

  globalthis@1.0.4:
    dependencies:
      define-properties: 1.2.1
      gopd: 1.2.0

  globby@11.1.0:
    dependencies:
      array-union: 2.1.0
      dir-glob: 3.0.1
      fast-glob: 3.3.3
      ignore: 5.3.2
      merge2: 1.4.1
      slash: 3.0.0

  gopd@1.2.0: {}

  graceful-fs@4.2.11: {}

  graphemer@1.4.0: {}

  gzip-size@6.0.0:
    dependencies:
      duplexer: 0.1.2

  handle-thing@2.0.1: {}

  harmony-reflect@1.6.2: {}

  has-bigints@1.1.0: {}

  has-flag@3.0.0: {}

  has-flag@4.0.0: {}

  has-property-descriptors@1.0.2:
    dependencies:
      es-define-property: 1.0.1

  has-proto@1.2.0:
    dependencies:
      dunder-proto: 1.0.1

  has-symbols@1.1.0: {}

  has-tostringtag@1.0.2:
    dependencies:
      has-symbols: 1.1.0

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  he@1.2.0: {}

  hoopy@0.1.4: {}

  hpack.js@2.1.6:
    dependencies:
      inherits: 2.0.4
      obuf: 1.1.2
      readable-stream: 2.3.8
      wbuf: 1.7.3

  html-encoding-sniffer@2.0.1:
    dependencies:
      whatwg-encoding: 1.0.5

  html-entities@2.6.0: {}

  html-escaper@2.0.2: {}

  html-minifier-terser@6.1.0:
    dependencies:
      camel-case: 4.1.2
      clean-css: 5.3.3
      commander: 8.3.0
      he: 1.2.0
      param-case: 3.0.4
      relateurl: 0.2.7
      terser: 5.43.1

  html-webpack-plugin@5.6.3(webpack@5.100.0):
    dependencies:
      '@types/html-minifier-terser': 6.1.0
      html-minifier-terser: 6.1.0
      lodash: 4.17.21
      pretty-error: 4.0.0
      tapable: 2.2.2
    optionalDependencies:
      webpack: 5.100.0

  htmlparser2@6.1.0:
    dependencies:
      domelementtype: 2.3.0
      domhandler: 4.3.1
      domutils: 2.8.0
      entities: 2.2.0

  http-deceiver@1.2.7: {}

  http-errors@1.6.3:
    dependencies:
      depd: 1.1.2
      inherits: 2.0.3
      setprototypeof: 1.1.0
      statuses: 1.5.0

  http-errors@2.0.0:
    dependencies:
      depd: 2.0.0
      inherits: 2.0.4
      setprototypeof: 1.2.0
      statuses: 2.0.1
      toidentifier: 1.0.1

  http-parser-js@0.5.10: {}

  http-proxy-agent@4.0.1:
    dependencies:
      '@tootallnate/once': 1.1.2
      agent-base: 6.0.2
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  http-proxy-middleware@2.0.9(@types/express@4.17.23):
    dependencies:
      '@types/http-proxy': 1.17.16
      http-proxy: 1.18.1
      is-glob: 4.0.3
      is-plain-obj: 3.0.0
      micromatch: 4.0.8
    optionalDependencies:
      '@types/express': 4.17.23
    transitivePeerDependencies:
      - debug

  http-proxy@1.18.1:
    dependencies:
      eventemitter3: 4.0.7
      follow-redirects: 1.15.9
      requires-port: 1.0.0
    transitivePeerDependencies:
      - debug

  https-proxy-agent@5.0.1:
    dependencies:
      agent-base: 6.0.2
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  human-signals@2.1.0: {}

  iconv-lite@0.4.24:
    dependencies:
      safer-buffer: 2.1.2

  iconv-lite@0.6.3:
    dependencies:
      safer-buffer: 2.1.2

  icss-utils@5.1.0(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6

  idb@7.1.1: {}

  identity-obj-proxy@3.0.0:
    dependencies:
      harmony-reflect: 1.6.2

  ignore@5.3.2: {}

  image-size@0.5.5:
    optional: true

  immer@9.0.21: {}

  import-fresh@3.3.1:
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0

  import-local@3.2.0:
    dependencies:
      pkg-dir: 4.2.0
      resolve-cwd: 3.0.0

  imurmurhash@0.1.4: {}

  indent-string@4.0.0: {}

  inflight@1.0.6:
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  inherits@2.0.3: {}

  inherits@2.0.4: {}

  ini@1.3.8: {}

  internal-slot@1.1.0:
    dependencies:
      es-errors: 1.3.0
      hasown: 2.0.2
      side-channel: 1.1.0

  ipaddr.js@1.9.1: {}

  ipaddr.js@2.2.0: {}

  is-arguments@1.2.0:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-array-buffer@3.0.5:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      get-intrinsic: 1.3.0

  is-arrayish@0.2.1: {}

  is-async-function@2.1.1:
    dependencies:
      async-function: 1.0.0
      call-bound: 1.0.4
      get-proto: 1.0.1
      has-tostringtag: 1.0.2
      safe-regex-test: 1.1.0

  is-bigint@1.1.0:
    dependencies:
      has-bigints: 1.1.0

  is-binary-path@2.1.0:
    dependencies:
      binary-extensions: 2.3.0

  is-boolean-object@1.2.2:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-callable@1.2.7: {}

  is-core-module@2.16.1:
    dependencies:
      hasown: 2.0.2

  is-data-view@1.0.2:
    dependencies:
      call-bound: 1.0.4
      get-intrinsic: 1.3.0
      is-typed-array: 1.1.15

  is-date-object@1.1.0:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-docker@2.2.1: {}

  is-extglob@2.1.1: {}

  is-finalizationregistry@1.1.1:
    dependencies:
      call-bound: 1.0.4

  is-fullwidth-code-point@3.0.0: {}

  is-generator-fn@2.1.0: {}

  is-generator-function@1.1.0:
    dependencies:
      call-bound: 1.0.4
      get-proto: 1.0.1
      has-tostringtag: 1.0.2
      safe-regex-test: 1.1.0

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-map@2.0.3: {}

  is-module@1.0.0: {}

  is-negative-zero@2.0.3: {}

  is-number-object@1.1.1:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-number@7.0.0: {}

  is-obj@1.0.1: {}

  is-path-inside@3.0.3: {}

  is-plain-obj@3.0.0: {}

  is-plain-object@2.0.4:
    dependencies:
      isobject: 3.0.1

  is-potential-custom-element-name@1.0.1: {}

  is-regex@1.2.1:
    dependencies:
      call-bound: 1.0.4
      gopd: 1.2.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  is-regexp@1.0.0: {}

  is-root@2.1.0: {}

  is-set@2.0.3: {}

  is-shared-array-buffer@1.0.4:
    dependencies:
      call-bound: 1.0.4

  is-stream@2.0.1: {}

  is-string@1.1.1:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-symbol@1.1.1:
    dependencies:
      call-bound: 1.0.4
      has-symbols: 1.1.0
      safe-regex-test: 1.1.0

  is-typed-array@1.1.15:
    dependencies:
      which-typed-array: 1.1.19

  is-typedarray@1.0.0: {}

  is-weakmap@2.0.2: {}

  is-weakref@1.1.1:
    dependencies:
      call-bound: 1.0.4

  is-weakset@2.0.4:
    dependencies:
      call-bound: 1.0.4
      get-intrinsic: 1.3.0

  is-what@3.14.1: {}

  is-wsl@2.2.0:
    dependencies:
      is-docker: 2.2.1

  isarray@1.0.0: {}

  isarray@2.0.5: {}

  isexe@2.0.0: {}

  isobject@3.0.1: {}

  istanbul-lib-coverage@3.2.2: {}

  istanbul-lib-instrument@5.2.1:
    dependencies:
      '@babel/core': 7.28.0
      '@babel/parser': 7.28.0
      '@istanbuljs/schema': 0.1.3
      istanbul-lib-coverage: 3.2.2
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  istanbul-lib-report@3.0.1:
    dependencies:
      istanbul-lib-coverage: 3.2.2
      make-dir: 4.0.0
      supports-color: 7.2.0

  istanbul-lib-source-maps@4.0.1:
    dependencies:
      debug: 4.4.1
      istanbul-lib-coverage: 3.2.2
      source-map: 0.6.1
    transitivePeerDependencies:
      - supports-color

  istanbul-reports@3.1.7:
    dependencies:
      html-escaper: 2.0.2
      istanbul-lib-report: 3.0.1

  iterator.prototype@1.1.5:
    dependencies:
      define-data-property: 1.1.4
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      get-proto: 1.0.1
      has-symbols: 1.1.0
      set-function-name: 2.0.2

  jackspeak@3.4.3:
    dependencies:
      '@isaacs/cliui': 8.0.2
    optionalDependencies:
      '@pkgjs/parseargs': 0.11.0

  jake@10.9.2:
    dependencies:
      async: 3.2.6
      chalk: 4.1.2
      filelist: 1.0.4
      minimatch: 3.1.2

  jest-changed-files@27.5.1:
    dependencies:
      '@jest/types': 27.5.1
      execa: 5.1.1
      throat: 6.0.2

  jest-circus@27.5.1:
    dependencies:
      '@jest/environment': 27.5.1
      '@jest/test-result': 27.5.1
      '@jest/types': 27.5.1
      '@types/node': 16.18.126
      chalk: 4.1.2
      co: 4.6.0
      dedent: 0.7.0
      expect: 27.5.1
      is-generator-fn: 2.1.0
      jest-each: 27.5.1
      jest-matcher-utils: 27.5.1
      jest-message-util: 27.5.1
      jest-runtime: 27.5.1
      jest-snapshot: 27.5.1
      jest-util: 27.5.1
      pretty-format: 27.5.1
      slash: 3.0.0
      stack-utils: 2.0.6
      throat: 6.0.2
    transitivePeerDependencies:
      - supports-color

  jest-cli@27.5.1(ts-node@10.9.2(@types/node@16.18.126)(typescript@4.9.5)):
    dependencies:
      '@jest/core': 27.5.1(ts-node@10.9.2(@types/node@16.18.126)(typescript@4.9.5))
      '@jest/test-result': 27.5.1
      '@jest/types': 27.5.1
      chalk: 4.1.2
      exit: 0.1.2
      graceful-fs: 4.2.11
      import-local: 3.2.0
      jest-config: 27.5.1(ts-node@10.9.2(@types/node@16.18.126)(typescript@4.9.5))
      jest-util: 27.5.1
      jest-validate: 27.5.1
      prompts: 2.4.2
      yargs: 16.2.0
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - ts-node
      - utf-8-validate

  jest-config@27.5.1(ts-node@10.9.2(@types/node@16.18.126)(typescript@4.9.5)):
    dependencies:
      '@babel/core': 7.28.0
      '@jest/test-sequencer': 27.5.1
      '@jest/types': 27.5.1
      babel-jest: 27.5.1(@babel/core@7.28.0)
      chalk: 4.1.2
      ci-info: 3.9.0
      deepmerge: 4.3.1
      glob: 7.2.3
      graceful-fs: 4.2.11
      jest-circus: 27.5.1
      jest-environment-jsdom: 27.5.1
      jest-environment-node: 27.5.1
      jest-get-type: 27.5.1
      jest-jasmine2: 27.5.1
      jest-regex-util: 27.5.1
      jest-resolve: 27.5.1
      jest-runner: 27.5.1
      jest-util: 27.5.1
      jest-validate: 27.5.1
      micromatch: 4.0.8
      parse-json: 5.2.0
      pretty-format: 27.5.1
      slash: 3.0.0
      strip-json-comments: 3.1.1
    optionalDependencies:
      ts-node: 10.9.2(@types/node@16.18.126)(typescript@4.9.5)
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate

  jest-diff@27.5.1:
    dependencies:
      chalk: 4.1.2
      diff-sequences: 27.5.1
      jest-get-type: 27.5.1
      pretty-format: 27.5.1

  jest-docblock@27.5.1:
    dependencies:
      detect-newline: 3.1.0

  jest-each@27.5.1:
    dependencies:
      '@jest/types': 27.5.1
      chalk: 4.1.2
      jest-get-type: 27.5.1
      jest-util: 27.5.1
      pretty-format: 27.5.1

  jest-environment-jsdom@27.5.1:
    dependencies:
      '@jest/environment': 27.5.1
      '@jest/fake-timers': 27.5.1
      '@jest/types': 27.5.1
      '@types/node': 16.18.126
      jest-mock: 27.5.1
      jest-util: 27.5.1
      jsdom: 16.7.0
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate

  jest-environment-node@27.5.1:
    dependencies:
      '@jest/environment': 27.5.1
      '@jest/fake-timers': 27.5.1
      '@jest/types': 27.5.1
      '@types/node': 16.18.126
      jest-mock: 27.5.1
      jest-util: 27.5.1

  jest-get-type@27.5.1: {}

  jest-haste-map@27.5.1:
    dependencies:
      '@jest/types': 27.5.1
      '@types/graceful-fs': 4.1.9
      '@types/node': 16.18.126
      anymatch: 3.1.3
      fb-watchman: 2.0.2
      graceful-fs: 4.2.11
      jest-regex-util: 27.5.1
      jest-serializer: 27.5.1
      jest-util: 27.5.1
      jest-worker: 27.5.1
      micromatch: 4.0.8
      walker: 1.0.8
    optionalDependencies:
      fsevents: 2.3.3

  jest-jasmine2@27.5.1:
    dependencies:
      '@jest/environment': 27.5.1
      '@jest/source-map': 27.5.1
      '@jest/test-result': 27.5.1
      '@jest/types': 27.5.1
      '@types/node': 16.18.126
      chalk: 4.1.2
      co: 4.6.0
      expect: 27.5.1
      is-generator-fn: 2.1.0
      jest-each: 27.5.1
      jest-matcher-utils: 27.5.1
      jest-message-util: 27.5.1
      jest-runtime: 27.5.1
      jest-snapshot: 27.5.1
      jest-util: 27.5.1
      pretty-format: 27.5.1
      throat: 6.0.2
    transitivePeerDependencies:
      - supports-color

  jest-leak-detector@27.5.1:
    dependencies:
      jest-get-type: 27.5.1
      pretty-format: 27.5.1

  jest-matcher-utils@27.5.1:
    dependencies:
      chalk: 4.1.2
      jest-diff: 27.5.1
      jest-get-type: 27.5.1
      pretty-format: 27.5.1

  jest-message-util@27.5.1:
    dependencies:
      '@babel/code-frame': 7.27.1
      '@jest/types': 27.5.1
      '@types/stack-utils': 2.0.3
      chalk: 4.1.2
      graceful-fs: 4.2.11
      micromatch: 4.0.8
      pretty-format: 27.5.1
      slash: 3.0.0
      stack-utils: 2.0.6

  jest-message-util@28.1.3:
    dependencies:
      '@babel/code-frame': 7.27.1
      '@jest/types': 28.1.3
      '@types/stack-utils': 2.0.3
      chalk: 4.1.2
      graceful-fs: 4.2.11
      micromatch: 4.0.8
      pretty-format: 28.1.3
      slash: 3.0.0
      stack-utils: 2.0.6

  jest-mock@27.5.1:
    dependencies:
      '@jest/types': 27.5.1
      '@types/node': 16.18.126

  jest-pnp-resolver@1.2.3(jest-resolve@27.5.1):
    optionalDependencies:
      jest-resolve: 27.5.1

  jest-regex-util@27.5.1: {}

  jest-regex-util@28.0.2: {}

  jest-resolve-dependencies@27.5.1:
    dependencies:
      '@jest/types': 27.5.1
      jest-regex-util: 27.5.1
      jest-snapshot: 27.5.1
    transitivePeerDependencies:
      - supports-color

  jest-resolve@27.5.1:
    dependencies:
      '@jest/types': 27.5.1
      chalk: 4.1.2
      graceful-fs: 4.2.11
      jest-haste-map: 27.5.1
      jest-pnp-resolver: 1.2.3(jest-resolve@27.5.1)
      jest-util: 27.5.1
      jest-validate: 27.5.1
      resolve: 1.22.10
      resolve.exports: 1.1.1
      slash: 3.0.0

  jest-runner@27.5.1:
    dependencies:
      '@jest/console': 27.5.1
      '@jest/environment': 27.5.1
      '@jest/test-result': 27.5.1
      '@jest/transform': 27.5.1
      '@jest/types': 27.5.1
      '@types/node': 16.18.126
      chalk: 4.1.2
      emittery: 0.8.1
      graceful-fs: 4.2.11
      jest-docblock: 27.5.1
      jest-environment-jsdom: 27.5.1
      jest-environment-node: 27.5.1
      jest-haste-map: 27.5.1
      jest-leak-detector: 27.5.1
      jest-message-util: 27.5.1
      jest-resolve: 27.5.1
      jest-runtime: 27.5.1
      jest-util: 27.5.1
      jest-worker: 27.5.1
      source-map-support: 0.5.21
      throat: 6.0.2
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate

  jest-runtime@27.5.1:
    dependencies:
      '@jest/environment': 27.5.1
      '@jest/fake-timers': 27.5.1
      '@jest/globals': 27.5.1
      '@jest/source-map': 27.5.1
      '@jest/test-result': 27.5.1
      '@jest/transform': 27.5.1
      '@jest/types': 27.5.1
      chalk: 4.1.2
      cjs-module-lexer: 1.4.3
      collect-v8-coverage: 1.0.2
      execa: 5.1.1
      glob: 7.2.3
      graceful-fs: 4.2.11
      jest-haste-map: 27.5.1
      jest-message-util: 27.5.1
      jest-mock: 27.5.1
      jest-regex-util: 27.5.1
      jest-resolve: 27.5.1
      jest-snapshot: 27.5.1
      jest-util: 27.5.1
      slash: 3.0.0
      strip-bom: 4.0.0
    transitivePeerDependencies:
      - supports-color

  jest-serializer@27.5.1:
    dependencies:
      '@types/node': 16.18.126
      graceful-fs: 4.2.11

  jest-snapshot@27.5.1:
    dependencies:
      '@babel/core': 7.28.0
      '@babel/generator': 7.28.0
      '@babel/plugin-syntax-typescript': 7.27.1(@babel/core@7.28.0)
      '@babel/traverse': 7.28.0
      '@babel/types': 7.28.0
      '@jest/transform': 27.5.1
      '@jest/types': 27.5.1
      '@types/babel__traverse': 7.20.7
      '@types/prettier': 2.7.3
      babel-preset-current-node-syntax: 1.1.0(@babel/core@7.28.0)
      chalk: 4.1.2
      expect: 27.5.1
      graceful-fs: 4.2.11
      jest-diff: 27.5.1
      jest-get-type: 27.5.1
      jest-haste-map: 27.5.1
      jest-matcher-utils: 27.5.1
      jest-message-util: 27.5.1
      jest-util: 27.5.1
      natural-compare: 1.4.0
      pretty-format: 27.5.1
      semver: 7.7.2
    transitivePeerDependencies:
      - supports-color

  jest-util@27.5.1:
    dependencies:
      '@jest/types': 27.5.1
      '@types/node': 16.18.126
      chalk: 4.1.2
      ci-info: 3.9.0
      graceful-fs: 4.2.11
      picomatch: 2.3.1

  jest-util@28.1.3:
    dependencies:
      '@jest/types': 28.1.3
      '@types/node': 16.18.126
      chalk: 4.1.2
      ci-info: 3.9.0
      graceful-fs: 4.2.11
      picomatch: 2.3.1

  jest-validate@27.5.1:
    dependencies:
      '@jest/types': 27.5.1
      camelcase: 6.3.0
      chalk: 4.1.2
      jest-get-type: 27.5.1
      leven: 3.1.0
      pretty-format: 27.5.1

  jest-watch-typeahead@1.1.0(jest@27.5.1(ts-node@10.9.2(@types/node@16.18.126)(typescript@4.9.5))):
    dependencies:
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      jest: 27.5.1(ts-node@10.9.2(@types/node@16.18.126)(typescript@4.9.5))
      jest-regex-util: 28.0.2
      jest-watcher: 28.1.3
      slash: 4.0.0
      string-length: 5.0.1
      strip-ansi: 7.1.0

  jest-watcher@27.5.1:
    dependencies:
      '@jest/test-result': 27.5.1
      '@jest/types': 27.5.1
      '@types/node': 16.18.126
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      jest-util: 27.5.1
      string-length: 4.0.2

  jest-watcher@28.1.3:
    dependencies:
      '@jest/test-result': 28.1.3
      '@jest/types': 28.1.3
      '@types/node': 16.18.126
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      emittery: 0.10.2
      jest-util: 28.1.3
      string-length: 4.0.2

  jest-worker@26.6.2:
    dependencies:
      '@types/node': 16.18.126
      merge-stream: 2.0.0
      supports-color: 7.2.0

  jest-worker@27.5.1:
    dependencies:
      '@types/node': 16.18.126
      merge-stream: 2.0.0
      supports-color: 8.1.1

  jest-worker@28.1.3:
    dependencies:
      '@types/node': 16.18.126
      merge-stream: 2.0.0
      supports-color: 8.1.1

  jest@27.5.1(ts-node@10.9.2(@types/node@16.18.126)(typescript@4.9.5)):
    dependencies:
      '@jest/core': 27.5.1(ts-node@10.9.2(@types/node@16.18.126)(typescript@4.9.5))
      import-local: 3.2.0
      jest-cli: 27.5.1(ts-node@10.9.2(@types/node@16.18.126)(typescript@4.9.5))
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - ts-node
      - utf-8-validate

  jiti@1.21.7: {}

  js-tokens@4.0.0: {}

  js-yaml@3.14.1:
    dependencies:
      argparse: 1.0.10
      esprima: 4.0.1

  js-yaml@4.1.0:
    dependencies:
      argparse: 2.0.1

  jsdom@16.7.0:
    dependencies:
      abab: 2.0.6
      acorn: 8.15.0
      acorn-globals: 6.0.0
      cssom: 0.4.4
      cssstyle: 2.3.0
      data-urls: 2.0.0
      decimal.js: 10.5.0
      domexception: 2.0.1
      escodegen: 2.1.0
      form-data: 3.0.3
      html-encoding-sniffer: 2.0.1
      http-proxy-agent: 4.0.1
      https-proxy-agent: 5.0.1
      is-potential-custom-element-name: 1.0.1
      nwsapi: 2.2.20
      parse5: 6.0.1
      saxes: 5.0.1
      symbol-tree: 3.2.4
      tough-cookie: 4.1.4
      w3c-hr-time: 1.0.2
      w3c-xmlserializer: 2.0.0
      webidl-conversions: 6.1.0
      whatwg-encoding: 1.0.5
      whatwg-mimetype: 2.3.0
      whatwg-url: 8.7.0
      ws: 7.5.10
      xml-name-validator: 3.0.0
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  jsesc@3.0.2: {}

  jsesc@3.1.0: {}

  json-buffer@3.0.1: {}

  json-parse-even-better-errors@2.3.1: {}

  json-schema-traverse@0.4.1: {}

  json-schema-traverse@1.0.0: {}

  json-schema@0.4.0: {}

  json-stable-stringify-without-jsonify@1.0.1: {}

  json5@1.0.2:
    dependencies:
      minimist: 1.2.8

  json5@2.2.3: {}

  jsonfile@6.1.0:
    dependencies:
      universalify: 2.0.1
    optionalDependencies:
      graceful-fs: 4.2.11

  jsonpath@1.1.1:
    dependencies:
      esprima: 1.2.2
      static-eval: 2.0.2
      underscore: 1.12.1

  jsonpointer@5.0.1: {}

  jsx-ast-utils@3.3.5:
    dependencies:
      array-includes: 3.1.9
      array.prototype.flat: 1.3.3
      object.assign: 4.1.7
      object.values: 1.2.1

  keyv@4.5.4:
    dependencies:
      json-buffer: 3.0.1

  kind-of@6.0.3: {}

  kleur@3.0.3: {}

  klona@2.0.6: {}

  language-subtag-registry@0.3.23: {}

  language-tags@1.0.9:
    dependencies:
      language-subtag-registry: 0.3.23

  launch-editor@2.10.0:
    dependencies:
      picocolors: 1.1.1
      shell-quote: 1.8.3

  less-loader@11.1.4(less@4.3.0)(webpack@5.100.0):
    dependencies:
      less: 4.3.0
      webpack: 5.100.0

  less-loader@7.3.0(less@4.3.0)(webpack@5.100.0):
    dependencies:
      klona: 2.0.6
      less: 4.3.0
      loader-utils: 2.0.4
      schema-utils: 3.3.0
      webpack: 5.100.0

  less@4.3.0:
    dependencies:
      copy-anything: 2.0.6
      parse-node-version: 1.0.1
      tslib: 2.8.1
    optionalDependencies:
      errno: 0.1.8
      graceful-fs: 4.2.11
      image-size: 0.5.5
      make-dir: 2.1.0
      mime: 1.6.0
      needle: 3.3.1
      source-map: 0.6.1

  leven@3.1.0: {}

  levn@0.3.0:
    dependencies:
      prelude-ls: 1.1.2
      type-check: 0.3.2

  levn@0.4.1:
    dependencies:
      prelude-ls: 1.2.1
      type-check: 0.4.0

  lilconfig@2.1.0: {}

  lilconfig@3.1.3: {}

  lines-and-columns@1.2.4: {}

  loader-runner@4.3.0: {}

  loader-utils@2.0.4:
    dependencies:
      big.js: 5.2.2
      emojis-list: 3.0.0
      json5: 2.2.3

  loader-utils@3.3.1: {}

  locate-path@3.0.0:
    dependencies:
      p-locate: 3.0.0
      path-exists: 3.0.0

  locate-path@5.0.0:
    dependencies:
      p-locate: 4.1.0

  locate-path@6.0.0:
    dependencies:
      p-locate: 5.0.0

  lodash.debounce@4.0.8: {}

  lodash.memoize@4.1.2: {}

  lodash.merge@4.6.2: {}

  lodash.sortby@4.7.0: {}

  lodash.uniq@4.5.0: {}

  lodash@4.17.21: {}

  loose-envify@1.4.0:
    dependencies:
      js-tokens: 4.0.0

  lower-case@2.0.2:
    dependencies:
      tslib: 2.8.1

  lru-cache@10.4.3: {}

  lru-cache@5.1.1:
    dependencies:
      yallist: 3.1.1

  lz-string@1.5.0: {}

  magic-string@0.25.9:
    dependencies:
      sourcemap-codec: 1.4.8

  make-dir@2.1.0:
    dependencies:
      pify: 4.0.1
      semver: 5.7.2
    optional: true

  make-dir@3.1.0:
    dependencies:
      semver: 6.3.1

  make-dir@4.0.0:
    dependencies:
      semver: 7.7.2

  make-error@1.3.6: {}

  makeerror@1.0.12:
    dependencies:
      tmpl: 1.0.5

  math-intrinsics@1.1.0: {}

  mdn-data@2.0.14: {}

  mdn-data@2.0.4: {}

  media-typer@0.3.0: {}

  memfs@3.5.3:
    dependencies:
      fs-monkey: 1.0.6

  merge-descriptors@1.0.3: {}

  merge-stream@2.0.0: {}

  merge2@1.4.1: {}

  methods@1.1.2: {}

  micromatch@4.0.8:
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1

  mime-db@1.52.0: {}

  mime-db@1.54.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  mime@1.6.0: {}

  mimic-fn@2.1.0: {}

  min-indent@1.0.1: {}

  mini-css-extract-plugin@2.9.2(webpack@5.100.0):
    dependencies:
      schema-utils: 4.3.2
      tapable: 2.2.2
      webpack: 5.100.0

  minimalistic-assert@1.0.1: {}

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.12

  minimatch@5.1.6:
    dependencies:
      brace-expansion: 2.0.2

  minimatch@9.0.5:
    dependencies:
      brace-expansion: 2.0.2

  minimist@1.2.8: {}

  minipass@7.1.2: {}

  mkdirp@0.5.6:
    dependencies:
      minimist: 1.2.8

  ms@2.0.0: {}

  ms@2.1.3: {}

  multicast-dns@7.2.5:
    dependencies:
      dns-packet: 5.6.1
      thunky: 1.1.0

  mz@2.7.0:
    dependencies:
      any-promise: 1.3.0
      object-assign: 4.1.1
      thenify-all: 1.6.0

  nanoid@3.3.11: {}

  natural-compare-lite@1.4.0: {}

  natural-compare@1.4.0: {}

  needle@3.3.1:
    dependencies:
      iconv-lite: 0.6.3
      sax: 1.4.1
    optional: true

  negotiator@0.6.3: {}

  negotiator@0.6.4: {}

  neo-async@2.6.2: {}

  no-case@3.0.4:
    dependencies:
      lower-case: 2.0.2
      tslib: 2.8.1

  node-forge@1.3.1: {}

  node-int64@0.4.0: {}

  node-releases@2.0.19: {}

  normalize-path@3.0.0: {}

  normalize-range@0.1.2: {}

  normalize-url@6.1.0: {}

  npm-run-path@4.0.1:
    dependencies:
      path-key: 3.1.1

  nth-check@1.0.2:
    dependencies:
      boolbase: 1.0.0

  nth-check@2.1.1:
    dependencies:
      boolbase: 1.0.0

  nwsapi@2.2.20: {}

  object-assign@4.1.1: {}

  object-hash@3.0.0: {}

  object-inspect@1.13.4: {}

  object-is@1.1.6:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1

  object-keys@1.1.1: {}

  object.assign@4.1.7:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1
      has-symbols: 1.1.0
      object-keys: 1.1.1

  object.entries@1.1.9:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  object.fromentries@2.0.8:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-object-atoms: 1.1.1

  object.getownpropertydescriptors@2.1.8:
    dependencies:
      array.prototype.reduce: 1.0.8
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-object-atoms: 1.1.1
      gopd: 1.2.0
      safe-array-concat: 1.1.3

  object.groupby@1.0.3:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0

  object.values@1.2.1:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  obuf@1.1.2: {}

  on-finished@2.4.1:
    dependencies:
      ee-first: 1.1.1

  on-headers@1.0.2: {}

  once@1.4.0:
    dependencies:
      wrappy: 1.0.2

  onetime@5.1.2:
    dependencies:
      mimic-fn: 2.1.0

  open@8.4.2:
    dependencies:
      define-lazy-prop: 2.0.0
      is-docker: 2.2.1
      is-wsl: 2.2.0

  optionator@0.8.3:
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.3.0
      prelude-ls: 1.1.2
      type-check: 0.3.2
      word-wrap: 1.2.5

  optionator@0.9.4:
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.4.1
      prelude-ls: 1.2.1
      type-check: 0.4.0
      word-wrap: 1.2.5

  own-keys@1.0.1:
    dependencies:
      get-intrinsic: 1.3.0
      object-keys: 1.1.1
      safe-push-apply: 1.0.0

  p-limit@2.3.0:
    dependencies:
      p-try: 2.2.0

  p-limit@3.1.0:
    dependencies:
      yocto-queue: 0.1.0

  p-locate@3.0.0:
    dependencies:
      p-limit: 2.3.0

  p-locate@4.1.0:
    dependencies:
      p-limit: 2.3.0

  p-locate@5.0.0:
    dependencies:
      p-limit: 3.1.0

  p-retry@4.6.2:
    dependencies:
      '@types/retry': 0.12.0
      retry: 0.13.1

  p-try@2.2.0: {}

  package-json-from-dist@1.0.1: {}

  param-case@3.0.4:
    dependencies:
      dot-case: 3.0.4
      tslib: 2.8.1

  parent-module@1.0.1:
    dependencies:
      callsites: 3.1.0

  parse-json@5.2.0:
    dependencies:
      '@babel/code-frame': 7.27.1
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4

  parse-node-version@1.0.1: {}

  parse5@6.0.1: {}

  parseurl@1.3.3: {}

  pascal-case@3.1.2:
    dependencies:
      no-case: 3.0.4
      tslib: 2.8.1

  path-exists@3.0.0: {}

  path-exists@4.0.0: {}

  path-is-absolute@1.0.1: {}

  path-key@3.1.1: {}

  path-parse@1.0.7: {}

  path-scurry@1.11.1:
    dependencies:
      lru-cache: 10.4.3
      minipass: 7.1.2

  path-to-regexp@0.1.12: {}

  path-type@4.0.0: {}

  performance-now@2.1.0: {}

  picocolors@0.2.1: {}

  picocolors@1.1.1: {}

  picomatch@2.3.1: {}

  pify@2.3.0: {}

  pify@4.0.1:
    optional: true

  pirates@4.0.7: {}

  pkg-dir@4.2.0:
    dependencies:
      find-up: 4.1.0

  pkg-up@3.1.0:
    dependencies:
      find-up: 3.0.0

  possible-typed-array-names@1.1.0: {}

  postcss-attribute-case-insensitive@5.0.2(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-selector-parser: 6.1.2

  postcss-browser-comments@4.0.0(browserslist@4.25.1)(postcss@8.5.6):
    dependencies:
      browserslist: 4.25.1
      postcss: 8.5.6

  postcss-calc@8.2.4(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-selector-parser: 6.1.2
      postcss-value-parser: 4.2.0

  postcss-clamp@4.1.0(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  postcss-color-functional-notation@4.2.4(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  postcss-color-hex-alpha@8.0.4(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  postcss-color-rebeccapurple@7.1.1(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  postcss-colormin@5.3.1(postcss@8.5.6):
    dependencies:
      browserslist: 4.25.1
      caniuse-api: 3.0.0
      colord: 2.9.3
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  postcss-convert-values@5.1.3(postcss@8.5.6):
    dependencies:
      browserslist: 4.25.1
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  postcss-custom-media@8.0.2(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  postcss-custom-properties@12.1.11(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  postcss-custom-selectors@6.0.3(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-selector-parser: 6.1.2

  postcss-dir-pseudo-class@6.0.5(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-selector-parser: 6.1.2

  postcss-discard-comments@5.1.2(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6

  postcss-discard-duplicates@5.1.0(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6

  postcss-discard-empty@5.1.1(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6

  postcss-discard-overridden@5.1.0(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6

  postcss-double-position-gradients@3.1.2(postcss@8.5.6):
    dependencies:
      '@csstools/postcss-progressive-custom-properties': 1.3.0(postcss@8.5.6)
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  postcss-env-function@4.0.6(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  postcss-flexbugs-fixes@5.0.2(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6

  postcss-focus-visible@6.0.4(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-selector-parser: 6.1.2

  postcss-focus-within@5.0.4(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-selector-parser: 6.1.2

  postcss-font-variant@5.0.0(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6

  postcss-gap-properties@3.0.5(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6

  postcss-image-set-function@4.0.7(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  postcss-import@15.1.0(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-value-parser: 4.2.0
      read-cache: 1.0.0
      resolve: 1.22.10

  postcss-initial@4.0.1(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6

  postcss-js@4.0.1(postcss@8.5.6):
    dependencies:
      camelcase-css: 2.0.1
      postcss: 8.5.6

  postcss-lab-function@4.2.1(postcss@8.5.6):
    dependencies:
      '@csstools/postcss-progressive-custom-properties': 1.3.0(postcss@8.5.6)
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  postcss-load-config@4.0.2(postcss@8.5.6)(ts-node@10.9.2(@types/node@16.18.126)(typescript@4.9.5)):
    dependencies:
      lilconfig: 3.1.3
      yaml: 2.8.0
    optionalDependencies:
      postcss: 8.5.6
      ts-node: 10.9.2(@types/node@16.18.126)(typescript@4.9.5)

  postcss-loader@6.2.1(postcss@8.5.6)(webpack@5.100.0):
    dependencies:
      cosmiconfig: 7.1.0
      klona: 2.0.6
      postcss: 8.5.6
      semver: 7.7.2
      webpack: 5.100.0

  postcss-logical@5.0.4(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6

  postcss-media-minmax@5.0.0(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6

  postcss-merge-longhand@5.1.7(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-value-parser: 4.2.0
      stylehacks: 5.1.1(postcss@8.5.6)

  postcss-merge-rules@5.1.4(postcss@8.5.6):
    dependencies:
      browserslist: 4.25.1
      caniuse-api: 3.0.0
      cssnano-utils: 3.1.0(postcss@8.5.6)
      postcss: 8.5.6
      postcss-selector-parser: 6.1.2

  postcss-minify-font-values@5.1.0(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  postcss-minify-gradients@5.1.1(postcss@8.5.6):
    dependencies:
      colord: 2.9.3
      cssnano-utils: 3.1.0(postcss@8.5.6)
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  postcss-minify-params@5.1.4(postcss@8.5.6):
    dependencies:
      browserslist: 4.25.1
      cssnano-utils: 3.1.0(postcss@8.5.6)
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  postcss-minify-selectors@5.2.1(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-selector-parser: 6.1.2

  postcss-modules-extract-imports@3.1.0(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6

  postcss-modules-local-by-default@4.2.0(postcss@8.5.6):
    dependencies:
      icss-utils: 5.1.0(postcss@8.5.6)
      postcss: 8.5.6
      postcss-selector-parser: 7.1.0
      postcss-value-parser: 4.2.0

  postcss-modules-scope@3.2.1(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-selector-parser: 7.1.0

  postcss-modules-values@4.0.0(postcss@8.5.6):
    dependencies:
      icss-utils: 5.1.0(postcss@8.5.6)
      postcss: 8.5.6

  postcss-nested@6.2.0(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-selector-parser: 6.1.2

  postcss-nesting@10.2.0(postcss@8.5.6):
    dependencies:
      '@csstools/selector-specificity': 2.2.0(postcss-selector-parser@6.1.2)
      postcss: 8.5.6
      postcss-selector-parser: 6.1.2

  postcss-normalize-charset@5.1.0(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6

  postcss-normalize-display-values@5.1.0(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  postcss-normalize-positions@5.1.1(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  postcss-normalize-repeat-style@5.1.1(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  postcss-normalize-string@5.1.0(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  postcss-normalize-timing-functions@5.1.0(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  postcss-normalize-unicode@5.1.1(postcss@8.5.6):
    dependencies:
      browserslist: 4.25.1
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  postcss-normalize-url@5.1.0(postcss@8.5.6):
    dependencies:
      normalize-url: 6.1.0
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  postcss-normalize-whitespace@5.1.1(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  postcss-normalize@10.0.1(browserslist@4.25.1)(postcss@8.5.6):
    dependencies:
      '@csstools/normalize.css': 12.1.1
      browserslist: 4.25.1
      postcss: 8.5.6
      postcss-browser-comments: 4.0.0(browserslist@4.25.1)(postcss@8.5.6)
      sanitize.css: 13.0.0

  postcss-opacity-percentage@1.1.3(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6

  postcss-ordered-values@5.1.3(postcss@8.5.6):
    dependencies:
      cssnano-utils: 3.1.0(postcss@8.5.6)
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  postcss-overflow-shorthand@3.0.4(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  postcss-page-break@3.0.4(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6

  postcss-place@7.0.5(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  postcss-preset-env@7.8.3(postcss@8.5.6):
    dependencies:
      '@csstools/postcss-cascade-layers': 1.1.1(postcss@8.5.6)
      '@csstools/postcss-color-function': 1.1.1(postcss@8.5.6)
      '@csstools/postcss-font-format-keywords': 1.0.1(postcss@8.5.6)
      '@csstools/postcss-hwb-function': 1.0.2(postcss@8.5.6)
      '@csstools/postcss-ic-unit': 1.0.1(postcss@8.5.6)
      '@csstools/postcss-is-pseudo-class': 2.0.7(postcss@8.5.6)
      '@csstools/postcss-nested-calc': 1.0.0(postcss@8.5.6)
      '@csstools/postcss-normalize-display-values': 1.0.1(postcss@8.5.6)
      '@csstools/postcss-oklab-function': 1.1.1(postcss@8.5.6)
      '@csstools/postcss-progressive-custom-properties': 1.3.0(postcss@8.5.6)
      '@csstools/postcss-stepped-value-functions': 1.0.1(postcss@8.5.6)
      '@csstools/postcss-text-decoration-shorthand': 1.0.0(postcss@8.5.6)
      '@csstools/postcss-trigonometric-functions': 1.0.2(postcss@8.5.6)
      '@csstools/postcss-unset-value': 1.0.2(postcss@8.5.6)
      autoprefixer: 10.4.21(postcss@8.5.6)
      browserslist: 4.25.1
      css-blank-pseudo: 3.0.3(postcss@8.5.6)
      css-has-pseudo: 3.0.4(postcss@8.5.6)
      css-prefers-color-scheme: 6.0.3(postcss@8.5.6)
      cssdb: 7.11.2
      postcss: 8.5.6
      postcss-attribute-case-insensitive: 5.0.2(postcss@8.5.6)
      postcss-clamp: 4.1.0(postcss@8.5.6)
      postcss-color-functional-notation: 4.2.4(postcss@8.5.6)
      postcss-color-hex-alpha: 8.0.4(postcss@8.5.6)
      postcss-color-rebeccapurple: 7.1.1(postcss@8.5.6)
      postcss-custom-media: 8.0.2(postcss@8.5.6)
      postcss-custom-properties: 12.1.11(postcss@8.5.6)
      postcss-custom-selectors: 6.0.3(postcss@8.5.6)
      postcss-dir-pseudo-class: 6.0.5(postcss@8.5.6)
      postcss-double-position-gradients: 3.1.2(postcss@8.5.6)
      postcss-env-function: 4.0.6(postcss@8.5.6)
      postcss-focus-visible: 6.0.4(postcss@8.5.6)
      postcss-focus-within: 5.0.4(postcss@8.5.6)
      postcss-font-variant: 5.0.0(postcss@8.5.6)
      postcss-gap-properties: 3.0.5(postcss@8.5.6)
      postcss-image-set-function: 4.0.7(postcss@8.5.6)
      postcss-initial: 4.0.1(postcss@8.5.6)
      postcss-lab-function: 4.2.1(postcss@8.5.6)
      postcss-logical: 5.0.4(postcss@8.5.6)
      postcss-media-minmax: 5.0.0(postcss@8.5.6)
      postcss-nesting: 10.2.0(postcss@8.5.6)
      postcss-opacity-percentage: 1.1.3(postcss@8.5.6)
      postcss-overflow-shorthand: 3.0.4(postcss@8.5.6)
      postcss-page-break: 3.0.4(postcss@8.5.6)
      postcss-place: 7.0.5(postcss@8.5.6)
      postcss-pseudo-class-any-link: 7.1.6(postcss@8.5.6)
      postcss-replace-overflow-wrap: 4.0.0(postcss@8.5.6)
      postcss-selector-not: 6.0.1(postcss@8.5.6)
      postcss-value-parser: 4.2.0

  postcss-pseudo-class-any-link@7.1.6(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-selector-parser: 6.1.2

  postcss-reduce-initial@5.1.2(postcss@8.5.6):
    dependencies:
      browserslist: 4.25.1
      caniuse-api: 3.0.0
      postcss: 8.5.6

  postcss-reduce-transforms@5.1.0(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-value-parser: 4.2.0

  postcss-replace-overflow-wrap@4.0.0(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6

  postcss-selector-not@6.0.1(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-selector-parser: 6.1.2

  postcss-selector-parser@6.1.2:
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2

  postcss-selector-parser@7.1.0:
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2

  postcss-svgo@5.1.0(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-value-parser: 4.2.0
      svgo: 2.8.0

  postcss-unique-selectors@5.1.1(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-selector-parser: 6.1.2

  postcss-value-parser@4.2.0: {}

  postcss@7.0.39:
    dependencies:
      picocolors: 0.2.1
      source-map: 0.6.1

  postcss@8.5.6:
    dependencies:
      nanoid: 3.3.11
      picocolors: 1.1.1
      source-map-js: 1.2.1

  prelude-ls@1.1.2: {}

  prelude-ls@1.2.1: {}

  pretty-bytes@5.6.0: {}

  pretty-error@4.0.0:
    dependencies:
      lodash: 4.17.21
      renderkid: 3.0.0

  pretty-format@27.5.1:
    dependencies:
      ansi-regex: 5.0.1
      ansi-styles: 5.2.0
      react-is: 17.0.2

  pretty-format@28.1.3:
    dependencies:
      '@jest/schemas': 28.1.3
      ansi-regex: 5.0.1
      ansi-styles: 5.2.0
      react-is: 18.3.1

  process-nextick-args@2.0.1: {}

  promise@8.3.0:
    dependencies:
      asap: 2.0.6

  prompts@2.4.2:
    dependencies:
      kleur: 3.0.3
      sisteransi: 1.0.5

  prop-types@15.8.1:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react-is: 16.13.1

  proxy-addr@2.0.7:
    dependencies:
      forwarded: 0.2.0
      ipaddr.js: 1.9.1

  prr@1.0.1:
    optional: true

  psl@1.15.0:
    dependencies:
      punycode: 2.3.1

  punycode@2.3.1: {}

  q@1.5.1: {}

  qs@6.13.0:
    dependencies:
      side-channel: 1.1.0

  querystringify@2.2.0: {}

  queue-microtask@1.2.3: {}

  raf@3.4.1:
    dependencies:
      performance-now: 2.1.0

  randombytes@2.1.0:
    dependencies:
      safe-buffer: 5.2.1

  range-parser@1.2.1: {}

  raw-body@2.5.2:
    dependencies:
      bytes: 3.1.2
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      unpipe: 1.0.0

  react-app-polyfill@3.0.0:
    dependencies:
      core-js: 3.44.0
      object-assign: 4.1.1
      promise: 8.3.0
      raf: 3.4.1
      regenerator-runtime: 0.13.11
      whatwg-fetch: 3.6.20

  react-dev-utils@12.0.1(eslint@8.57.1)(typescript@4.9.5)(webpack@5.100.0):
    dependencies:
      '@babel/code-frame': 7.27.1
      address: 1.2.2
      browserslist: 4.25.1
      chalk: 4.1.2
      cross-spawn: 7.0.6
      detect-port-alt: 1.1.6
      escape-string-regexp: 4.0.0
      filesize: 8.0.7
      find-up: 5.0.0
      fork-ts-checker-webpack-plugin: 6.5.3(eslint@8.57.1)(typescript@4.9.5)(webpack@5.100.0)
      global-modules: 2.0.0
      globby: 11.1.0
      gzip-size: 6.0.0
      immer: 9.0.21
      is-root: 2.1.0
      loader-utils: 3.3.1
      open: 8.4.2
      pkg-up: 3.1.0
      prompts: 2.4.2
      react-error-overlay: 6.1.0
      recursive-readdir: 2.2.3
      shell-quote: 1.8.3
      strip-ansi: 6.0.1
      text-table: 0.2.0
      webpack: 5.100.0
    optionalDependencies:
      typescript: 4.9.5
    transitivePeerDependencies:
      - eslint
      - supports-color
      - vue-template-compiler

  react-dom@18.3.1(react@18.3.1):
    dependencies:
      loose-envify: 1.4.0
      react: 18.3.1
      scheduler: 0.23.2

  react-error-overlay@6.1.0: {}

  react-is@16.13.1: {}

  react-is@17.0.2: {}

  react-is@18.3.1: {}

  react-refresh@0.11.0: {}

  react-router-dom@6.30.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@remix-run/router': 1.23.0
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-router: 6.30.1(react@18.3.1)

  react-router@6.30.1(react@18.3.1):
    dependencies:
      '@remix-run/router': 1.23.0
      react: 18.3.1

  react-scripts@5.0.1(@babel/plugin-syntax-flow@7.27.1(@babel/core@7.28.0))(@babel/plugin-transform-react-jsx@7.27.1(@babel/core@7.28.0))(@types/babel__core@7.20.5)(eslint@8.57.1)(react@18.3.1)(ts-node@10.9.2(@types/node@16.18.126)(typescript@4.9.5))(type-fest@0.21.3)(typescript@4.9.5):
    dependencies:
      '@babel/core': 7.28.0
      '@pmmmwh/react-refresh-webpack-plugin': 0.5.17(react-refresh@0.11.0)(type-fest@0.21.3)(webpack-dev-server@4.15.2(webpack@5.100.0))(webpack@5.100.0)
      '@svgr/webpack': 5.5.0
      babel-jest: 27.5.1(@babel/core@7.28.0)
      babel-loader: 8.4.1(@babel/core@7.28.0)(webpack@5.100.0)
      babel-plugin-named-asset-import: 0.3.8(@babel/core@7.28.0)
      babel-preset-react-app: 10.1.0
      bfj: 7.1.0
      browserslist: 4.25.1
      camelcase: 6.3.0
      case-sensitive-paths-webpack-plugin: 2.4.0
      css-loader: 6.11.0(webpack@5.100.0)
      css-minimizer-webpack-plugin: 3.4.1(webpack@5.100.0)
      dotenv: 10.0.0
      dotenv-expand: 5.1.0
      eslint: 8.57.1
      eslint-config-react-app: 7.0.1(@babel/plugin-syntax-flow@7.27.1(@babel/core@7.28.0))(@babel/plugin-transform-react-jsx@7.27.1(@babel/core@7.28.0))(eslint@8.57.1)(jest@27.5.1(ts-node@10.9.2(@types/node@16.18.126)(typescript@4.9.5)))(typescript@4.9.5)
      eslint-webpack-plugin: 3.2.0(eslint@8.57.1)(webpack@5.100.0)
      file-loader: 6.2.0(webpack@5.100.0)
      fs-extra: 10.1.0
      html-webpack-plugin: 5.6.3(webpack@5.100.0)
      identity-obj-proxy: 3.0.0
      jest: 27.5.1(ts-node@10.9.2(@types/node@16.18.126)(typescript@4.9.5))
      jest-resolve: 27.5.1
      jest-watch-typeahead: 1.1.0(jest@27.5.1(ts-node@10.9.2(@types/node@16.18.126)(typescript@4.9.5)))
      mini-css-extract-plugin: 2.9.2(webpack@5.100.0)
      postcss: 8.5.6
      postcss-flexbugs-fixes: 5.0.2(postcss@8.5.6)
      postcss-loader: 6.2.1(postcss@8.5.6)(webpack@5.100.0)
      postcss-normalize: 10.0.1(browserslist@4.25.1)(postcss@8.5.6)
      postcss-preset-env: 7.8.3(postcss@8.5.6)
      prompts: 2.4.2
      react: 18.3.1
      react-app-polyfill: 3.0.0
      react-dev-utils: 12.0.1(eslint@8.57.1)(typescript@4.9.5)(webpack@5.100.0)
      react-refresh: 0.11.0
      resolve: 1.22.10
      resolve-url-loader: 4.0.0
      sass-loader: 12.6.0(webpack@5.100.0)
      semver: 7.7.2
      source-map-loader: 3.0.2(webpack@5.100.0)
      style-loader: 3.3.4(webpack@5.100.0)
      tailwindcss: 3.4.17(ts-node@10.9.2(@types/node@16.18.126)(typescript@4.9.5))
      terser-webpack-plugin: 5.3.14(webpack@5.100.0)
      webpack: 5.100.0
      webpack-dev-server: 4.15.2(webpack@5.100.0)
      webpack-manifest-plugin: 4.1.1(webpack@5.100.0)
      workbox-webpack-plugin: 6.6.0(@types/babel__core@7.20.5)(webpack@5.100.0)
    optionalDependencies:
      fsevents: 2.3.3
      typescript: 4.9.5
    transitivePeerDependencies:
      - '@babel/plugin-syntax-flow'
      - '@babel/plugin-transform-react-jsx'
      - '@parcel/css'
      - '@rspack/core'
      - '@swc/core'
      - '@types/babel__core'
      - '@types/webpack'
      - bufferutil
      - canvas
      - clean-css
      - csso
      - debug
      - esbuild
      - eslint-import-resolver-typescript
      - eslint-import-resolver-webpack
      - fibers
      - node-notifier
      - node-sass
      - rework
      - rework-visit
      - sass
      - sass-embedded
      - sockjs-client
      - supports-color
      - ts-node
      - type-fest
      - uglify-js
      - utf-8-validate
      - vue-template-compiler
      - webpack-cli
      - webpack-hot-middleware
      - webpack-plugin-serve

  react@18.3.1:
    dependencies:
      loose-envify: 1.4.0

  read-cache@1.0.0:
    dependencies:
      pify: 2.3.0

  readable-stream@2.3.8:
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 1.0.0
      process-nextick-args: 2.0.1
      safe-buffer: 5.1.2
      string_decoder: 1.1.1
      util-deprecate: 1.0.2

  readable-stream@3.6.2:
    dependencies:
      inherits: 2.0.4
      string_decoder: 1.3.0
      util-deprecate: 1.0.2

  readdirp@3.6.0:
    dependencies:
      picomatch: 2.3.1

  recursive-readdir@2.2.3:
    dependencies:
      minimatch: 3.1.2

  redent@3.0.0:
    dependencies:
      indent-string: 4.0.0
      strip-indent: 3.0.0

  reflect.getprototypeof@1.0.10:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      get-proto: 1.0.1
      which-builtin-type: 1.2.1

  regenerate-unicode-properties@10.2.0:
    dependencies:
      regenerate: 1.4.2

  regenerate@1.4.2: {}

  regenerator-runtime@0.13.11: {}

  regex-parser@2.3.1: {}

  regexp.prototype.flags@1.5.4:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-errors: 1.3.0
      get-proto: 1.0.1
      gopd: 1.2.0
      set-function-name: 2.0.2

  regexpu-core@6.2.0:
    dependencies:
      regenerate: 1.4.2
      regenerate-unicode-properties: 10.2.0
      regjsgen: 0.8.0
      regjsparser: 0.12.0
      unicode-match-property-ecmascript: 2.0.0
      unicode-match-property-value-ecmascript: 2.2.0

  regjsgen@0.8.0: {}

  regjsparser@0.12.0:
    dependencies:
      jsesc: 3.0.2

  relateurl@0.2.7: {}

  renderkid@3.0.0:
    dependencies:
      css-select: 4.3.0
      dom-converter: 0.2.0
      htmlparser2: 6.1.0
      lodash: 4.17.21
      strip-ansi: 6.0.1

  require-directory@2.1.1: {}

  require-from-string@2.0.2: {}

  requires-port@1.0.0: {}

  resolve-cwd@3.0.0:
    dependencies:
      resolve-from: 5.0.0

  resolve-from@4.0.0: {}

  resolve-from@5.0.0: {}

  resolve-url-loader@4.0.0:
    dependencies:
      adjust-sourcemap-loader: 4.0.0
      convert-source-map: 1.9.0
      loader-utils: 2.0.4
      postcss: 7.0.39
      source-map: 0.6.1

  resolve.exports@1.1.1: {}

  resolve@1.22.10:
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  resolve@2.0.0-next.5:
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  retry@0.13.1: {}

  reusify@1.1.0: {}

  rimraf@3.0.2:
    dependencies:
      glob: 7.2.3

  rollup-plugin-terser@7.0.2(rollup@2.79.2):
    dependencies:
      '@babel/code-frame': 7.27.1
      jest-worker: 26.6.2
      rollup: 2.79.2
      serialize-javascript: 4.0.0
      terser: 5.43.1

  rollup@2.79.2:
    optionalDependencies:
      fsevents: 2.3.3

  run-parallel@1.2.0:
    dependencies:
      queue-microtask: 1.2.3

  safe-array-concat@1.1.3:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      get-intrinsic: 1.3.0
      has-symbols: 1.1.0
      isarray: 2.0.5

  safe-buffer@5.1.2: {}

  safe-buffer@5.2.1: {}

  safe-push-apply@1.0.0:
    dependencies:
      es-errors: 1.3.0
      isarray: 2.0.5

  safe-regex-test@1.1.0:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-regex: 1.2.1

  safer-buffer@2.1.2: {}

  sanitize.css@13.0.0: {}

  sass-loader@12.6.0(webpack@5.100.0):
    dependencies:
      klona: 2.0.6
      neo-async: 2.6.2
      webpack: 5.100.0

  sax@1.2.4: {}

  sax@1.4.1:
    optional: true

  saxes@5.0.1:
    dependencies:
      xmlchars: 2.2.0

  scheduler@0.23.2:
    dependencies:
      loose-envify: 1.4.0

  schema-utils@2.7.0:
    dependencies:
      '@types/json-schema': 7.0.15
      ajv: 6.12.6
      ajv-keywords: 3.5.2(ajv@6.12.6)

  schema-utils@2.7.1:
    dependencies:
      '@types/json-schema': 7.0.15
      ajv: 6.12.6
      ajv-keywords: 3.5.2(ajv@6.12.6)

  schema-utils@3.3.0:
    dependencies:
      '@types/json-schema': 7.0.15
      ajv: 6.12.6
      ajv-keywords: 3.5.2(ajv@6.12.6)

  schema-utils@4.3.2:
    dependencies:
      '@types/json-schema': 7.0.15
      ajv: 8.17.1
      ajv-formats: 2.1.1(ajv@8.17.1)
      ajv-keywords: 5.1.0(ajv@8.17.1)

  select-hose@2.0.0: {}

  selfsigned@2.4.1:
    dependencies:
      '@types/node-forge': 1.3.11
      node-forge: 1.3.1

  semver@5.7.2:
    optional: true

  semver@6.3.1: {}

  semver@7.7.2: {}

  send@0.19.0:
    dependencies:
      debug: 2.6.9
      depd: 2.0.0
      destroy: 1.2.0
      encodeurl: 1.0.2
      escape-html: 1.0.3
      etag: 1.8.1
      fresh: 0.5.2
      http-errors: 2.0.0
      mime: 1.6.0
      ms: 2.1.3
      on-finished: 2.4.1
      range-parser: 1.2.1
      statuses: 2.0.1
    transitivePeerDependencies:
      - supports-color

  serialize-javascript@4.0.0:
    dependencies:
      randombytes: 2.1.0

  serialize-javascript@6.0.2:
    dependencies:
      randombytes: 2.1.0

  serve-index@1.9.1:
    dependencies:
      accepts: 1.3.8
      batch: 0.6.1
      debug: 2.6.9
      escape-html: 1.0.3
      http-errors: 1.6.3
      mime-types: 2.1.35
      parseurl: 1.3.3
    transitivePeerDependencies:
      - supports-color

  serve-static@1.16.2:
    dependencies:
      encodeurl: 2.0.0
      escape-html: 1.0.3
      parseurl: 1.3.3
      send: 0.19.0
    transitivePeerDependencies:
      - supports-color

  set-function-length@1.2.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.3.0
      gopd: 1.2.0
      has-property-descriptors: 1.0.2

  set-function-name@2.0.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      functions-have-names: 1.2.3
      has-property-descriptors: 1.0.2

  set-proto@1.0.0:
    dependencies:
      dunder-proto: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1

  setprototypeof@1.1.0: {}

  setprototypeof@1.2.0: {}

  shallow-clone@3.0.1:
    dependencies:
      kind-of: 6.0.3

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@3.0.0: {}

  shell-quote@1.8.3: {}

  side-channel-list@1.0.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4

  side-channel-map@1.0.1:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      object-inspect: 1.13.4

  side-channel-weakmap@1.0.2:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      object-inspect: 1.13.4
      side-channel-map: 1.0.1

  side-channel@1.1.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4
      side-channel-list: 1.0.0
      side-channel-map: 1.0.1
      side-channel-weakmap: 1.0.2

  signal-exit@3.0.7: {}

  signal-exit@4.1.0: {}

  sisteransi@1.0.5: {}

  slash@3.0.0: {}

  slash@4.0.0: {}

  sockjs@0.3.24:
    dependencies:
      faye-websocket: 0.11.4
      uuid: 8.3.2
      websocket-driver: 0.7.4

  source-list-map@2.0.1: {}

  source-map-js@1.2.1: {}

  source-map-loader@3.0.2(webpack@5.100.0):
    dependencies:
      abab: 2.0.6
      iconv-lite: 0.6.3
      source-map-js: 1.2.1
      webpack: 5.100.0

  source-map-support@0.5.21:
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1

  source-map@0.6.1: {}

  source-map@0.7.4: {}

  source-map@0.8.0-beta.0:
    dependencies:
      whatwg-url: 7.1.0

  sourcemap-codec@1.4.8: {}

  spdy-transport@3.0.0:
    dependencies:
      debug: 4.4.1
      detect-node: 2.1.0
      hpack.js: 2.1.6
      obuf: 1.1.2
      readable-stream: 3.6.2
      wbuf: 1.7.3
    transitivePeerDependencies:
      - supports-color

  spdy@4.0.2:
    dependencies:
      debug: 4.4.1
      handle-thing: 2.0.1
      http-deceiver: 1.2.7
      select-hose: 2.0.0
      spdy-transport: 3.0.0
    transitivePeerDependencies:
      - supports-color

  sprintf-js@1.0.3: {}

  stable@0.1.8: {}

  stack-utils@2.0.6:
    dependencies:
      escape-string-regexp: 2.0.0

  stackframe@1.3.4: {}

  static-eval@2.0.2:
    dependencies:
      escodegen: 1.14.3

  statuses@1.5.0: {}

  statuses@2.0.1: {}

  stop-iteration-iterator@1.1.0:
    dependencies:
      es-errors: 1.3.0
      internal-slot: 1.1.0

  string-length@4.0.2:
    dependencies:
      char-regex: 1.0.2
      strip-ansi: 6.0.1

  string-length@5.0.1:
    dependencies:
      char-regex: 2.0.2
      strip-ansi: 7.1.0

  string-natural-compare@3.0.1: {}

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  string-width@5.1.2:
    dependencies:
      eastasianwidth: 0.2.0
      emoji-regex: 9.2.2
      strip-ansi: 7.1.0

  string.prototype.includes@2.0.1:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0

  string.prototype.matchall@4.0.12:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      gopd: 1.2.0
      has-symbols: 1.1.0
      internal-slot: 1.1.0
      regexp.prototype.flags: 1.5.4
      set-function-name: 2.0.2
      side-channel: 1.1.0

  string.prototype.repeat@1.0.0:
    dependencies:
      define-properties: 1.2.1
      es-abstract: 1.24.0

  string.prototype.trim@1.2.10:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-data-property: 1.1.4
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-object-atoms: 1.1.1
      has-property-descriptors: 1.0.2

  string.prototype.trimend@1.0.9:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  string.prototype.trimstart@1.0.8:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  string_decoder@1.1.1:
    dependencies:
      safe-buffer: 5.1.2

  string_decoder@1.3.0:
    dependencies:
      safe-buffer: 5.2.1

  stringify-object@3.3.0:
    dependencies:
      get-own-enumerable-property-symbols: 3.0.2
      is-obj: 1.0.1
      is-regexp: 1.0.0

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  strip-ansi@7.1.0:
    dependencies:
      ansi-regex: 6.1.0

  strip-bom@3.0.0: {}

  strip-bom@4.0.0: {}

  strip-comments@2.0.1: {}

  strip-final-newline@2.0.0: {}

  strip-indent@3.0.0:
    dependencies:
      min-indent: 1.0.1

  strip-json-comments@3.1.1: {}

  style-loader@3.3.4(webpack@5.100.0):
    dependencies:
      webpack: 5.100.0

  stylehacks@5.1.1(postcss@8.5.6):
    dependencies:
      browserslist: 4.25.1
      postcss: 8.5.6
      postcss-selector-parser: 6.1.2

  sucrase@3.35.0:
    dependencies:
      '@jridgewell/gen-mapping': 0.3.12
      commander: 4.1.1
      glob: 10.4.5
      lines-and-columns: 1.2.4
      mz: 2.7.0
      pirates: 4.0.7
      ts-interface-checker: 0.1.13

  supports-color@5.5.0:
    dependencies:
      has-flag: 3.0.0

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  supports-color@8.1.1:
    dependencies:
      has-flag: 4.0.0

  supports-hyperlinks@2.3.0:
    dependencies:
      has-flag: 4.0.0
      supports-color: 7.2.0

  supports-preserve-symlinks-flag@1.0.0: {}

  svg-parser@2.0.4: {}

  svgo@1.3.2:
    dependencies:
      chalk: 2.4.2
      coa: 2.0.2
      css-select: 2.1.0
      css-select-base-adapter: 0.1.1
      css-tree: 1.0.0-alpha.37
      csso: 4.2.0
      js-yaml: 3.14.1
      mkdirp: 0.5.6
      object.values: 1.2.1
      sax: 1.2.4
      stable: 0.1.8
      unquote: 1.1.1
      util.promisify: 1.0.1

  svgo@2.8.0:
    dependencies:
      '@trysound/sax': 0.2.0
      commander: 7.2.0
      css-select: 4.3.0
      css-tree: 1.1.3
      csso: 4.2.0
      picocolors: 1.1.1
      stable: 0.1.8

  symbol-tree@3.2.4: {}

  tailwindcss@3.4.17(ts-node@10.9.2(@types/node@16.18.126)(typescript@4.9.5)):
    dependencies:
      '@alloc/quick-lru': 5.2.0
      arg: 5.0.2
      chokidar: 3.6.0
      didyoumean: 1.2.2
      dlv: 1.1.3
      fast-glob: 3.3.3
      glob-parent: 6.0.2
      is-glob: 4.0.3
      jiti: 1.21.7
      lilconfig: 3.1.3
      micromatch: 4.0.8
      normalize-path: 3.0.0
      object-hash: 3.0.0
      picocolors: 1.1.1
      postcss: 8.5.6
      postcss-import: 15.1.0(postcss@8.5.6)
      postcss-js: 4.0.1(postcss@8.5.6)
      postcss-load-config: 4.0.2(postcss@8.5.6)(ts-node@10.9.2(@types/node@16.18.126)(typescript@4.9.5))
      postcss-nested: 6.2.0(postcss@8.5.6)
      postcss-selector-parser: 6.1.2
      resolve: 1.22.10
      sucrase: 3.35.0
    transitivePeerDependencies:
      - ts-node

  tapable@1.1.3: {}

  tapable@2.2.2: {}

  temp-dir@2.0.0: {}

  tempy@0.6.0:
    dependencies:
      is-stream: 2.0.1
      temp-dir: 2.0.0
      type-fest: 0.16.0
      unique-string: 2.0.0

  terminal-link@2.1.1:
    dependencies:
      ansi-escapes: 4.3.2
      supports-hyperlinks: 2.3.0

  terser-webpack-plugin@5.3.14(webpack@5.100.0):
    dependencies:
      '@jridgewell/trace-mapping': 0.3.29
      jest-worker: 27.5.1
      schema-utils: 4.3.2
      serialize-javascript: 6.0.2
      terser: 5.43.1
      webpack: 5.100.0

  terser@5.43.1:
    dependencies:
      '@jridgewell/source-map': 0.3.6
      acorn: 8.15.0
      commander: 2.20.3
      source-map-support: 0.5.21

  test-exclude@6.0.0:
    dependencies:
      '@istanbuljs/schema': 0.1.3
      glob: 7.2.3
      minimatch: 3.1.2

  text-table@0.2.0: {}

  thenify-all@1.6.0:
    dependencies:
      thenify: 3.3.1

  thenify@3.3.1:
    dependencies:
      any-promise: 1.3.0

  throat@6.0.2: {}

  thunky@1.1.0: {}

  tmpl@1.0.5: {}

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  toidentifier@1.0.1: {}

  tough-cookie@4.1.4:
    dependencies:
      psl: 1.15.0
      punycode: 2.3.1
      universalify: 0.2.0
      url-parse: 1.5.10

  tr46@1.0.1:
    dependencies:
      punycode: 2.3.1

  tr46@2.1.0:
    dependencies:
      punycode: 2.3.1

  tryer@1.0.1: {}

  ts-interface-checker@0.1.13: {}

  ts-node@10.9.2(@types/node@16.18.126)(typescript@4.9.5):
    dependencies:
      '@cspotcode/source-map-support': 0.8.1
      '@tsconfig/node10': 1.0.11
      '@tsconfig/node12': 1.0.11
      '@tsconfig/node14': 1.0.3
      '@tsconfig/node16': 1.0.4
      '@types/node': 16.18.126
      acorn: 8.15.0
      acorn-walk: 8.3.4
      arg: 4.1.3
      create-require: 1.1.1
      diff: 4.0.2
      make-error: 1.3.6
      typescript: 4.9.5
      v8-compile-cache-lib: 3.0.1
      yn: 3.1.1

  tsconfig-paths@3.15.0:
    dependencies:
      '@types/json5': 0.0.29
      json5: 1.0.2
      minimist: 1.2.8
      strip-bom: 3.0.0

  tslib@1.14.1: {}

  tslib@2.8.1: {}

  tsutils@3.21.0(typescript@4.9.5):
    dependencies:
      tslib: 1.14.1
      typescript: 4.9.5

  type-check@0.3.2:
    dependencies:
      prelude-ls: 1.1.2

  type-check@0.4.0:
    dependencies:
      prelude-ls: 1.2.1

  type-detect@4.0.8: {}

  type-fest@0.16.0: {}

  type-fest@0.20.2: {}

  type-fest@0.21.3: {}

  type-is@1.6.18:
    dependencies:
      media-typer: 0.3.0
      mime-types: 2.1.35

  typed-array-buffer@1.0.3:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-typed-array: 1.1.15

  typed-array-byte-length@1.0.3:
    dependencies:
      call-bind: 1.0.8
      for-each: 0.3.5
      gopd: 1.2.0
      has-proto: 1.2.0
      is-typed-array: 1.1.15

  typed-array-byte-offset@1.0.4:
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      for-each: 0.3.5
      gopd: 1.2.0
      has-proto: 1.2.0
      is-typed-array: 1.1.15
      reflect.getprototypeof: 1.0.10

  typed-array-length@1.0.7:
    dependencies:
      call-bind: 1.0.8
      for-each: 0.3.5
      gopd: 1.2.0
      is-typed-array: 1.1.15
      possible-typed-array-names: 1.1.0
      reflect.getprototypeof: 1.0.10

  typedarray-to-buffer@3.1.5:
    dependencies:
      is-typedarray: 1.0.0

  typescript@4.9.5: {}

  unbox-primitive@1.1.0:
    dependencies:
      call-bound: 1.0.4
      has-bigints: 1.1.0
      has-symbols: 1.1.0
      which-boxed-primitive: 1.1.1

  underscore@1.12.1: {}

  unicode-canonical-property-names-ecmascript@2.0.1: {}

  unicode-match-property-ecmascript@2.0.0:
    dependencies:
      unicode-canonical-property-names-ecmascript: 2.0.1
      unicode-property-aliases-ecmascript: 2.1.0

  unicode-match-property-value-ecmascript@2.2.0: {}

  unicode-property-aliases-ecmascript@2.1.0: {}

  unique-string@2.0.0:
    dependencies:
      crypto-random-string: 2.0.0

  universalify@0.2.0: {}

  universalify@2.0.1: {}

  unpipe@1.0.0: {}

  unquote@1.1.1: {}

  upath@1.2.0: {}

  update-browserslist-db@1.1.3(browserslist@4.25.1):
    dependencies:
      browserslist: 4.25.1
      escalade: 3.2.0
      picocolors: 1.1.1

  uri-js@4.4.1:
    dependencies:
      punycode: 2.3.1

  url-parse@1.5.10:
    dependencies:
      querystringify: 2.2.0
      requires-port: 1.0.0

  util-deprecate@1.0.2: {}

  util.promisify@1.0.1:
    dependencies:
      define-properties: 1.2.1
      es-abstract: 1.24.0
      has-symbols: 1.1.0
      object.getownpropertydescriptors: 2.1.8

  utila@0.4.0: {}

  utils-merge@1.0.1: {}

  uuid@8.3.2: {}

  v8-compile-cache-lib@3.0.1: {}

  v8-to-istanbul@8.1.1:
    dependencies:
      '@types/istanbul-lib-coverage': 2.0.6
      convert-source-map: 1.9.0
      source-map: 0.7.4

  vary@1.1.2: {}

  w3c-hr-time@1.0.2:
    dependencies:
      browser-process-hrtime: 1.0.0

  w3c-xmlserializer@2.0.0:
    dependencies:
      xml-name-validator: 3.0.0

  walker@1.0.8:
    dependencies:
      makeerror: 1.0.12

  watchpack@2.4.4:
    dependencies:
      glob-to-regexp: 0.4.1
      graceful-fs: 4.2.11

  wbuf@1.7.3:
    dependencies:
      minimalistic-assert: 1.0.1

  web-vitals@2.1.4: {}

  webidl-conversions@4.0.2: {}

  webidl-conversions@5.0.0: {}

  webidl-conversions@6.1.0: {}

  webpack-dev-middleware@5.3.4(webpack@5.100.0):
    dependencies:
      colorette: 2.0.20
      memfs: 3.5.3
      mime-types: 2.1.35
      range-parser: 1.2.1
      schema-utils: 4.3.2
      webpack: 5.100.0

  webpack-dev-server@4.15.2(webpack@5.100.0):
    dependencies:
      '@types/bonjour': 3.5.13
      '@types/connect-history-api-fallback': 1.5.4
      '@types/express': 4.17.23
      '@types/serve-index': 1.9.4
      '@types/serve-static': 1.15.8
      '@types/sockjs': 0.3.36
      '@types/ws': 8.18.1
      ansi-html-community: 0.0.8
      bonjour-service: 1.3.0
      chokidar: 3.6.0
      colorette: 2.0.20
      compression: 1.8.0
      connect-history-api-fallback: 2.0.0
      default-gateway: 6.0.3
      express: 4.21.2
      graceful-fs: 4.2.11
      html-entities: 2.6.0
      http-proxy-middleware: 2.0.9(@types/express@4.17.23)
      ipaddr.js: 2.2.0
      launch-editor: 2.10.0
      open: 8.4.2
      p-retry: 4.6.2
      rimraf: 3.0.2
      schema-utils: 4.3.2
      selfsigned: 2.4.1
      serve-index: 1.9.1
      sockjs: 0.3.24
      spdy: 4.0.2
      webpack-dev-middleware: 5.3.4(webpack@5.100.0)
      ws: 8.18.3
    optionalDependencies:
      webpack: 5.100.0
    transitivePeerDependencies:
      - bufferutil
      - debug
      - supports-color
      - utf-8-validate

  webpack-manifest-plugin@4.1.1(webpack@5.100.0):
    dependencies:
      tapable: 2.2.2
      webpack: 5.100.0
      webpack-sources: 2.3.1

  webpack-merge@5.10.0:
    dependencies:
      clone-deep: 4.0.1
      flat: 5.0.2
      wildcard: 2.0.1

  webpack-sources@1.4.3:
    dependencies:
      source-list-map: 2.0.1
      source-map: 0.6.1

  webpack-sources@2.3.1:
    dependencies:
      source-list-map: 2.0.1
      source-map: 0.6.1

  webpack-sources@3.3.3: {}

  webpack@5.100.0:
    dependencies:
      '@types/eslint-scope': 3.7.7
      '@types/estree': 1.0.8
      '@types/json-schema': 7.0.15
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/wasm-edit': 1.14.1
      '@webassemblyjs/wasm-parser': 1.14.1
      acorn: 8.15.0
      acorn-import-phases: 1.0.3(acorn@8.15.0)
      browserslist: 4.25.1
      chrome-trace-event: 1.0.4
      enhanced-resolve: 5.18.2
      es-module-lexer: 1.7.0
      eslint-scope: 5.1.1
      events: 3.3.0
      glob-to-regexp: 0.4.1
      graceful-fs: 4.2.11
      json-parse-even-better-errors: 2.3.1
      loader-runner: 4.3.0
      mime-types: 2.1.35
      neo-async: 2.6.2
      schema-utils: 4.3.2
      tapable: 2.2.2
      terser-webpack-plugin: 5.3.14(webpack@5.100.0)
      watchpack: 2.4.4
      webpack-sources: 3.3.3
    transitivePeerDependencies:
      - '@swc/core'
      - esbuild
      - uglify-js

  websocket-driver@0.7.4:
    dependencies:
      http-parser-js: 0.5.10
      safe-buffer: 5.2.1
      websocket-extensions: 0.1.4

  websocket-extensions@0.1.4: {}

  whatwg-encoding@1.0.5:
    dependencies:
      iconv-lite: 0.4.24

  whatwg-fetch@3.6.20: {}

  whatwg-mimetype@2.3.0: {}

  whatwg-url@7.1.0:
    dependencies:
      lodash.sortby: 4.7.0
      tr46: 1.0.1
      webidl-conversions: 4.0.2

  whatwg-url@8.7.0:
    dependencies:
      lodash: 4.17.21
      tr46: 2.1.0
      webidl-conversions: 6.1.0

  which-boxed-primitive@1.1.1:
    dependencies:
      is-bigint: 1.1.0
      is-boolean-object: 1.2.2
      is-number-object: 1.1.1
      is-string: 1.1.1
      is-symbol: 1.1.1

  which-builtin-type@1.2.1:
    dependencies:
      call-bound: 1.0.4
      function.prototype.name: 1.1.8
      has-tostringtag: 1.0.2
      is-async-function: 2.1.1
      is-date-object: 1.1.0
      is-finalizationregistry: 1.1.1
      is-generator-function: 1.1.0
      is-regex: 1.2.1
      is-weakref: 1.1.1
      isarray: 2.0.5
      which-boxed-primitive: 1.1.1
      which-collection: 1.0.2
      which-typed-array: 1.1.19

  which-collection@1.0.2:
    dependencies:
      is-map: 2.0.3
      is-set: 2.0.3
      is-weakmap: 2.0.2
      is-weakset: 2.0.4

  which-typed-array@1.1.19:
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      call-bound: 1.0.4
      for-each: 0.3.5
      get-proto: 1.0.1
      gopd: 1.2.0
      has-tostringtag: 1.0.2

  which@1.3.1:
    dependencies:
      isexe: 2.0.0

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  wildcard@2.0.1: {}

  word-wrap@1.2.5: {}

  workbox-background-sync@6.6.0:
    dependencies:
      idb: 7.1.1
      workbox-core: 6.6.0

  workbox-broadcast-update@6.6.0:
    dependencies:
      workbox-core: 6.6.0

  workbox-build@6.6.0(@types/babel__core@7.20.5):
    dependencies:
      '@apideck/better-ajv-errors': 0.3.6(ajv@8.17.1)
      '@babel/core': 7.28.0
      '@babel/preset-env': 7.28.0(@babel/core@7.28.0)
      '@babel/runtime': 7.27.6
      '@rollup/plugin-babel': 5.3.1(@babel/core@7.28.0)(@types/babel__core@7.20.5)(rollup@2.79.2)
      '@rollup/plugin-node-resolve': 11.2.1(rollup@2.79.2)
      '@rollup/plugin-replace': 2.4.2(rollup@2.79.2)
      '@surma/rollup-plugin-off-main-thread': 2.2.3
      ajv: 8.17.1
      common-tags: 1.8.2
      fast-json-stable-stringify: 2.1.0
      fs-extra: 9.1.0
      glob: 7.2.3
      lodash: 4.17.21
      pretty-bytes: 5.6.0
      rollup: 2.79.2
      rollup-plugin-terser: 7.0.2(rollup@2.79.2)
      source-map: 0.8.0-beta.0
      stringify-object: 3.3.0
      strip-comments: 2.0.1
      tempy: 0.6.0
      upath: 1.2.0
      workbox-background-sync: 6.6.0
      workbox-broadcast-update: 6.6.0
      workbox-cacheable-response: 6.6.0
      workbox-core: 6.6.0
      workbox-expiration: 6.6.0
      workbox-google-analytics: 6.6.0
      workbox-navigation-preload: 6.6.0
      workbox-precaching: 6.6.0
      workbox-range-requests: 6.6.0
      workbox-recipes: 6.6.0
      workbox-routing: 6.6.0
      workbox-strategies: 6.6.0
      workbox-streams: 6.6.0
      workbox-sw: 6.6.0
      workbox-window: 6.6.0
    transitivePeerDependencies:
      - '@types/babel__core'
      - supports-color

  workbox-cacheable-response@6.6.0:
    dependencies:
      workbox-core: 6.6.0

  workbox-core@6.6.0: {}

  workbox-expiration@6.6.0:
    dependencies:
      idb: 7.1.1
      workbox-core: 6.6.0

  workbox-google-analytics@6.6.0:
    dependencies:
      workbox-background-sync: 6.6.0
      workbox-core: 6.6.0
      workbox-routing: 6.6.0
      workbox-strategies: 6.6.0

  workbox-navigation-preload@6.6.0:
    dependencies:
      workbox-core: 6.6.0

  workbox-precaching@6.6.0:
    dependencies:
      workbox-core: 6.6.0
      workbox-routing: 6.6.0
      workbox-strategies: 6.6.0

  workbox-range-requests@6.6.0:
    dependencies:
      workbox-core: 6.6.0

  workbox-recipes@6.6.0:
    dependencies:
      workbox-cacheable-response: 6.6.0
      workbox-core: 6.6.0
      workbox-expiration: 6.6.0
      workbox-precaching: 6.6.0
      workbox-routing: 6.6.0
      workbox-strategies: 6.6.0

  workbox-routing@6.6.0:
    dependencies:
      workbox-core: 6.6.0

  workbox-strategies@6.6.0:
    dependencies:
      workbox-core: 6.6.0

  workbox-streams@6.6.0:
    dependencies:
      workbox-core: 6.6.0
      workbox-routing: 6.6.0

  workbox-sw@6.6.0: {}

  workbox-webpack-plugin@6.6.0(@types/babel__core@7.20.5)(webpack@5.100.0):
    dependencies:
      fast-json-stable-stringify: 2.1.0
      pretty-bytes: 5.6.0
      upath: 1.2.0
      webpack: 5.100.0
      webpack-sources: 1.4.3
      workbox-build: 6.6.0(@types/babel__core@7.20.5)
    transitivePeerDependencies:
      - '@types/babel__core'
      - supports-color

  workbox-window@6.6.0:
    dependencies:
      '@types/trusted-types': 2.0.7
      workbox-core: 6.6.0

  wrap-ansi@7.0.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrap-ansi@8.1.0:
    dependencies:
      ansi-styles: 6.2.1
      string-width: 5.1.2
      strip-ansi: 7.1.0

  wrappy@1.0.2: {}

  write-file-atomic@3.0.3:
    dependencies:
      imurmurhash: 0.1.4
      is-typedarray: 1.0.0
      signal-exit: 3.0.7
      typedarray-to-buffer: 3.1.5

  ws@7.5.10: {}

  ws@8.18.3: {}

  xml-name-validator@3.0.0: {}

  xmlchars@2.2.0: {}

  y18n@5.0.8: {}

  yallist@3.1.1: {}

  yaml@1.10.2: {}

  yaml@2.8.0: {}

  yargs-parser@20.2.9: {}

  yargs@16.2.0:
    dependencies:
      cliui: 7.0.4
      escalade: 3.2.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 20.2.9

  yn@3.1.1: {}

  yocto-queue@0.1.0: {}
