/* eslint-disable @typescript-eslint/no-explicit-any */
import Quill from "quill";
const Inline = Quill.import("blots/inline") as any;

// const BlockEmbed = Quill.import('blots/block/embed') as any;

interface ContactMentionValue {
  id: string;
  name: string;
}

export class ContactMentionBlot extends Inline {
  static blotName = "contactMention";
  static tagName = "div";
  static className = "ql-contact-mention";

  static create(value: ContactMentionValue): HTMLElement {
    const node = super.create() as HTMLElement;
    node.setAttribute("data-id", String(value.id));
    node.setAttribute("data-name", value.name);
    node.setAttribute("contenteditable", "false");
    node.classList.add("ql-contact-mention");
    node.innerText = value.name;
    return node;
  }

  static value(node: HTMLElement): ContactMentionValue {
    return {
      id: node.getAttribute("data-id") ?? "",
      name: node.getAttribute("data-name") ?? ""
    };
  }
}
