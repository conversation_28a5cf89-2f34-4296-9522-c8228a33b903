// FileMentionBlot.ts
import Quill from "quill";

import { FileMentionValue } from "../type/index";

// const Embed = Quill.import("blots/embed") as any;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const Inline = Quill.import("blots/inline") as any;

export class FileMentionBlot extends Inline {
  static blotName = "fileMention";
  static tagName = "div";
  static className = "ql-file-mention";

  static create(value: FileMentionValue): HTMLElement {
    const node = super.create() as HTMLElement;
    console.log("file-blot", value);
    node.setAttribute("data-name", value.name);
    node.setAttribute("contenteditable", "false");
    if (value.fileId) {
      node.setAttribute("data-fileid", value.fileId);
    }
    node.innerHTML = value.name;
    return node;
  }

  static value(node: HTMLElement): FileMentionValue {
    return {
      name: node.getAttribute("data-name") || "",
      fileId: node.getAttribute("data-fileid") || ""
    };
  }
}
