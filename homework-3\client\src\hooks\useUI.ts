import { useAppSelector, useAppDispatch } from "../store/hooks"
import { setActiveTab, setShowAddButton } from "../store"

export const useUI = () => {
  const dispatch = useAppDispatch()
  const activeTab = useAppSelector((state) => state.ui.activeTab)
  const showAddButton = useAppSelector((state) => state.ui.showAddButton)

  // 设置激活的标签页
  const handleSetActiveTab = (tab: string) => {
    dispatch(setActiveTab(tab))
  }

  // 设置显示添加按钮的节点
  const handleSetShowAddButton = (nodeId: string | null) => {
    dispatch(setShowAddButton(nodeId))
  }

  return {
    activeTab,
    showAddButton,
    setActiveTab: handleSetActiveTab,
    setShowAddButton: handleSetShowAddButton
  }
}
