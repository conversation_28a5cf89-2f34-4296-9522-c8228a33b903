import React from 'react'
import { createRoot } from 'react-dom/client'

import TodoApp from './react/TodoApp.jsx'




const renderReact = (rootElement: HTMLElement) => {
  const root = createRoot(rootElement)
  root.render(React.createElement(TodoApp))
  console.log('已加载 React 组件')
}


const appElement = document.getElementById('app')

if (!appElement) {
  throw new Error('找不到 #app 元素')
}

renderReact(appElement)

