import { StrictMode } from "react"
import { createRoot } from "react-dom/client"
import dayjs from "dayjs"
import "dayjs/locale/zh-cn"
import { ConfigProvider } from "antd"
import zhCN from "antd/es/locale/zh_CN"
import App from "./App"

// 设置 dayjs 全局语言
dayjs.locale("zh-cn")

createRoot(document.getElementById("root")).render(
  <ConfigProvider locale={zhCN}>
    <StrictMode>
      <App />
    </StrictMode>
  </ConfigProvider>
)
