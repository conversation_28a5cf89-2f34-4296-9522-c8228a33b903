const { sendAIRequest } = require("../services/aiSimpleService") // 你的AI请求服务

async function streamMindmap(ctx) {
  const prompt = ctx.query.prompt || "示例主题"

  console.log(`🎯 开始处理流式请求: ${prompt}`)

  ctx.set("Content-Type", "text/event-stream")
  ctx.set("Cache-Control", "no-cache")
  ctx.set("Connection", "keep-alive")
  ctx.set("Access-Control-Allow-Origin", "*")
  ctx.set("Access-Control-Allow-Headers", "Content-Type")
  ctx.status = 200

  const res = ctx.res

  // 首先发送用户输入的根节点
  const rootNodeData = JSON.stringify({
    type: "node",
    node: {
      id: "root",
      text: prompt,
      level: 0,
      children: [],
      parentId: null,
    },
  })

  res.write(`data: ${rootNodeData}\n\n`)
  console.log(`📤 发送根节点: ${rootNodeData}`)

  // 强制刷新
  if (res.flush) {
    res.flush()
  }

  await new Promise((resolve) => {
    sendAIRequest(
      prompt,
      (data) => {
        // 确保每个数据包都立即发送
        const jsonData = JSON.stringify(data)
        res.write(`data: ${jsonData}\n\n`)

        // 强制刷新缓冲区
        if (res.flush) {
          res.flush()
        }

        console.log(`📤 发送节点数据: ${jsonData}`)

        if (data.type === "done") {
          console.log("🏁 收到done信号，准备关闭连接")
          // 延迟关闭连接，确保所有数据都发送完毕
          setTimeout(() => {
            res.end()
            resolve()
          }, 200) // 给额外的时间确保所有数据发送完毕
        }
      },
      (err) => {
        res.write(
          `data: ${JSON.stringify({ type: "error", message: err })}\n\n`
        )
        res.end()
        resolve()
      },
      () => {
        console.log("🔚 AI服务onEnd回调被调用，但不立即关闭连接")
        // 不在这里关闭连接，让done消息的处理来关闭
      }
    )
  })
}

module.exports = {
  streamMindmap,
}
