import { createSlice, type PayloadAction } from "@reduxjs/toolkit"
import type { MindMapNode, MindMapState, NodeStyle } from "../types"
import {
  getDefaultNodeName,
  getDefaultNodeStyle,
  recalculateAllPositions
} from "../utils/nodeUtils"
import {
  clearMindMapStorage,
  loadMindMapFromStorage,
  saveMindMapToStorage
} from "../utils/storage"

// 初始化状态
const getInitialState = (): MindMapState => {
  const stored = loadMindMapFromStorage()
  if (stored && stored.root) {
    // 更新现有节点的字体大小以匹配层级
    const updatedNodes = { ...stored }
    Object.values(updatedNodes).forEach((node) => {
      if (node.style) {
        // 根据层级设置字体大小：根节点最大，每层递减1.5px，最小不少于10px
        const fontSize = Math.max(10, 18 - node.level * 1.5)

        node.style.fontSize = fontSize
      }
    })

    // 保存更新后的节点到localStorage
    saveMindMapToStorage(updatedNodes)

    return {
      nodes: updatedNodes,
      selectedNodeId: null
    }
  }

  // 默认根节点
  const defaultNodes = {
    root: {
      id: "root",
      text: "未命名文件(1)",
      x: 500,
      y: 300,
      level: 0,
      children: [],
      style: getDefaultNodeStyle(0) // 根节点
    }
  }

  return {
    nodes: defaultNodes,
    selectedNodeId: null
  }
}

const mindMapSlice = createSlice({
  name: "mindMap",
  initialState: getInitialState(),
  reducers: {
    setNodes: (state, action: PayloadAction<Record<string, MindMapNode>>) => {
      state.nodes = action.payload
      // 自动保存到localStorage
      saveMindMapToStorage(action.payload)
    },

    addChildNode: (state, action: PayloadAction<string>) => {
      const parentId = action.payload
      const parent = state.nodes[parentId]
      if (!parent) return // 移除层级限制

      const newNodeId = `node_${Date.now()}`
      const defaultName = getDefaultNodeName(parent.level)
      const newNode: MindMapNode = {
        id: newNodeId,
        text: defaultName,
        x: parent.x + 200, // 临时位置，会被重新计算
        y: parent.y,
        level: parent.level + 1,
        parentId: parentId,
        children: [],
        style: getDefaultNodeStyle(parent.level + 1) // 子节点层级
      }

      // 添加新节点
      state.nodes[newNodeId] = newNode

      // 更新父节点的children
      state.nodes[parentId].children.push(newNodeId)

      // 重新计算所有节点位置
      state.nodes = recalculateAllPositions(state.nodes)

      // 自动选中新添加的节点
      state.selectedNodeId = newNodeId

      // 自动保存到localStorage
      saveMindMapToStorage(state.nodes)
    },

    addSiblingNode: (state, action: PayloadAction<string>) => {
      const nodeId = action.payload
      const node = state.nodes[nodeId]
      if (!node || !node.parentId) return // 根节点没有同级

      const parent = state.nodes[node.parentId]
      if (!parent) return

      const newNodeId = `node_${Date.now()}`
      const defaultName = getDefaultNodeName(parent.level)
      const newNode: MindMapNode = {
        id: newNodeId,
        text: defaultName,
        x: parent.x + 200, // 临时位置，会被重新计算
        y: parent.y,
        level: node.level,
        parentId: node.parentId,
        children: [],
        style: getDefaultNodeStyle(node.level) // 同级节点
      }

      // 添加新节点
      state.nodes[newNodeId] = newNode

      // 更新父节点的children
      state.nodes[node.parentId!].children.push(newNodeId)

      // 重新计算所有节点位置
      state.nodes = recalculateAllPositions(state.nodes)

      // 自动选中新添加的节点
      state.selectedNodeId = newNodeId

      // 自动保存到localStorage
      saveMindMapToStorage(state.nodes)
    },

    addParentNode: (state, action: PayloadAction<string>) => {
      const nodeId = action.payload
      const node = state.nodes[nodeId]
      if (!node || node.id === "root") return // 根节点不能添加父节点

      const newNodeId = `node_${Date.now()}`

      // 新父节点的位置在当前节点和其父节点之间
      let newX = node.x
      let newY = node.y - 100 // 向上偏移

      if (node.parentId) {
        const grandParent = state.nodes[node.parentId]
        newX = (node.x + grandParent.x) / 2
        newY = (node.y + grandParent.y) / 2
      }

      const newNode: MindMapNode = {
        id: newNodeId,
        text: "父主题",
        x: newX,
        y: newY,
        level: Math.max(0, node.level - 1),
        parentId: node.parentId,
        children: [nodeId],
        style: getDefaultNodeStyle(Math.max(0, node.level - 1)) // 父节点层级
      }

      // 添加新父节点
      state.nodes[newNodeId] = newNode

      // 更新当前节点的父节点
      state.nodes[nodeId] = {
        ...state.nodes[nodeId],
        parentId: newNodeId,
        level: newNode.level + 1
      }

      // 如果当前节点有原父节点，更新原父节点的children
      if (node.parentId) {
        state.nodes[node.parentId].children = state.nodes[
          node.parentId
        ].children.map((childId) => (childId === nodeId ? newNodeId : childId))
      }

      // 重新计算所有节点位置
      state.nodes = recalculateAllPositions(state.nodes)

      // 自动选中新添加的节点
      state.selectedNodeId = newNodeId

      // 自动保存到localStorage
      saveMindMapToStorage(state.nodes)
    },

    deleteNode: (state, action: PayloadAction<string>) => {
      const nodeId = action.payload
      const node = state.nodes[nodeId]

      if (!node || nodeId === "root") return

      // 从父节点的children中移除
      if (node.parentId && state.nodes[node.parentId]) {
        state.nodes[node.parentId].children = state.nodes[
          node.parentId
        ].children.filter((id) => id !== nodeId)
      }

      // 递归删除所有子节点
      const deleteNodeRecursive = (id: string) => {
        const nodeToDelete = state.nodes[id]
        if (nodeToDelete) {
          nodeToDelete.children.forEach((childId) =>
            deleteNodeRecursive(childId)
          )
          delete state.nodes[id]
        }
      }

      deleteNodeRecursive(nodeId)

      // 如果删除的是选中节点，清除选中状态
      if (state.selectedNodeId === nodeId) {
        state.selectedNodeId = null
      }

      // 自动保存到localStorage
      saveMindMapToStorage(state.nodes)
    },

    deleteNodeWithChildren: (state, action: PayloadAction<string>) => {
      const nodeId = action.payload
      if (nodeId === "root") return // 不能删除根节点

      const nodeToDelete = state.nodes[nodeId]
      if (!nodeToDelete) return

      // 如果有子节点，删除当前节点及所有子节点
      // 如果没有子节点，只删除当前节点
      if (nodeToDelete.children.length > 0) {
        // 有子节点：删除当前节点及所有子节点 - 使用deleteNode逻辑
        const deleteNodeRecursive = (id: string) => {
          const node = state.nodes[id]
          if (node) {
            node.children.forEach((childId) => deleteNodeRecursive(childId))
            delete state.nodes[id]
          }
        }

        // 从父节点的children中移除
        if (nodeToDelete.parentId && state.nodes[nodeToDelete.parentId]) {
          state.nodes[nodeToDelete.parentId].children = state.nodes[
            nodeToDelete.parentId
          ].children.filter((id) => id !== nodeId)
        }

        deleteNodeRecursive(nodeId)
      } else {
        // 没有子节点：只删除当前节点
        delete state.nodes[nodeId]

        // 从父节点的children中移除
        if (nodeToDelete.parentId && state.nodes[nodeToDelete.parentId]) {
          state.nodes[nodeToDelete.parentId].children = state.nodes[
            nodeToDelete.parentId
          ].children.filter((childId) => childId !== nodeId)
        }
      }

      // 如果删除的是当前选中的节点，清除选中状态
      if (state.selectedNodeId === nodeId) {
        state.selectedNodeId = null
      }

      // 自动保存到localStorage
      saveMindMapToStorage(state.nodes)
    },

    // 直接创建节点（用于AI生成）
    createNodeDirectly: (
      state,
      action: PayloadAction<{
        id: string
        text: string
        level: number
        parentId: string
      }>
    ) => {
      const { id, text, level, parentId } = action.payload

      // 检查父节点是否存在
      const parent = state.nodes[parentId]
      if (!parent) return

      // 检查节点是否已存在
      if (state.nodes[id]) return

      const newNode: MindMapNode = {
        id,
        text: text.trim(),
        x: parent.x + 200, // 临时位置，会被重新计算
        y: parent.y,
        level,
        parentId,
        children: [],
        style: getDefaultNodeStyle(level) // 指定层级
      }

      // 添加新节点
      state.nodes[id] = newNode

      // 更新父节点的children
      state.nodes[parentId].children.push(id)

      // 重新计算所有节点位置
      state.nodes = recalculateAllPositions(state.nodes)

      // 自动保存到localStorage
      saveMindMapToStorage(state.nodes)
    },

    // 快速创建节点（用于AI流式生成，不立即重新计算布局）
    createNodeFast: (
      state,
      action: PayloadAction<{
        id: string
        text: string
        level: number
        parentId: string
      }>
    ) => {
      const { id, text, level, parentId } = action.payload

      // 检查父节点是否存在
      const parent = state.nodes[parentId]
      if (!parent) return

      // 检查节点是否已存在
      if (state.nodes[id]) return

      const newNode: MindMapNode = {
        id,
        text: text.trim(),
        x: parent.x + 200, // 临时位置，会被重新计算
        y: parent.y,
        level,
        parentId,
        children: [],
        style: getDefaultNodeStyle(level) // 指定层级
      }

      // 添加新节点
      state.nodes[id] = newNode

      // 更新父节点的children
      state.nodes[parentId].children.push(id)

      // 不立即重新计算布局，由外部控制
      // 自动保存到localStorage
      saveMindMapToStorage(state.nodes)
    },

    updateNode: (
      state,
      action: PayloadAction<{
        nodeId: string
        text?: string
        style?: NodeStyle
      }>
    ) => {
      const { nodeId, text, style } = action.payload
      if (state.nodes[nodeId]) {
        if (text !== undefined) {
          state.nodes[nodeId].text = text.trim()
        }
        if (style) {
          state.nodes[nodeId].style = { ...style }
        }

        // 自动保存到localStorage
        saveMindMapToStorage(state.nodes)
      }
    },

    setSelectedNode: (state, action: PayloadAction<string | null>) => {
      state.selectedNodeId = action.payload
    },

    // 重新计算布局（在DOM更新后调用）
    recalculateLayout: (state) => {
      state.nodes = recalculateAllPositions(state.nodes)
      // 自动保存到localStorage
      saveMindMapToStorage(state.nodes)
    },

    resetMindMap: (state) => {
      const defaultNodes = {
        root: {
          id: "root",
          text: "未命名文件(1)",
          x: 500,
          y: 300,
          level: 0,
          children: [],
          style: getDefaultNodeStyle(0) // 根节点
        }
      }
      state.nodes = defaultNodes
      state.selectedNodeId = null
      // 清除localStorage
      clearMindMapStorage()
    },

    // 更新所有节点的字体大小以匹配层级
    updateAllNodeFontSizes: (state) => {
      Object.values(state.nodes).forEach((node) => {
        if (node.style) {
          // 根据层级设置字体大小：根节点最大，每层递减1.5px，最小不少于10px
          const fontSize = Math.max(10, 18 - node.level * 1.5)

          node.style.fontSize = fontSize
        }
      })
      // 保存到localStorage
      saveMindMapToStorage(state.nodes)
    },

    importMindMap: (
      state,
      action: PayloadAction<Record<string, MindMapNode>>
    ) => {
      const importedNodes = action.payload
      // 验证导入的数据格式
      if (
        importedNodes &&
        typeof importedNodes === "object" &&
        importedNodes.root
      ) {
        state.nodes = importedNodes
        state.selectedNodeId = null
        // 保存到localStorage
        saveMindMapToStorage(importedNodes)
      }
    },

    // 切换节点折叠状态
    toggleNodeCollapse: (state, action: PayloadAction<string>) => {
      const nodeId = action.payload
      const node = state.nodes[nodeId]
      if (
        node &&
        node.id !== "root" &&
        node.children &&
        node.children.length > 0
      ) {
        // 切换折叠状态
        node.collapsed = !node.collapsed

        // 重新计算所有节点位置
        state.nodes = recalculateAllPositions(state.nodes)

        // 自动保存到localStorage
        saveMindMapToStorage(state.nodes)
      }
    }
  }
})

export const {
  setNodes,
  addChildNode,
  addSiblingNode,
  addParentNode,
  deleteNode,
  deleteNodeWithChildren,
  createNodeDirectly,
  createNodeFast,
  updateNode,
  setSelectedNode,
  recalculateLayout,
  resetMindMap,
  updateAllNodeFontSizes,
  importMindMap,
  toggleNodeCollapse
} = mindMapSlice.actions

export default mindMapSlice.reducer
