/**
 * 设置SSE响应头
 * @param {Object} ctx - Koa上下文
 */
function setSSEHeaders(ctx) {
  ctx.set("Content-Type", "text/event-stream")
  ctx.set("Cache-Control", "no-cache")
  ctx.set("Connection", "keep-alive")
  ctx.set("Transfer-Encoding", "chunked")
  ctx.set("X-Accel-Buffering", "no") // Nginx 关闭缓冲
  ctx.set("Access-Control-Allow-Origin", "*")
  ctx.set("Access-Control-Allow-Headers", "Content-Type")
  ctx.set("Access-Control-Allow-Methods", "POST, GET, OPTIONS")
  ctx.set("Access-Control-Allow-Credentials", "true")
}

/**
 * 安全写入，避免断连时报错
 * @param {Object} ctx - Koa上下文
 * @param {string} str - 写入内容
 */
function safeWrite(ctx, str) {
  if (ctx.res.writableEnded || ctx.res.destroyed) return
  try {
    ctx.res.write(str)
    if (ctx.res.flush) {
      ctx.res.flush()
    }
  } catch (err) {
    console.error("❌ SSE写入失败:", err)
  }
}

/**
 * 安全关闭响应，只关闭一次
 * @param {Object} ctx - Koa上下文
 */
function safeEnd(ctx) {
  if (!ctx.__sseClosed && ctx.res.writableEnded === false) {
    ctx.__sseClosed = true
    ctx.res.end()
  }
}

/**
 * 发送SSE数据（默认事件）
 * @param {Object} ctx
 * @param {Object} data
 */
function sendSSEData(ctx, data) {
  safeWrite(ctx, `data: ${JSON.stringify(data)}\n\n`)
}

/**
 * 发送命名事件（支持可选id与重连时间）
 * @param {Object} ctx
 * @param {string} event
 * @param {Object} data
 * @param {string} [id]
 * @param {number} [retry]
 */
function sendSSEEvent(ctx, event, data, id, retry) {
  if (id) safeWrite(ctx, `id: ${id}\n`)
  if (retry) safeWrite(ctx, `retry: ${retry}\n`)
  safeWrite(ctx, `event: ${event}\n`)
  safeWrite(ctx, `data: ${JSON.stringify(data)}\n\n`)
}

/**
 * 发送AI内容块（content 类型）
 * @param {Object} ctx
 * @param {string} content
 */
function sendContent(ctx, content) {
  sendSSEData(ctx, {
    type: "content",
    content: content,
  })
}

/**
 * 发送完成信号
 * @param {Object} ctx
 */
function sendDone(ctx) {
  sendSSEData(ctx, { type: "done" })
  safeEnd(ctx)
}

/**
 * 发送错误信息
 * @param {Object} ctx
 * @param {string} message
 */
function sendError(ctx, message) {
  sendSSEData(ctx, {
    type: "error",
    message: message,
  })
  safeEnd(ctx)
}

module.exports = {
  setSSEHeaders,
  sendSSEData,
  sendSSEEvent,
  sendContent,
  sendDone,
  sendError,
  safeEnd,
}
