.users-list {
  padding: 5px;
  max-width: 400px;
  margin: 0 auto;
  max-height: 300px;
  overflow-y: auto;
  overflow-x: hidden;
}

.users-list-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px;
}

.back-button {
  background: none;
  border: none;
  color: #4285f4;
  cursor: pointer;
  font-size: 14px;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.back-button:hover {
  background-color: #f0f4ff;
}

.users-list-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.user_login {
  text-align: center;
  width: 250px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.user_login button {
  max-width: 360px;
  padding: 12px 0;
  background-color: #2e81fc;
  color: white;
  font-size: 16px;
  font-weight: bold;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.user_login button:hover {
  background-color: #1a73e8;
}

.selection-status {
  font-size: 14px;
  color: #888e95;
  max-width: 400px;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
  line-height: 1.4;
  padding: 2px;
}

.user_login_title .nav_title {
  font-size: 18px;
}

.users-list::-webkit-scrollbar {
  width: 6px;
}

.users-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.users-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.users-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}