import { fireEvent, render, screen } from "@testing-library/react"
import { describe, expect, it, vi } from "vitest"
import "vitest-dom/extend-expect"
// @ts-expect-error: jsx文件无类型声明
import TodoStats from "../components/TodoStats"
import React from "react"


describe("TodoStats 组件", () => {
  const stats = {
    total: 10,
    active: 3,
    completed: 7,
    percentage: 70,
  }

  // 1. 正常渲染统计信息
  it("正常渲染统计信息", () => {
    render(<TodoStats stats={stats} onClearCompleted={() => {}} />)
    expect(screen.getByText("总计: 10")).toBeInTheDocument()
    expect(screen.getByText("进行中: 3")).toBeInTheDocument()
    expect(screen.getByText("已完成: 7")).toBeInTheDocument()
    expect(screen.getByText("完成率: 70%")).toBeInTheDocument()
  })

  // 2. stats为null时显示默认值
  it("stats为null时显示默认值", () => {
    render(<TodoStats stats={null} onClearCompleted={() => {}} />)
    expect(screen.getByText("总计: 0")).toBeInTheDocument()
    expect(screen.getByText("进行中: 0")).toBeInTheDocument()
    expect(screen.getByText("已完成: 0")).toBeInTheDocument()
    // 完成率不显示
    expect(screen.queryByText(/完成率/)).not.toBeInTheDocument()
  })

  // 3. 完成率仅在total>0时显示
  it("完成率仅在total>0时显示", () => {
    render(
      <TodoStats stats={{ ...stats, total: 0 }} onClearCompleted={() => {}} />
    )
    expect(screen.queryByText(/完成率/)).not.toBeInTheDocument()
    render(
      <TodoStats stats={{ ...stats, total: 5 }} onClearCompleted={() => {}} />
    )
    expect(screen.getByText(/完成率/)).toBeInTheDocument()
  })

  // 4. 清除已完成按钮可用/禁用状态
  it("清除已完成按钮在有已完成时可用，无已完成时禁用", () => {
    const { rerender } = render(
      <TodoStats stats={stats} onClearCompleted={() => {}} />
    )
    const btn = screen.getByRole("button", { name: "清除已完成" })
    expect(btn).not.toBeDisabled()
    rerender(
      <TodoStats
        stats={{ ...stats, completed: 0 }}
        onClearCompleted={() => {}}
      />
    )
    expect(btn).toBeDisabled()
  })

  // 5. 点击清除已完成按钮触发onClearCompleted
  it("点击清除已完成按钮触发onClearCompleted", () => {
    const onClearCompleted = vi.fn()
    render(<TodoStats stats={stats} onClearCompleted={onClearCompleted} />)
    const btn = screen.getByRole("button", { name: "清除已完成" })
    fireEvent.click(btn)
    expect(onClearCompleted).toHaveBeenCalled()
  })
})
