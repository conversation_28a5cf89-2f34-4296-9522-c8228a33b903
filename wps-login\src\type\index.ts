export interface VerifyType {
  result: string;
  ssid?: string;
  msg?: string;
}

export interface ReasonV2 {
  key: string;
  translate: boolean;
}

export interface LinkTextV2 {
  key: string;
  translate: boolean;
}

export interface User {
  userid: number;
  nickname: string;
  company_id: number;
  company_name: string;
  company_logo: string;
  company_custom_domain: string;
  is_company_account: boolean;
  avatar_url: string;
  status: number;
  reason: string;
  reason_v2: ReasonV2;
  link_text: string;
  link_text_v2: LinkTextV2;
  link_url: string;
  is_current: boolean;
  is_login: boolean;
  loginmode: string;
  session_status: number;
  logout_reason: string;
  need_tfa: boolean;
  default_select: boolean;
  last_active_time: number;
}

export interface UsersData {
  auth_type: string;
  email: string;
  in_plus_white_list: boolean;
  is_all_login: boolean;
  is_email_register: boolean;
  is_second_phone: boolean;
  login_account_num_limit: number;
  need_active: boolean;
  need_set_password: boolean;
  result: string;
  select_rule: string;
  userid: number;
  users: User[];
  uzone: string;
}
