/**
 * 组件可扩展设计练习：优化图片上传组件
 * 这里有3个图片上传组件，分别是：背景图片上传组件、封面图片上传组件、logo图片上传组件；
 * 这些组件存在一些异同点，比如：背景上传和封面上传UI样式相似，背景、Logo上传都用了同一个接口等；
 * 请根据这些异同点，结合组件可扩展设计原则，优化这些组件，使其更易于维护和扩展
 */

import { memo } from "react";
import BgUploader from "./BgUploader";
import CoverUploader from "./CoverUploader";
import LogoUploader from "./LogoUploader";

const ComponentPractice = () => {
    return <div>
        <BgUploader />
        <CoverUploader />
        <LogoUploader />
    </div>
}

export default memo(ComponentPractice);