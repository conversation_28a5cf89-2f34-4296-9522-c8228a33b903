server {
  listen 80;
  server_name localhost;

  # 主页
  location / {
    root /usr/share/nginx/html/main;
    index index.html;
    try_files $uri $uri/ /index.html;
  }

  # Vue 子应用
  location /vue/ {
    alias /usr/share/nginx/html/vue/;
    index index.html;
    try_files $uri $uri/ index.html;
  }

  location = /vue {
    return 301 /vue/;
  }
  # React 子应用
  location /react/ {
    root /usr/share/nginx/html;
    index index.html;
    try_files $uri $uri/ /react/index.html;
  }

}
