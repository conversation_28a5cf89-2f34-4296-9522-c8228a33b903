import type { ComponentType } from "react"

// 基础操作项接口
export interface ActionItem {
  id: string
  label: string
  onClick: () => void
  disabled?: boolean
}

// 图标操作项接口（继承基础操作项）
export interface IconActionItem extends ActionItem {
  icon: ComponentType<any> | string
}

// 标签页配置接口
export interface TabItem {
  id: string
  label: string
}

// 文件信息接口
export interface FileInfo {
  name: string
  location: string
  isStarred?: boolean
}

export interface HeaderProps {
  activeTab: string
  onTabChange: (tab: string) => void
}

export interface TabNavigationProps {
  activeTab: string
  onTabChange: (tab: string) => void
}

export interface FileControlsProps {
  fileName?: string
  onFileNameChange?: (name: string) => void
}
