<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <title>flex</title>
    <link rel="stylesheet" href="style.css" />
  </head>
  <style>
    .container {
      display: flex;
      flex-direction: column;
      height: 100vh;
    }
    .header {
      background-color: orange;
      width: 100%;
      height: 10%;
    }
    .content {
      display: flex;
      flex: 1;
    }
    .content .content-item-left {
      background: red;
      width: 10%;
      height: 100%;
    }
    .content .content-item-right {
      background: rgb(36, 162, 116);
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
    }
    .content .content-item-right .nav {
      background: green;
      width: 100%;
      height: 10%;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .content .content-item-right .nav .nav-item {
      display: flex;
      align-items: center;
    }
    .content .content-item-right .main {
      background: pink;
      width: 100%;
      height: 90%;
    }
    .footer {
      background-color: blue;
      width: 100%;
      height: 10%;
    }
  </style>
  <body>
    <div class="container">
      <div class="header"></div>
      <div class="content">
        <div class="content-item-left"></div>
        <div class="content-item-right">
          <div class="nav">
            <div class="nav-item">
              <div class="nav-opr">笔试名称</div>
              <div class="nav-opr">技术方向</div>
            </div>

            <div class="nav-item">
              <button>操作1</button>
              <button>操作2</button>
            </div>
          </div>
          <div class="main"></div>
        </div>
      </div>
      <div class="footer"></div>
    </div>
  </body>
</html>
