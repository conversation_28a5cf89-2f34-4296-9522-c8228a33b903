<script setup lang="ts">
import subTitle from '@/components/SubTitle.vue'
import { useMoney } from '@/hooks/useMoney'

import ShowItem from './components/ShowItem.vue'
const { moneyList, total_pay, total_income, summary_money } = useMoney()
</script>
<template>
  <div class="show-box">
    <subTitle title="本月账单明细" />
    <div class="show-list">
      <ShowItem v-for="(item, index) in moneyList" :key="index" :index="index" :item="item" />
    </div>
    <div v-if="moneyList.length > 0" class="show-footer">
      <div>总支出：{{ total_pay > 0 ? `-${total_pay}` : `${total_pay}` }}</div>
      <div>总收入：{{ total_income }}</div>
      <div>汇总：{{ summary_money }}</div>
    </div>
    <div v-else class="show-info">
      <p>还没有账单哦</p>
      <p>快去添加你的账单吧</p>
    </div>
  </div>
</template>

<style scoped>
.show-box {
  display: flex;
  flex-direction: column;
  margin-top: 2em;
}

.show-list {
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  font-size: 13px;
  text-align: center;
}
.show-footer {
  margin-top: 40px;
}
.show-info {
  text-align: center;
  font-size: 14px;
  color: #c1c1c1;
  margin-top: 40px;
  width: 100%;
}
</style>
