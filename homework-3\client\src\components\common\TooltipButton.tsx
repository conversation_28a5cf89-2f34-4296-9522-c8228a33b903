import * as Toolbar from "@radix-ui/react-toolbar"
import * as <PERSON><PERSON><PERSON> from "@radix-ui/react-tooltip"
import React, { createContext, useContext } from "react"

interface TooltipButtonProps {
  label: string
  onClick?: () => void
  disabled?: boolean
  active?: boolean
  className?: string
  children: React.ReactNode
}

// 自定义 Context，默认 false
const ToolbarContext = createContext(false)

export const ToolbarRoot: React.FC<
  React.ComponentProps<typeof Toolbar.Root>
> = (props) => {
  return (
    <ToolbarContext.Provider value={true}>
      <Toolbar.Root {...props} />
    </ToolbarContext.Provider>
  )
}

export const TooltipButton: React.FC<TooltipButtonProps> = ({
  label,
  onClick,
  disabled,
  active,
  className = "",
  children
}) => {
  // 通过自定义Context判断是否处于 Toolbar 环境中
  const isInToolbar = useContext(ToolbarContext)

  const ButtonComponent: React.ElementType = isInToolbar
    ? Toolbar.Button
    : "button"

  return (
    <Tooltip.Provider delayDuration={200}>
      <Tooltip.Root>
        <Tooltip.Trigger asChild>
          <ButtonComponent
            type="button"
            onClick={(e: React.MouseEvent) => {
              e.stopPropagation()
              if (!disabled && onClick) onClick()
            }}
            disabled={disabled}
            data-state={active ? "on" : "off"}
            className={`toolbar-icon-btn ${active ? "active" : ""} ${
              disabled ? "disabled" : ""
            } ${className}`}
          >
            {children}
          </ButtonComponent>
        </Tooltip.Trigger>
        <Tooltip.Content
          className="tooltip-content"
          side="bottom"
          align="center"
        >
          {label}
          <Tooltip.Arrow className="tooltip-arrow" />
        </Tooltip.Content>
      </Tooltip.Root>
    </Tooltip.Provider>
  )
}
