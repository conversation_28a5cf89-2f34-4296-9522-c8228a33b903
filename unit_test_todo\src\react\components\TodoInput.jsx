import React, { useRef, useState } from "react"
import { validateTodoText } from "../utils/todoUtils.js"
import "./TodoInput.css"

const TodoInput = React.forwardRef(
  (
    {
      placeholder = "Add a new todo...",
      buttonText = "Add",
      maxLength = 200,
      onSubmit,
      onCancel,
    },
    ref
  ) => {
    const [inputValue, setInputValue] = useState("")
    const inputRef = useRef(null)

    const isValid = validateTodoText(inputValue)

    const handleSubmit = () => {
      if (isValid) {
        onSubmit(inputValue.trim())
        setInputValue("")
      }
    }

    const handleCancel = () => {
      setInputValue("")
      onCancel?.()
    }

    const handleKeyDown = (e) => {
      if (e.key === "Enter") {
        handleSubmit()
      } else if (e.key === "Escape") {
        handleCancel()
      }
    }

    // 暴露方法给父组件
    React.useImperativeHandle(
      ref,
      () => ({
        focus: () => {
          inputRef.current?.focus()
        },
        clear: () => {
          setInputValue("")
        },
      }),
      []
    )

    return (
      <div className="todo-input">
        <input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          maxLength={maxLength}
          className="todo-input-field"
        />
        <button
          onClick={handleSubmit}
          disabled={!isValid}
          className="todo-input-button"
        >
          {buttonText}
        </button>
      </div>
    )
  }
)

export default TodoInput
