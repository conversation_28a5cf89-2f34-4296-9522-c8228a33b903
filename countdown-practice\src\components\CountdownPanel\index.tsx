import {
  BellFilled,
  BellOutlined,
  ClockCircleOutlined,
  CloseOutlined,
} from '@ant-design/icons'
import React from 'react'
import { formatTime, formatTitle } from '../../utils/time'
import './style.css'
// CountdownPanel/index.tsx
export interface CountdownPanelProps {
  leftSeconds: number
  visible: boolean
  ring: boolean
  ended: boolean
  onClose?: () => void
  onToggleRing?: (ring: boolean) => void
  style?: React.CSSProperties
  myChildrenClass?: string
  children?: React.ReactNode // 在倒计时面板底部插入自定义内容
}

const CountdownPanel: React.FC<CountdownPanelProps> = ({
  leftSeconds,
  visible,
  ring,
  ended,
  onClose,
  onToggleRing,
  style,
  myChildrenClass = '',
  children,
}) => {
  if (!visible) return null
  const [h, m, s] = formatTime(leftSeconds)
  return (
    <div className="countdown-panel" style={style}>
      <div className="countdown-panel-header">
        <div className="countdown-panel-title">{formatTitle(leftSeconds)}</div>
        <div className="countdown-panel-actions">
          <span
            className={`countdown-panel-action-btn${
              ended && ring ? ' bell-shake' : ''
            }`}
            onClick={() => onToggleRing && onToggleRing(!ring)}
          >
            {ring ? (
              <BellFilled style={{ color: '#222' }} />
            ) : (
              <BellOutlined style={{ color: '#aaa' }} />
            )}
          </span>
          <span
            className="countdown-panel-action-btn close"
            title="关闭"
            onClick={onClose}
          >
            <CloseOutlined />
          </span>
        </div>
      </div>
      {ended ? (
        <div className="countdown-panel-ended">
          <div className="countdown-panel-clock">
            <span className={ring ? 'clock-bounce' : ''}>
              <ClockCircleOutlined />
            </span>
          </div>
          <div className="countdown-panel-ended-text">
            <span className="ok-time"></span>
            时间到~
          </div>
        </div>
      ) : (
        <div className="countdown-panel-digits">
          <div className="countdown-panel-digit">{h}</div>
          <span className="countdown-panel-colon">:</span>
          <div className="countdown-panel-digit">{m}</div>
          <span className="countdown-panel-colon">:</span>
          <div className="countdown-panel-digit">{s}</div>
        </div>
      )}
      {children && <div className={myChildrenClass}>{children}</div>}
    </div>
  )
}

export default CountdownPanel
