import { useEffect, useState } from "react"

type StorageType = "local" | "session"

interface QuestionState {
  questionId: string
  questionTitle: string
  selectedOption: string | null
}

/**
 * Hook：管理单题状态并持久化
 * @param key 存储 key
 * @param questionId 题目 ID
 * @param questionTitle 题目标题
 * @param storageType localStorage 或 sessionStorage
 */
export function useQuestionState(
  key: string,
  questionId: string,
  questionTitle: string,
  storageType: StorageType = "local"
): [QuestionState, (option: string | null) => void] {
  const storage = storageType === "local" ? localStorage : sessionStorage

  const [state, setState] = useState<QuestionState>(() => {
    try {
      const item = storage.getItem(key)
      return item
        ? (JSON.parse(item) as QuestionState)
        : { questionId, questionTitle, selectedOption: null }
    } catch {
      return { questionId, questionTitle, selectedOption: null }
    }
  })

  useEffect(() => {
    storage.setItem(key, JSON.stringify(state))
  }, [key, state, storage])

  const updateOption = (option: string | null) => {
    setState({ questionId, questionTitle, selectedOption: option })
  }

  return [state, updateOption]
}
