import React, { useState } from "react";
import "./index.css";

interface UserItemProps {
  avatarUrl: string;
  nickname: string;
  companyName: string;
  isCompanyAccount: boolean;
  isCurrent: boolean;
  isLogin: boolean;
  onSelect?: (isSelected: boolean) => void;
}

const UserItem: React.FC<UserItemProps> = ({
  avatarUrl,
  nickname,
  companyName,
  isCompanyAccount,
  isCurrent,
  isLogin,
  onSelect
}) => {
  const [isSelected, setIsSelected] = useState(false);

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const checked = e.target.checked;
    setIsSelected(checked);
    onSelect?.(checked);
  };

  return (
    <div className={`account-card ${isCurrent ? "selected" : ""}`}>
      <div className="avatar-container">
        <img src={avatarUrl} alt="avatar" className="avatar" />
        {isCompanyAccount && <span className="badge">企</span>}
      </div>
      <div className="account-info">
        <div className="nickname">{nickname}</div>
        <div className="company-name">{companyName}</div>
      </div>
      {onSelect && (
        <input
          type="checkbox"
          className="user-checkbox"
          checked={isSelected}
          onChange={handleCheckboxChange}
        />
      )}
      {isLogin && <span className="checkmark">✔</span>}
    </div>
  );
};

export default UserItem;
