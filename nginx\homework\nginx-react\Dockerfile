# 第一阶段：构建React应用
FROM node:22-alpine as builder

# 设置工作目录
WORKDIR /app

# 首先复制package.json和package-lock.json
# 这样只有这些文件变化时才会重新安装依赖
COPY package.json package-lock.json ./

# 安装依赖
RUN npm install

# 复制其他源代码文件
# 这样源代码变化不会触发重新安装依赖
COPY . .

# 构建应用
# 设置为生产环境
ENV NODE_ENV=production
RUN npm run build

# 第二阶段：配置nginx服务
FROM nginx:alpine


# 复制nginx配置文件
COPY nginx.conf /etc/nginx/conf.d/default.conf
COPY --from=builder /app/dist/. /app/dist/
# 从builder阶段复制构建产物
# 只复制需要的dist目录，避免复制其他文件
COPY --from=builder /app/dist /usr/share/nginx/html

#本地要设置user为root才能成功运行容器
# 这是一个安全性最佳实践
USER root

# 暴露80端口
EXPOSE 80

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]