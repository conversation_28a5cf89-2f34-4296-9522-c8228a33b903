
import { storeToRefs } from 'pinia'
import { useMoneyStore } from '@/stores'

export function useMoney() {
  const moneyStore = useMoneyStore()
  const { category, tag, money, pay_list, collect_list, moneyList, activateTag, total_pay, total_income, summary_money } = storeToRefs(moneyStore)
  const {
    addMoney,
    delMoney
  } = moneyStore

  return {
    summary_money, total_pay, total_income, category, tag, money, addMoney, pay_list, collect_list, moneyList, delMoney, activateTag
  }
}
