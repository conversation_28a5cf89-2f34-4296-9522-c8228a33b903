const koa = require("koa")
const app = new koa()
const routes = require("./routers")
const { koaBody } = require("koa-body")
const serve = require("koa-static")
const path = require("path")

// CORS中间件
app.use(async (ctx, next) => {
  ctx.set("Access-Control-Allow-Origin", "*")
  ctx.set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
  ctx.set(
    "Access-Control-Allow-Headers",
    "Content-Type, Authorization, Cache-Control"
  )

  if (ctx.method === "OPTIONS") {
    ctx.status = 200
    return
  }

  await next()
})

app.use(koaBody())

// 静态文件服务
app.use(serve(path.join(__dirname, "public")))

// 加载数据路由
app.use(routes.routes())

// 添加错误处理中间件
app.use(async (ctx, next) => {
  try {
    await next()
  } catch (err) {
    console.error("❌ 服务器错误:", err)
    ctx.status = err.status || 500
    ctx.body = {
      error: err.message,
      stack: process.env.NODE_ENV === "development" ? err.stack : undefined,
    }
  }
})

// 404处理
app.use(async (ctx) => {
  console.log(`❌ 404 - 未找到路由: ${ctx.method} ${ctx.url}`)
  ctx.status = 404
  ctx.body = {
    error: "Requested resource could not be found",
    path: ctx.url,
    method: ctx.method,
  }
})

//启动应用程序
const PORT = process.env.PORT || 3001
app.listen(PORT, () => {
  console.log(`🚀 Server is running on port ${PORT}`)
})
