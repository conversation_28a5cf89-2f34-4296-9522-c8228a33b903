const path = require("path");
const HtmlWebpackPlugin = require("html-webpack-plugin");
const { CleanWebpackPlugin } = require("clean-webpack-plugin");

module.exports = {
  mode: "development",
  entry: "./src/index.ts",
  output: {
    filename: "bundle.js",
    path: path.resolve(__dirname, "dist")
    // publicPath: '/zhouxinyi/dev/quilljs/',
  },
  devtool: "inline-source-map",
  devServer: {
    host: "0.0.0.0",
    port: 3000,
    compress: true,
    hot: true,
    allowedHosts: ["woa.wps.cn"],
    historyApiFallback: {
      index: "/zhouxinyi/dev/quilljs/index.html"
    },
    client: {
      overlay: {
        errors: true,
        warnings: false
      }
    }
  },
  module: {
    rules: [
      {
        test: /\.ts$/,
        exclude: /node_modules/,
        use: {
          loader: "ts-loader"
        }
      },
      {
        test: /\.css$/,
        use: ["style-loader", "css-loader"]
      },
      {
        test: /\.(png|svg|jpg|jpeg|gif)$/i,
        type: "asset/resource"
      },
      {
        test: /\.(woff|woff2|eot|ttf|otf)$/i,
        type: "asset/resource"
      }
    ]
  },
  plugins: [
    new CleanWebpackPlugin(),
    new HtmlWebpackPlugin({
      template: "./src/index.html",
      filename: "index.html",
      minify: false
    })
  ],
  resolve: {
    alias: {
      quill$: path.resolve(__dirname, "node_modules/quill/quill.js")
    },
    extensions: [".ts", ".js"] // 自动解析扩展名
  },
  optimization: {
    minimize: false
  }
};
