<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <title>flex</title>
    <link rel="stylesheet" href="style.css" />
  </head>
  <style>
    .container {
      width: 100%;
      height: auto;
    }
    .container .content {
      width: 100%;
      height: 100px;
      background-color: #f0f0f0;
      border: solid 1px black;
    }
    .container .footer-flex-container {
      position: fixed;
      bottom: 0;
      display: flex;
      width: 100%;
      height: 50px;
    }
    .container .footer-flex-container .flex-item {
      flex: 1;
      text-align: center;
      line-height: 50px;
      border: solid 1px black;
    }
  </style>
  <body>
    <div class="container">
      <div class="content">内容展示区域</div>
      <div class="footer-flex-container">
        <div class="flex-item">微信</div>
        <div class="flex-item">文件</div>
        <div class="flex-item">消失</div>
        <div class="flex-item">我</div>
      </div>
    </div>
  </body>
</html>
