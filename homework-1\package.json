{"name": "example-esm", "version": "1.0.0", "main": "src/index.js", "scripts": {"build": "webpack --mode development", "start": "http-server ./dist", "dev": "webpack-dev-server --config webpack.config.js", "lint": "eslint src", "lint:fix": "eslint src --fix", "prettier:check": "prettier --check \"src/**/*\"", "prettier:fix": "prettier --write \"src/**/*\"", "ts:check": "tsc --noEmit", "check:all": "npm run lint && npm run prettier:check && npm run ts:check", "fix:all": "npm run lint:fix && npm run prettier:fix", "proxy": "w2 add --force"}, "dependencies": {"axios": "^1.10.0", "http-server": "^14.1.1", "pinyin-pro": "^3.26.0", "quill": "^2.0.2", "quill-mention": "^6.1.1", "quilljs": "^0.18.1"}, "devDependencies": {"@babel/core": "^7.14.6", "@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.27.1", "@eslint/js": "^9.31.0", "@types/axios": "^0.9.36", "@types/quill": "^2.0.14", "babel-loader": "^8.2.2", "babel-plugin-transform-remove-console": "^6.9.4", "clean-webpack-plugin": "^4.0.0", "css-loader": "^5.2.6", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "globals": "^16.3.0", "html-webpack-plugin": "^5.3.1", "prettier": "^3.6.2", "style-loader": "^2.0.0", "ts-loader": "^9.5.2", "typescript": "^5.8.3", "typescript-eslint": "^8.37.0", "webpack": "^5.100.1", "webpack-cli": "^4.10.0", "webpack-dev-server": "^4.15.2"}}