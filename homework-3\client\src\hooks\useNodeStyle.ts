import type { NodeStyle } from "../types/mindmap"

export const useNodeStyle = (
  selectedNodeId: string | null,
  mindMapNodes: Record<string, any>,
  updateNode: (nodeId: string, text: string, style?: NodeStyle) => void
) => {
  const getDefaultStyle = (): NodeStyle => ({
    fontSize: 14,
    fontFamily: "微软雅黑",
    fontWeight: "normal",
    fontStyle: "normal",
    textDecoration: "none",
    color: "#000000",
    backgroundColor: "#ffffff",
    borderColor: "#d1d5db",
    borderWidth: 1
  })

  const handleToggleBold = () => {
    if (selectedNodeId) {
      const node = mindMapNodes[selectedNodeId]
      const currentStyle = node.style || getDefaultStyle()
      const newStyle = {
        ...currentStyle,
        fontWeight:
          currentStyle.fontWeight === "bold"
            ? "normal"
            : ("bold" as "normal" | "bold")
      }
      updateNode(selectedNodeId, node.text, newStyle)
    }
  }

  const handleToggleItalic = () => {
    if (selectedNodeId) {
      const node = mindMapNodes[selectedNodeId]
      const currentStyle = node.style || getDefaultStyle()
      const newStyle = {
        ...currentStyle,
        fontStyle:
          currentStyle.fontStyle === "italic"
            ? "normal"
            : ("italic" as "normal" | "italic")
      }
      updateNode(selectedNodeId, node.text, newStyle)
    }
  }

  const handleToggleUnderline = () => {
    if (selectedNodeId) {
      const node = mindMapNodes[selectedNodeId]
      const currentStyle = node.style || getDefaultStyle()
      const newStyle = {
        ...currentStyle,
        textDecoration:
          currentStyle.textDecoration === "underline"
            ? "none"
            : ("underline" as "none" | "underline")
      }
      updateNode(selectedNodeId, node.text, newStyle)
    }
  }

  const handleColorChange = (color: string) => {
    if (selectedNodeId) {
      const node = mindMapNodes[selectedNodeId]
      const currentStyle = node.style || getDefaultStyle()
      const newStyle = {
        ...currentStyle,
        color: color
      }
      updateNode(selectedNodeId, node.text, newStyle)
    }
  }

  const handleFontFamilyChange = (fontFamily: string) => {
    if (selectedNodeId) {
      const node = mindMapNodes[selectedNodeId]
      const currentStyle = node.style || getDefaultStyle()
      const newStyle = {
        ...currentStyle,
        fontFamily: fontFamily
      }
      updateNode(selectedNodeId, node.text, newStyle)
    }
  }

  const handleFontSizeChange = (fontSize: number) => {
    if (selectedNodeId) {
      const node = mindMapNodes[selectedNodeId]
      const currentStyle = node.style || getDefaultStyle()
      const newStyle = {
        ...currentStyle,
        fontSize: fontSize
      }
      updateNode(selectedNodeId, node.text, newStyle)
    }
  }

  const handleTextAlignChange = (textAlign: "left" | "center" | "right") => {
    if (selectedNodeId) {
      const node = mindMapNodes[selectedNodeId]
      const currentStyle = node.style || getDefaultStyle()
      const newStyle = {
        ...currentStyle,
        textAlign: textAlign
      }
      updateNode(selectedNodeId, node.text, newStyle)
    }
  }

  const handleBorderWidthChange = (borderWidth: number) => {
    if (selectedNodeId) {
      const node = mindMapNodes[selectedNodeId]
      const currentStyle = node.style || getDefaultStyle()
      const newStyle = {
        ...currentStyle,
        borderWidth: borderWidth
      }
      updateNode(selectedNodeId, node.text, newStyle)
    }
  }

  return {
    handleToggleBold,
    handleToggleItalic,
    handleToggleUnderline,
    handleColorChange,
    handleFontFamilyChange,
    handleFontSizeChange,
    handleTextAlignChange,
    handleBorderWidthChange,
    getDefaultStyle
  }
}
