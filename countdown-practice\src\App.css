.app-center-container {
  min-height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
}

.start-timer-btn {
  display: flex;
  align-items: center;
  gap: 14px;
  padding: 20px 44px;
  background: #fff;
  border: 2px solid #fa7d1a;
  border-radius: 18px;
  box-shadow: 0 2px 12px rgba(250, 125, 26, 0.08);
  font-size: 30px;
  font-family:
    'Segoe UI', 'PingFang SC', 'Hiragino Sans', 'Microsoft YaHei', Arial,
    sans-serif;
  font-weight: 600;
  color: #fa7d1a;
  cursor: pointer;
  transition:
    background 0.18s cubic-bezier(0.4, 0, 0.2, 1),
    color 0.18s cubic-bezier(0.4, 0, 0.2, 1),
    border-color 0.18s cubic-bezier(0.4, 0, 0.2, 1),
    box-shadow 0.18s cubic-bezier(0.4, 0, 0.2, 1),
    transform 0.18s cubic-bezier(0.4, 0, 0.2, 1);
}

.start-timer-btn:hover {
  background: #fa7d1a;
  color: #fff;
  border-color: #fa7d1a;
  box-shadow: 0 6px 24px rgba(250, 125, 26, 0.18);
  transform: scale(1.04);
}

.start-timer-btn .icon {
  width: 44px;
  height: 44px;
  display: block;
  transition: filter 0.18s;
  filter: drop-shadow(0 2px 4px rgba(250, 125, 26, 0.1));
}
