import * as DropdownMenu from "@radix-ui/react-dropdown-menu"
import * as Tooltip from "@radix-ui/react-tooltip"
import React from "react"
import "./css/TooltipDropMenusCss.css"
interface MenusItemProp {
  id?: string
  label?: string
  icon?: React.ReactNode // 按钮图标
  onClick: () => void
}

interface TooltipDropdownButtonProps {
  id?: string
  label: string // Tooltip 提示文本
  menuItems: MenusItemProp[] // Dropdown 菜单项
  icon?: React.ReactNode // 按钮图标
  className?: string
}

export const TooltipDropdownButton: React.FC<TooltipDropdownButtonProps> = ({
  label,
  menuItems,
  icon,
  className = ""
}) => {
  const [menuOpen, setMenuOpen] = React.useState(false)
  const [tooltipOpen, setTooltipOpen] = React.useState(false)

  return (
    <Tooltip.Provider delayDuration={200}>
      <DropdownMenu.Root
        open={menuOpen}
        onOpenChange={(open) => {
          setMenuOpen(open)
          if (open) setTooltipOpen(false) // 打开菜单时强制关闭 Tooltip
        }}
      >
        <Tooltip.Root open={tooltipOpen} onOpenChange={setTooltipOpen}>
          <Tooltip.Trigger asChild>
            <DropdownMenu.Trigger asChild>
              <button
                className={`icon-btn ${className}`}
                aria-label={label}
                type="button"
              >
                {icon || "☰"}
              </button>
            </DropdownMenu.Trigger>
          </Tooltip.Trigger>
          <Tooltip.Content
            className="tooltip-content"
            side="bottom"
            align="center"
          >
            {label}
            <Tooltip.Arrow className="tooltip-arrow" />
          </Tooltip.Content>
        </Tooltip.Root>

        <DropdownMenu.Portal>
          <DropdownMenu.Content
            className="dropdown-content"
            sideOffset={6}
            onCloseAutoFocus={(e) => e.preventDefault()} // 防止按钮聚焦闪烁
          >
            {menuItems.map((item, index) => (
              <DropdownMenu.Item
                key={index}
                className="dropdown-item"
                onSelect={() => {
                  setMenuOpen(false)
                  item.onClick()
                }}
              >
                {item.label}
              </DropdownMenu.Item>
            ))}
          </DropdownMenu.Content>
        </DropdownMenu.Portal>
      </DropdownMenu.Root>
    </Tooltip.Provider>
  )
}
