// 简单的测试脚本来验证流式发送
const http = require('http');

console.log('🚀 开始测试流式接口...');

const options = {
  hostname: 'localhost',
  port: 3001,
  path: '/api/stream-mindmap?prompt=react学习路线',
  method: 'GET',
  headers: {
    'Content-Type': 'application/json',
    'Cache-Control': 'no-cache'
  }
};

const req = http.request(options, (res) => {
  console.log(`状态码: ${res.statusCode}`);
  console.log(`响应头: ${JSON.stringify(res.headers)}`);

  let buffer = '';
  let nodeCount = 0;
  let doneCount = 0;

  res.on('data', (chunk) => {
    buffer += chunk.toString();
    const lines = buffer.split('\n');
    buffer = '';

    for (const line of lines) {
      if (line.trim() === '') continue;
      
      if (line.startsWith('data: ')) {
        try {
          const data = JSON.parse(line.slice(6));
          
          if (data.type === 'node') {
            nodeCount++;
            console.log(`📦 节点 ${nodeCount}: ${data.node.text} (${data.node.id})`);
          } else if (data.type === 'done') {
            doneCount++;
            console.log(`🏁 收到done消息 #${doneCount}`);
          }
        } catch (e) {
          console.error('解析错误:', line.slice(6));
        }
      }
    }
  });

  res.on('end', () => {
    console.log(`✅ 连接结束，共收到 ${nodeCount} 个节点，${doneCount} 个done消息`);
  });

  res.on('error', (err) => {
    console.error('❌ 响应错误:', err);
  });
});

req.on('error', (err) => {
  console.error('❌ 请求错误:', err);
});

req.end();
