<script setup lang="ts">
import ChartGraph from '@/components/ChartGraph.vue'
import { useMoney } from '@/hooks/useMoney'
import type { moneyItem } from '@/stores'
import { computed, ref, watch } from 'vue'
const { pay_list, collect_list, total_pay, total_income } = useMoney()

//传递的数据类型
const dataType = ref<string>('collect')
const allData = ref([
  { name: '总支出', value: total_pay },
  { name: '总收入', value: total_income }
])
const handleClick = (event: Event) => {
  const target = event.target as HTMLElement
  if (target.tagName === 'DIV' && target.parentElement?.classList.contains('tab-sel')) {
    dataType.value = target.getAttribute('value')!
    console.log('🚀 ~ handleClick ~ dataType.value:', dataType.value)
    const parent = target.parentElement as HTMLElement
    parent.querySelectorAll('div').forEach((el) => {
      el.classList.remove('tab-underline')
    })
    target.classList.add('tab-underline')
  }
}

// 提取 name 和 money组成饼图数据
const extractNameAndMoney = (data: any) => {
  return data.map((item: moneyItem) => ({
    name: item.name,
    value: item.money
  }))
}

const data = computed(() => {
  const collectListData = extractNameAndMoney(collect_list.value)
  const payListData = extractNameAndMoney(pay_list.value)

  switch (dataType.value) {
    case 'collect':
      return collectListData
    case 'pay':
      return payListData
    case 'all':
      return allData.value
    default:
      return []
  }
})

//监控 data 的变化
watch(data, (newVal) => {
  console.log('Data updated:', newVal)
})
</script>

<template>
  <div class="chart-box">
    <div class="tab-sel" @click="handleClick">
      <div value="collect" class="tab-underline">收入明细</div>
      <div value="pay">支出明细</div>
      <div value="all">本月汇总</div>
    </div>
    <ChartGraph :width="'100vw'" :height="'50vh'" :data="data" class="charts" />
    <div class="no-data" v-show="!data.length">暂无数据</div>
  </div>
</template>

<style scoped>
.chart-box {
  width: 100%;
  position: relative;
}
.tab-sel {
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-top: 50px;
  cursor: pointer;
}
.tab-underline {
  border-bottom: 1px solid black;
}
.charts {
  display: block;
  max-width: 100%;
  margin: 0 auto;
  position: relative;
  top: 50%;
}
.no-data {
  position: absolute;
  left: 51%;
  top: 49%;
  color: grey;
  font-size: 14px;
  transform: translateX(-50%);
}
</style>
