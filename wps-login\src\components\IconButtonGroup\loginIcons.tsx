import { type IconButtonProps } from "../IconButton";

// 微信图标SVG
const WechatIcon = () => (
  <svg
    className="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="2361"
    width="32"
    height="32"
  >
    <path
      d="M337.387283 341.82659c-17.757225 0-35.514451 11.83815-35.514451 29.595375s17.757225 29.595376 35.514451 29.595376 29.595376-11.83815 29.595376-29.595376c0-18.49711-11.83815-29.595376-29.595376-29.595375zM577.849711 513.479769c-11.83815 0-22.936416 12.578035-22.936416 23.6763 0 12.578035 11.83815 23.676301 22.936416 23.676301 17.757225 0 29.595376-11.83815 29.595376-23.676301s-11.83815-23.676301-29.595376-23.6763zM501.641618 401.017341c17.757225 0 29.595376-12.578035 29.595376-29.595376 0-17.757225-11.83815-29.595376-29.595376-29.595375s-35.514451 11.83815-35.51445 29.595375 17.757225 29.595376 35.51445 29.595376zM706.589595 513.479769c-11.83815 0-22.936416 12.578035-22.936416 23.6763 0 12.578035 11.83815 23.676301 22.936416 23.676301 17.757225 0 29.595376-11.83815 29.595376-23.676301s-11.83815-23.676301-29.595376-23.6763z"
      fill="#28C445"
      p-id="2362"
    ></path>
    <path
      d="M510.520231 2.959538C228.624277 2.959538 0 231.583815 0 513.479769s228.624277 510.520231 510.520231 510.520231 510.520231-228.624277 510.520231-510.520231-228.624277-510.520231-510.520231-510.520231zM413.595376 644.439306c-29.595376 0-53.271676-5.919075-81.387284-12.578034l-81.387283 41.433526 22.936416-71.768786c-58.450867-41.433526-93.965318-95.445087-93.965317-159.815029 0-113.202312 105.803468-201.988439 233.803468-201.98844 114.682081 0 216.046243 71.028902 236.023121 166.473989-7.398844-0.739884-14.797688-1.479769-22.196532-1.479769-110.982659 1.479769-198.289017 85.086705-198.289017 188.67052 0 17.017341 2.959538 33.294798 7.398844 49.572255-7.398844 0.739884-15.537572 1.479769-22.936416 1.479768z m346.265896 82.867052l17.757225 59.190752-63.630058-35.514451c-22.936416 5.919075-46.612717 11.83815-70.289017 11.83815-111.722543 0-199.768786-76.947977-199.768786-172.393063-0.739884-94.705202 87.306358-171.653179 198.289017-171.65318 105.803468 0 199.028902 77.687861 199.028902 172.393064 0 53.271676-34.774566 100.624277-81.387283 136.138728z"
      fill="#28C445"
      p-id="2363"
    ></path>
  </svg>
);

// QQ图标SVG
const QQIcon = () => (
  <svg
    className="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="3340"
    width="32"
    height="32"
  >
    <path
      d="M512 0C229.003636 0 0 229.003636 0 512s229.003636 512 512 512 512-229.003636 512-512S794.996364 0 512 0z m210.385455 641.396364c-7.447273 9.309091-26.996364-1.861818-41.89091-32.581819-3.723636 13.963636-13.032727 36.305455-34.443636 64.232728 35.374545 8.378182 44.683636 42.821818 33.512727 61.44-8.378182 13.032727-26.996364 24.203636-59.578181 24.203636-58.647273 0-83.781818-15.825455-95.883637-26.996364-1.861818-2.792727-5.585455-3.723636-10.24-3.723636-4.654545 0-7.447273 0.930909-10.24 3.723636-11.170909 11.170909-37.236364 26.996364-95.883636 26.996364-32.581818 0-52.130909-11.170909-59.578182-24.203636-12.101818-18.618182-1.861818-53.061818 33.512727-61.44-20.48-27.927273-29.789091-50.269091-34.443636-64.232728-13.963636 30.72-34.443636 42.821818-41.890909 32.581819-5.585455-8.378182-8.378182-26.065455-7.447273-38.167273 3.723636-46.545455 34.443636-85.643636 53.061818-106.123636-2.792727-5.585455-8.378182-40.029091 14.894546-63.301819v-1.861818c0-92.16 65.163636-158.254545 148.014545-158.254545 81.92 0 148.014545 66.094545 148.014546 158.254545v1.861818c23.272727 23.272727 17.687273 57.716364 14.894545 63.301819 17.687273 20.48 49.338182 59.578182 53.061818 106.123636 0.930909 12.101818-0.930909 29.789091-7.447272 38.167273z"
      fill="#30A5DD"
      p-id="3341"
    ></path>
  </svg>
);

// SSO图标SVG
const SSOIcon = () => (
  <svg
    className="icon sso_icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="6061"
    width="32"
    height="32"
  >
    <path
      d="M313.344 569.2672c0 19.968-5.1456 37.888-15.4368 53.76-10.2656 15.9232-25.3184 28.3648-45.1072 37.376-19.7888 8.9856-43.264 13.4912-70.4256 13.4912-32.5376 0-59.392-6.144-80.5376-18.432a107.3152 107.3152 0 0 1-36.5824-35.456C55.8848 605.2352 51.2 590.8736 51.2 576.9216c0-8.0896 2.816-15.0272 8.448-20.8128 5.632-5.76 12.8-8.6528 21.4784-8.6528 7.04 0 13.0048 2.2528 17.8944 6.7328 4.864 4.5056 9.0368 11.1872 12.4672 20.0448 4.224 10.496 8.7552 19.2768 13.6192 26.3168s11.7504 12.8512 20.5824 17.4336c8.8576 4.5824 20.48 6.8608 34.8928 6.8608 19.7888 0 35.8912-4.608 48.256-13.824 12.3648-9.216 18.56-20.736 18.56-34.56 0-10.9312-3.328-19.8144-10.0096-26.624a64.5632 64.5632 0 0 0-25.856-15.6672c-10.5984-3.584-24.7296-7.424-42.4448-11.4688-23.68-5.5552-43.52-12.032-59.4944-19.456-15.9744-7.424-28.672-17.5616-38.0416-30.3872-9.3696-12.8-14.0544-28.7488-14.0544-47.7952 0-18.176 4.9408-34.304 14.848-48.384 9.9072-14.08 24.2176-24.9344 42.9824-32.512 18.7392-7.5776 40.8064-11.3664 66.1504-11.3664 20.224 0 37.76 2.5088 52.5312 7.5264 14.7712 5.0432 27.0336 11.6992 36.7872 20.0448 9.7536 8.32 16.896 17.0496 21.376 26.2144 4.5056 9.1392 6.7584 18.0736 6.7584 26.752 0 7.9616-2.816 15.104-8.448 21.504-5.632 6.3744-12.6208 9.5488-21.0432 9.5488-7.6288 0-13.44-1.8944-17.408-5.7344-3.9936-3.84-8.2944-10.0864-12.9536-18.7904-6.016-12.4416-13.2096-22.144-21.6064-29.1328-8.3968-6.9632-21.888-10.4704-40.4992-10.4704-17.2544 0-31.1552 3.7888-41.728 11.3664-10.5728 7.5776-15.872 16.6912-15.872 27.3408 0 6.6048 1.792 12.288 5.4016 17.1008 3.584 4.8128 8.5504 8.9344 14.848 12.3904 6.2976 3.4304 12.672 6.144 19.1232 8.0896 6.4512 1.9456 17.1008 4.7872 31.9488 8.5504 18.6112 4.352 35.4304 9.1392 50.5088 14.3872 15.104 5.2736 27.904 11.648 38.4768 19.1488 10.5728 7.5008 18.8416 16.9728 24.7552 28.4416 5.9392 11.4944 8.8832 25.5488 8.8832 42.1888z m307.328 0c0 19.968-5.12 37.888-15.4112 53.76-10.2656 15.9232-25.3184 28.3648-45.1072 37.376-19.8144 8.9856-43.264 13.4912-70.4256 13.4912-32.5632 0-59.392-6.144-80.5632-18.432a107.3152 107.3152 0 0 1-36.5568-35.456c-9.3696-14.7712-14.08-29.1328-14.08-43.0848 0-8.0896 2.816-15.0272 8.448-20.8128 5.632-5.76 12.8-8.6528 21.504-8.6528 7.04 0 13.0048 2.2528 17.8944 6.7328 4.864 4.5056 9.0368 11.1872 12.4672 20.0448 4.1984 10.496 8.7552 19.2768 13.6192 26.3168s11.7504 12.8512 20.5824 17.4336c8.8576 4.5824 20.48 6.8608 34.8928 6.8608 19.7888 0 35.8656-4.608 48.256-13.824 12.3648-9.216 18.56-20.736 18.56-34.56 0-10.9312-3.328-19.8144-10.0096-26.624a64.5632 64.5632 0 0 0-25.856-15.6672c-10.5984-3.584-24.7296-7.424-42.4448-11.4688-23.68-5.5552-43.52-12.032-59.4944-19.456-15.9744-7.424-28.672-17.5616-38.0416-30.3872-9.3696-12.8-14.0544-28.7488-14.0544-47.7952 0-18.176 4.9408-34.304 14.848-48.384 9.9072-14.08 24.2176-24.9344 42.9824-32.512 18.7392-7.5776 40.7808-11.3664 66.1504-11.3664 20.224 0 37.76 2.5088 52.5312 7.5264 14.7712 5.0432 27.0336 11.6992 36.7872 20.0448 9.728 8.32 16.896 17.0496 21.376 26.2144 4.5056 9.1392 6.7584 18.0736 6.7584 26.752 0 7.9616-2.816 15.104-8.448 21.504-5.632 6.3744-12.6464 9.5488-21.0432 9.5488-7.6544 0-13.4656-1.8944-17.408-5.7344-3.9936-3.84-8.32-10.0864-12.9536-18.7904-6.016-12.4416-13.2096-22.144-21.6064-29.1328-8.3968-6.9632-21.888-10.4704-40.4992-10.4704-17.2544 0-31.1552 3.7888-41.728 11.3664-10.5984 7.5776-15.872 16.6912-15.872 27.3408 0 6.6048 1.792 12.288 5.376 17.1008 3.6352 4.8128 8.576 8.9344 14.8736 12.3904 6.2976 3.4304 12.672 6.144 19.1232 8.0896 6.4512 1.9456 17.1008 4.7872 31.9488 8.5504 18.6112 4.352 35.4304 9.1392 50.5088 14.3872 15.104 5.2736 27.904 11.648 38.4768 19.1488 10.5728 7.5008 18.8416 16.9728 24.7552 28.4416 5.9136 11.4944 8.8832 25.5488 8.8832 42.1888zM822.528 332.8c34.2016 0 63.5648 6.9376 88.064 20.8128a134.912 134.912 0 0 1 55.7056 59.1872c12.5952 25.5744 18.8928 55.6032 18.8928 90.112 0 25.4976-3.4304 48.64-10.3424 69.504a150.4256 150.4256 0 0 1-31.0528 54.2464 135.6544 135.6544 0 0 1-50.8416 35.072c-20.096 8.1152-43.136 12.16-69.0688 12.16-25.8048 0-48.896-4.1472-69.2992-12.4928a137.472 137.472 0 0 1-51.0976-35.2c-13.6448-15.1552-23.9616-33.3824-30.9248-54.6816a220.7232 220.7232 0 0 1-10.4704-69.0688c0-25.344 3.6352-48.5888 10.9312-69.76 7.2704-21.1456 17.792-39.1424 31.5904-53.9904a136.704 136.704 0 0 1 50.432-34.0992c19.7888-7.8592 42.2912-11.8016 67.4816-11.8016z m94.9504 169.6512c0-24.1408-3.8912-45.056-11.6992-62.7712-7.808-17.7152-18.944-31.104-33.408-40.1664-14.4896-9.088-31.104-13.6192-49.8432-13.6192-13.3632 0-25.7024 2.5088-37.0176 7.552a81.3312 81.3312 0 0 0-29.2352 21.9392c-8.192 9.6-14.6432 21.8624-19.3536 36.7872-4.736 14.9248-7.0912 31.6672-7.0912 50.2784 0 18.7392 2.3552 35.6864 7.0912 50.8416 4.7104 15.1552 11.392 27.7248 20.0192 37.6832 8.6272 9.984 18.5344 17.4592 29.696 22.4 11.1872 4.9408 23.4496 7.424 36.7872 7.424 17.1008 0 32.8192-4.2752 47.1296-12.8256 14.336-8.5504 25.728-21.76 34.2016-39.6032 8.4736-17.8432 12.7232-39.8336 12.7232-65.92z"
      fill="#ffffff"
      p-id="6062"
    ></path>
  </svg>
);

// 苹果图标
const AppleIcon = () => (
  <svg
    className="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="9457"
    width="30"
    height="30"
  >
    <path
      d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z"
      fill="#333333"
      p-id="9458"
    ></path>
    <path
      d="M684.8 571.648c15.872 22.528 33.792 36.864 54.272 43.264-8.96 27.392-22.784 56.064-42.24 85.76-29.696 44.8-58.88 67.072-87.808 67.072-10.752 0-26.88-3.584-47.872-11.008-19.712-7.424-36.864-11.008-51.456-11.008-14.592 0-30.72 3.84-48.384 11.52-18.432 7.424-33.792 11.008-45.824 11.008-34.816 0-68.864-29.44-102.4-88.32-33.536-58.112-50.176-115.456-50.176-171.776 0-52.224 12.544-94.72 38.144-128 26.112-33.024 58.624-49.664 97.28-49.664 8.448 0 17.92 1.024 28.416 3.072 10.496 2.048 21.248 6.144 32.512 12.032 12.032 6.656 22.016 11.008 29.44 13.568 7.68 2.56 13.824 3.84 17.92 3.84 4.864 0 12.544-1.28 23.04-3.328 10.496-2.304 20.736-6.656 31.232-12.544 11.264-6.144 20.736-10.752 28.928-13.824s16.384-4.608 24.832-4.608c27.136 0 51.2 7.424 72.96 22.016 11.52 7.68 23.296 18.944 35.328 34.048-17.664 15.616-30.72 28.928-38.656 40.448-14.848 21.76-22.528 45.056-22.528 70.656-0.512 27.648 7.424 53.248 23.04 75.776z m-115.968-270.08c-13.312 12.544-25.856 20.992-37.12 24.832-3.84 1.28-8.704 2.304-14.848 3.072-6.144 1.024-12.8 2.048-20.48 2.56 0.256-33.792 9.216-63.232 26.624-87.808s46.08-41.728 85.76-50.944c0.768 3.84 1.536 6.656 1.792 7.936v6.4c0 13.824-3.328 29.44-9.728 46.848-7.168 17.152-17.92 32.768-32 47.104z"
      fill="#FFFFFF"
      p-id="9459"
    ></path>
  </svg>
);

// 手机图标
const PhoneIcon = () => (
  <svg
    className="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="51375"
    width="32"
    height="32"
  >
    <path
      d="M512 0c282.771911 0 512 229.228089 512 512s-229.228089 512-512 512S0 794.771911 0 512 229.228089 0 512 0z m111.189333 204.714667h-222.378666c-11.275378 0-21.822578 2.133333-31.635911 6.405689a81.095111 81.095111 0 0 0-25.685334 17.544533 82.147556 82.147556 0 0 0-17.225955 25.969778c-4.175644 9.898667-6.263467 20.462933-6.263467 31.704177v451.9936c0 11.246933 2.087822 21.703111 6.263467 31.368534a83.228444 83.228444 0 0 0 17.225955 25.634133 81.095111 81.095111 0 0 0 25.685334 17.544533c9.813333 4.266667 20.360533 6.405689 31.630222 6.405689h222.384355c11.275378 0 21.822578-2.133333 31.635911-6.405689a81.095111 81.095111 0 0 0 25.685334-17.544533 83.228444 83.228444 0 0 0 17.225955-25.634133c4.175644-9.671111 6.263467-20.1216 6.263467-31.368534V286.338844c0-11.241244-2.087822-21.8112-6.263467-31.704177a82.147556 82.147556 0 0 0-17.225955-25.969778 81.095111 81.095111 0 0 0-25.685334-17.544533c-9.813333-4.266667-20.360533-6.405689-31.630222-6.405689z m-111.502222 519.452444c9.187556 0 16.913067 3.373511 23.176533 10.120533 6.263467 6.741333 9.398044 15.064178 9.398045 24.957156 0 9.443556-3.128889 17.652622-9.398045 24.6272-6.263467 6.968889-13.988978 10.456178-23.176533 10.456178-8.772267 0-16.389689-3.487289-22.869333-10.456178-6.468267-6.974578-9.705244-15.183644-9.705245-24.6272 0-9.892978 3.236978-18.215822 9.710934-24.957156 6.473956-6.747022 14.091378-10.126222 22.863644-10.126222z m141.573689-405.447111v386.56H370.113422v-386.56h283.147378zM551.776711 249.912889c2.503111 0 4.801422 1.012622 6.894933 3.037867 2.087822 2.019556 3.128889 4.835556 3.128889 8.430933s-1.041067 6.405689-3.128889 8.430933c-2.093511 2.025244-4.386133 3.037867-6.894933 3.037867H472.223289c-2.503111 0-4.801422-1.012622-6.894933-3.037867-2.087822-2.025244-3.128889-4.835556-3.128889-8.430933 0-3.601067 1.149156-6.411378 3.447466-8.430933 2.292622-2.025244 4.488533-3.037867 6.576356-3.037867z"
      fill="#11a7fa"
      p-id="51376"
    ></path>
  </svg>
);

// 更多图标
const MoreIcon = () => (
  <svg
    className="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="2300"
    width="32"
    height="32"
  >
    <path
      d="M510.5697576277334 9.55795236535306c-277.40996943265185 0-502.3005237753679 224.890554342716-502.3005237753679 502.3508618960593 0 277.4073195128098 224.890554342716 502.30184808801965 502.3005237753679 502.30184808801965 277.46163316053327 0 502.3495375834074-224.8945285752099 502.3495375834074-502.30184808801965C1012.9192952111408 234.4485067080692 788.0313907882667 9.55795236535306 510.5697576277334 9.55795236535306zM219.6676274656396 599.285296959842c-48.284320188997526 0-87.4281464263111-39.15575022933333-87.4281464263111-87.4281464263111 0-48.21940945098271 39.143827531851855-87.37648399296789 87.4281464263111-87.37648399296789 48.24722778390124 0 87.40297801323459 39.15707454198518 87.40297801323459 87.37648399296789C307.0706041843358 560.1308723376987 267.91485524954084 599.285296959842 219.6676274656396 599.285296959842zM510.5962516480001 599.285296959842c-48.27239749151605 0-87.40297801323459-39.15575022933333-87.40297801323459-87.4281464263111 0-48.21940945098271 39.130580521718514-87.37648399296789 87.40297801323459-87.37648399296789 48.24722778390124 0 87.40297801323459 39.15707454198518 87.40297801323459 87.37648399296789C597.9979040540445 560.1308723376987 558.8434794319012 599.285296959842 510.5962516480001 599.285296959842zM801.497057497442 599.285296959842c-48.21940945098271 0-87.37648399296789-39.15575022933333-87.37648399296789-87.4281464263111 0-48.21940945098271 39.15707454198518-87.37648399296789 87.37648399296789-87.37648399296789 48.27107317886419 0 87.45464174111605 39.15707454198518 87.45464174111605 87.37648399296789C888.951699238558 560.1308723376987 849.769454988958 599.285296959842 801.497057497442 599.285296959842z"
      fill="#8a8a8a"
      p-id="2301"
    ></path>
  </svg>
);

// 登录方式配置
export const loginMethodConfigs: Record<
  string,
  Omit<IconButtonProps, "onClick">
> = {
  wechat: {
    id: "wechat",
    text: "微信账号",
    mobileIcon: {
      type: "svg",
      content: <WechatIcon />,
      size: 40
    },
    desktopIcon: {
      type: "svg",
      content: <WechatIcon />,
      size: 48
    }
  },
  qq: {
    id: "qq",
    text: "QQ",
    mobileIcon: {
      type: "svg",
      content: <QQIcon />,
      size: 40
    },
    desktopIcon: {
      type: "svg",
      content: <QQIcon />,
      size: 48
    }
  },
  phone: {
    id: "phone",
    text: "手机",
    mobileIcon: {
      type: "svg",
      content: <PhoneIcon />,
      size: 40
    },
    desktopIcon: {
      type: "svg",
      content: <PhoneIcon />,
      size: 48
    }
  },
  sso: {
    id: "sso",
    text: "SSO",
    mobileIcon: {
      type: "svg",
      content: <SSOIcon />,
      size: 40
    },
    desktopIcon: {
      type: "svg",
      content: <SSOIcon />,
      size: 48
    }
  },
  apple: {
    id: "apple",
    text: "苹果",
    mobileIcon: {
      type: "svg",
      content: <AppleIcon />,
      size: 40
    },
    desktopIcon: {
      type: "svg",
      content: <AppleIcon />,
      size: 48
    }
  },
  more: {
    id: "more",
    text: "更多",
    mobileIcon: {
      type: "svg",
      content: <MoreIcon />,
      size: 40
    },
    desktopIcon: {
      type: "svg",
      content: <MoreIcon />,
      size: 48
    }
  }
};
