import React, { useEffect } from "react"
import { useQuestionState } from "../../hooks/useQuestionState"
import "./index.css"

interface QuestionItemProp {
  id: string
  question: string
  options: string[]
  onOptionSelect: () => void // 用于通知父组件更新进度
}

const QuestionItem: React.FC<QuestionItemProp> = ({
  id,
  question,
  options,
  onOptionSelect,
}) => {
  const [state, setOption] = useQuestionState(
    `question-${id}`,
    id,
    question,
    "local"
  )

  // 当选项改变时通知父组件更新进度
  useEffect(() => {
    onOptionSelect() // 调用父组件的方法更新进度
  }, [state.selectedOption, onOptionSelect])

  const handleOptionChange = (option: string) => {
    // 如果选中的选项已经是当前选项，则取消选择
    if (state.selectedOption === option) {
      setOption(null) // 取消选中
    } else {
      setOption(option) // 设置选中的选项
    }
  }

  return (
    <div className="question-item">
      <h2>
        第{id}题：{question}
      </h2>
      <div className="options">
        {options.map((option) => (
          <label key={option} className="option">
            <input
              type="checkbox" // 改为 checkbox
              name={`question-${id}`}
              value={option}
              checked={state.selectedOption === option}
              onChange={() => handleOptionChange(option)} // 调用 handleOptionChange 方法
            />
            {option}
          </label>
        ))}
      </div>
    </div>
  )
}

export default QuestionItem
