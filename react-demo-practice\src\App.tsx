import RetryDemo from './retry-demo';
import LazyLoadDemo from './lazy-load-demo'
import Demo1 from './state-demo/demo-1';
import Demo2 from './state-demo/demo-2';
import Demo3 from './state-demo/demo-3';
import Demo4 from './state-demo/demo-4';
import Demo5 from './state-demo/demo-5';
import Practice1 from './state-practice/practice-1';
import Practice2 from './state-practice/practice-2';
import Practice3 from './state-practice/practice-3';
import Practice4 from './state-practice/practice-4';
import ComponentPractice from './component-practice';

function App() {
  return <div className='demo-ctn' >
    <h1 >懒加载Demo</h1>
    <RetryDemo/>
    
    <h1 >懒加载Demo</h1>
    <LazyLoadDemo />

    <h1 className='practice'>组件可扩展设计练习：优化图片上传组件</h1>
    <ComponentPractice />

    <h1 >状态设计Demo</h1>

    <h2 >1. 选择合适的数据作为state</h2>
    <Demo1/>

    <h2 >2. 合并关联的状态Demo</h2>
    <Demo2/>

    <h2 >3. 避免矛盾的状态Demo</h2>
    <Demo3/>

    <h2 >4. 避免冗余的状态Demo</h2>
    <Demo4/>

    <h2 >5. 避免重复的状态Demo</h2>
    <Demo5/>

    <h1 className='practice'>状态设计练习</h1>

    <h2 className='practice'>练习 1 : 修复一个未更新的组件</h2>
    <Practice1 />

    <h2 className='practice'>练习 2 : 修复一个损坏的打包清单</h2>
    <Practice2 />

    <h2 className='practice'>练习 3 : 修复消失的选项</h2>
    <Practice3 />

    <h2 className='practice'>练习 4 : 实现多选功能</h2>
    <Practice4 />
  </div>
}

export default App;
