import { Button, Checkbox, Modal } from 'antd'
import React from 'react'
import CustomInputNumber from '../CustomInputNumber'
import './style.css'

export interface CountdownSetupModalProps {
  open: boolean
  hours: number
  minutes: number
  seconds: number
  ring: boolean
  onChange: (data: {
    hours: number
    minutes: number
    seconds: number
    ring: boolean
  }) => void
  onOk: () => void
  onCancel: () => void
  afterClose?: () => void
  title?: string
  tip?: string
  okText?: string
}

const safeNum = (v: number | null | undefined) =>
  typeof v === 'number' && !isNaN(v) ? v : 0

const CountdownSetupModal: React.FC<CountdownSetupModalProps> = ({
  open,
  hours,
  minutes,
  seconds,
  ring,
  onChange,
  onOk,
  onCancel,
  afterClose,
  title = '倒计时',
  tip = '课间休息一会儿吧~',
  okText = '开始倒计时',
}) => {
  const isTimeSet = safeNum(hours) + safeNum(minutes) + safeNum(seconds) > 0
  return (
    <Modal
      title={title}
      open={open}
      onOk={onOk}
      onCancel={onCancel}
      footer={null}
      afterClose={afterClose}
    >
      <div className="countdown-setup-tip">{tip}</div>
      <div className="countdown-setup-inputs">
        <CustomInputNumber
          min={0}
          max={23}
          value={safeNum(hours)}
          onChange={(v) =>
            onChange({ hours: safeNum(v), minutes, seconds, ring })
          }
          style={{ width: 60 }}
          formatter={(v) => String(v).padStart(2, '0')}
          className="custom-input-number"
        />
        <span>:</span>
        <CustomInputNumber
          min={0}
          max={59}
          value={safeNum(minutes)}
          onChange={(v) =>
            onChange({ hours, minutes: safeNum(v), seconds, ring })
          }
          style={{ width: 60 }}
          formatter={(v) => String(v).padStart(2, '0')}
          className="custom-input-number"
        />
        <span>:</span>
        <CustomInputNumber
          min={0}
          max={59}
          value={safeNum(seconds)}
          onChange={(v) =>
            onChange({ hours, minutes, seconds: safeNum(v), ring })
          }
          style={{ width: 60 }}
          formatter={(v) => String(v).padStart(2, '0')}
          className="custom-input-number"
        />
      </div>
      <div className="countdown-setup-actions">
        <Checkbox
          checked={ring}
          onChange={(e) =>
            onChange({ hours, minutes, seconds, ring: e.target.checked })
          }
        >
          结束时响铃
        </Checkbox>
        <Button
          type="primary"
          className="countdown-setup-btn"
          onClick={onOk}
          disabled={!isTimeSet}
        >
          {okText}
        </Button>
      </div>
    </Modal>
  )
}

export default CountdownSetupModal
