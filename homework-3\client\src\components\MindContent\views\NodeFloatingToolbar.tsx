import {
  TextAlignCenterIcon,
  TextAlignLeftIcon,
  TextAlignRightIcon
} from "@radix-ui/react-icons"
import type { NodeStyle } from "../../../types/mindmap"
import { FloatingToolbar } from "../../common/FloatingToolbar"

interface NodeFloatingToolbarProps {
  nodeX: number
  nodeY: number
  selectedNodeStyle?: NodeStyle
  onToggleBold?: () => void
  onToggleItalic?: () => void
  onToggleUnderline?: () => void
  onColorChange?: (color: string) => void
  onFontSizeChange?: (fontSize: number) => void
  onTextAlignChange?: (align: "left" | "center" | "right") => void
}

export const NodeFloatingToolbar = ({
  nodeX,
  nodeY,
  selectedNodeStyle,
  onToggleBold,
  onToggleItalic,
  onToggleUnderline,
  onColorChange,
  onFontSizeChange,
  onTextAlignChange
}: NodeFloatingToolbarProps) => {
  const config = [
    {
      type: "select" as const,
      options: ["12", "14", "16", "18", "20", "24"],
      value: selectedNodeStyle?.fontSize?.toString() || "14",
      onChange: (val: string) => onFontSizeChange?.(parseInt(val)),
      width: "w-[60px]"
    },
    { type: "separator" as const },

    {
      type: "button" as const,
      label: "加粗",
      icon: <strong>B</strong>,
      onClick: () => onToggleBold?.(),
      isActive: selectedNodeStyle?.fontWeight === "bold"
    },
    {
      type: "button" as const,
      label: "斜体",
      icon: <em>I</em>,
      onClick: () => onToggleItalic?.(),
      isActive: selectedNodeStyle?.fontStyle === "italic"
    },
    {
      type: "button" as const,
      label: "下划线",
      icon: <u>U</u>,
      onClick: () => onToggleUnderline?.(),
      isActive: selectedNodeStyle?.textDecoration === "underline"
    },
    { type: "separator" as const },

    {
      type: "color" as const,
      value: selectedNodeStyle?.color || "#000000",
      onChange: (color: string) => onColorChange?.(color)
    },
    
    { type: "separator" as const },

    {
      type: "button" as const,
      label: "左对齐",
      icon: <TextAlignLeftIcon />,
      onClick: () => onTextAlignChange?.("left"),
      isActive: selectedNodeStyle?.textAlign === "left"
    },
    {
      type: "button" as const,
      label: "居中对齐",
      icon: <TextAlignCenterIcon />,
      onClick: () => onTextAlignChange?.("center"),
      isActive: selectedNodeStyle?.textAlign === "center"
    },
    {
      type: "button" as const,
      label: "右对齐",
      icon: <TextAlignRightIcon />,
      onClick: () => onTextAlignChange?.("right"),
      isActive: selectedNodeStyle?.textAlign === "right"
    }
  ]

  return (
    <FloatingToolbar
      position={{ x: nodeX - 100, y: nodeY + 50 }}
      config={config}
    />
  )
}
