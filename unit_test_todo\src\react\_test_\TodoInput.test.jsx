import { fireEvent, render, screen } from "@testing-library/react"
import { beforeEach, describe, expect, it, vi } from "vitest"
import "vitest-dom/extend-expect"

import React from "react"
import TodoInput from "../components/TodoInput"
describe("TodoInput 组件", () => {
  let onSubmit
  let onCancel

  beforeEach(() => {
    onSubmit = vi.fn()
    onCancel = vi.fn()
  })

  // 1. 渲染测试
  it("应该渲染一个输入框和一个按钮", () => {
    render(<TodoInput onSubmit={onSubmit} onCancel={onCancel} />)
    expect(screen.getByRole("textbox")).toBeInTheDocument()
    expect(screen.getByRole("button")).toBeInTheDocument()
  })

  // 2. 输入与按钮状态
  it("输入有效内容时，按钮应可点击", () => {
    render(<TodoInput onSubmit={onSubmit} onCancel={onCancel} />)
    const input = screen.getByRole("textbox")
    const button = screen.getByRole("button")
    fireEvent.change(input, { target: { value: "任务" } })
    expect(button).not.toBeDisabled()
  })

  it("输入无效内容时，按钮应禁用", () => {
    render(<TodoInput onSubmit={onSubmit} onCancel={onCancel} />)
    const input = screen.getByRole("textbox")
    const button = screen.getByRole("button")
    fireEvent.change(input, { target: { value: "   " } })
    expect(button).toBeDisabled()
  })

  // 3. 提交行为
  it("输入有效内容并点击按钮，应调用 onSubmit 并清空输入框", () => {
    render(<TodoInput onSubmit={onSubmit} onCancel={onCancel} />)
    const input = screen.getByRole("textbox")
    const button = screen.getByRole("button")
    fireEvent.change(input, { target: { value: "任务" } })
    fireEvent.click(button)
    expect(onSubmit).toHaveBeenCalledWith("任务")
    expect(input).toHaveValue("")
  })

  it("输入有效内容并按 Enter，应调用 onSubmit 并清空输入框", () => {
    render(<TodoInput onSubmit={onSubmit} onCancel={onCancel} />)
    const input = screen.getByRole("textbox")
    fireEvent.change(input, { target: { value: "任务" } })
    fireEvent.keyDown(input, { key: "Enter" })
    expect(onSubmit).toHaveBeenCalledWith("任务")
    expect(input).toHaveValue("")
  })

  // 4. 取消行为
  it("输入内容后按 ESC，应调用 onCancel 并清空输入框", () => {
    render(<TodoInput onSubmit={onSubmit} onCancel={onCancel} />)
    const input = screen.getByRole("textbox")
    fireEvent.change(input, { target: { value: "任务" } })
    fireEvent.keyDown(input, { key: "Escape" })
    expect(onCancel).toHaveBeenCalled()
    expect(input).toHaveValue("")
  })

  // 5. 自定义属性
  it("可以自定义按钮文本和输入框 placeholder", () => {
    render(
      <TodoInput
        buttonText="提交"
        placeholder="请输入任务"
        onSubmit={onSubmit}
        onCancel={onCancel}
      />
    )
    expect(screen.getByText("提交")).toBeInTheDocument()
    expect(screen.getByPlaceholderText("请输入任务")).toBeInTheDocument()
  })

  // 6. 最大长度限制
  it("输入内容超过 maxLength 时，输入框内容应被截断", () => {
    render(<TodoInput maxLength={5} onSubmit={onSubmit} onCancel={onCancel} />)
    const input = screen.getByRole("textbox")
    fireEvent.change(input, { target: { value: "12345" } })
    expect(input).toHaveValue("12345")
  })

  // 7. ref 方法测试
  it("通过 ref 调用 focus 方法应该聚焦输入框", () => {
    const ref = React.createRef()
    render(<TodoInput ref={ref} onSubmit={onSubmit} onCancel={onCancel} />)

    const input = screen.getByRole("textbox")
    const focusSpy = vi.spyOn(input, "focus")

    // 调用 ref 的 focus 方法
    ref.current.focus()

    expect(focusSpy).toHaveBeenCalled()
    focusSpy.mockRestore()
  })

  it("通过 ref 调用 clear 方法应该清空输入框", () => {
    const ref = React.createRef()
    render(<TodoInput ref={ref} onSubmit={onSubmit} onCancel={onCancel} />)

    const input = screen.getByRole("textbox")

    // 先输入一些内容
    fireEvent.change(input, { target: { value: "测试内容" } })
    expect(input).toHaveValue("测试内容")

    // 调用 ref 的 clear 方法
    React.act(() => {
      ref.current.clear()
    })

    expect(input).toHaveValue("")
  })

  // 8. onCancel 可选参数测试
  it("当 onCancel 未提供时，按 ESC 应该只清空输入框", () => {
    render(<TodoInput onSubmit={onSubmit} />)
    const input = screen.getByRole("textbox")

    fireEvent.change(input, { target: { value: "任务" } })
    fireEvent.keyDown(input, { key: "Escape" })

    expect(input).toHaveValue("")
  })
})
