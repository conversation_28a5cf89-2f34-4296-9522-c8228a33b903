import js from "@eslint/js";
import prettierConfig from "eslint-config-prettier";
import eslintPluginPrettier from "eslint-plugin-prettier";
import { defineConfig } from "eslint/config";

export default defineConfig([
  // 忽略文件
  {
    ignores: [
      "node_modules",
      "dist",
      "**/*-min.js",
      "**/*.min.js",
      "src/**/*.d.ts",
      "package.json",
      ".gitignore",
      ".npmignore",
      ".prettierrc",
      ".prettierignore",
      ".editorconfig"
    ]
  },
  // JavaScript 推荐规则
  js.configs.recommended,

  // 插件及规则
  {
    plugins: {
      prettier: eslintPluginPrettier
    },
    rules: {
      "no-unused-vars": "warn",
      "no-undef": "warn",
      "prettier/prettier": "error" // 开启 prettier 报错规则
    }
  },

  // 关闭与 Prettier 冲突的 ESLint 规则
  prettierConfig
]);
