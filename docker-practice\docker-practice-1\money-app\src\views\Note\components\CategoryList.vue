<script setup lang="ts">
import { defineProps } from 'vue'
import { computed } from 'vue'
import CollectData from '@/data/collect.json'
import PayData from '@/data/pay.json'
import { useMoney } from '@/hooks/useMoney'

const { tag, activateTag, category } = useMoney()

const props = defineProps(['activeTab'])
const showData = computed(() => {
  return props.activeTab === 'collect' ? CollectData : PayData
})

const handleActive = (name: string, idx: number) => {
  tag.value = name
  activateTag.value = idx
}
</script>
<template>
  <div>
    <div v-for="(item, index) in showData" :key="index" class="list-tag">
      <span
        :class="[
          'tag',
          {
            colActive: activateTag === item['id'] && category === 'collect'
          },
          {
            payActive: activateTag === item['id'] && category === 'pay'
          }
        ]"
        @click="handleActive(item['name'], item['id'])"
        >{{ item['name'] }}</span
      >
    </div>
  </div>
</template>

<style scoped>
.list-tag {
  height: 50px;
  width: 25%;
  display: inline-block;
  font-size: 13px;
  text-align: center;
  line-height: 50px;
}
.list-tag span {
  border-radius: 10px;
}
.tag {
  padding: 10px;
  background-color: #f4f4f5;
  border-color: #e9e9eb;
  color: #909399;
  line-height: 30px;
  font-size: 12px;
  cursor: pointer;
}
.colActive {
  color: #409eff;
  background-color: #ecf5ff;
  border-color: #d9ecff;
}
.payActive {
  background-color: #fef0f0;
  border-color: #fde2e2;
  color: #f56c6c;
}
</style>
