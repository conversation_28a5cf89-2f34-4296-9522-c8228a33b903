//导入ai配置
const aiConfig = require("../config/ai")
const { env } = require("../config/index")
const aiServiceNonStream = require("../services/aiServiceNonStream")

console.log("🚀 ~~ 非流式环境 🤖--EndLog--🤖", env)

/**
 * 非流式调用AI生成思维导图
 * @param {*} ctx
 */
const generateMindMapNonStream = async (ctx) => {
  const { prompt } = ctx.request.body || ctx.request.query
  console.log("🚀 ~~ generateMindMapNonStream ~~ prompt 🤖--EndLog--🤖", prompt)

  const trimmedPrompt = prompt ? prompt.trim() : ""

  console.log(
    "🚀 ~~ generateMindMapNonStream ~~ trimmedPrompt 🤖--EndLog--🤖",
    trimmedPrompt
  )

  // 验证参数
  if (!trimmedPrompt) {
    ctx.status = 400
    ctx.body = {
      code: 400,
      message: "prompt参数不能为空",
    }
    return
  }

  try {
    console.log("🚀 开始发送非流式AI请求...")

    // 发送非流式AI请求
    const result = await aiServiceNonStream.sendNonStreamAIRequest(
      trimmedPrompt
    )

    if (result.success) {
      console.log("🎉 非流式AI请求成功!")
      ctx.body = {
        code: 1000,
        message: "思维导图生成成功",
        data: result.data,
        rawContent: result.rawContent,
      }
    } else {
      console.error("❌ 非流式AI请求失败:", result.error)
      ctx.status = 500
      ctx.body = {
        code: 500,
        message: result.error,
        details: result.details || result.rawContent,
      }
    }
  } catch (error) {
    console.error("非流式AI请求异常:", error)
    ctx.status = 500
    ctx.body = {
      code: 500,
      message: "AI请求异常",
      details: error.message,
    }
  }
}

module.exports = {
  generateMindMapNonStream,
}
