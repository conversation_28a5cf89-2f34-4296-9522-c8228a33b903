import React from "react"
import { MealSection } from "../../components/MealSection"

const Dinner = ({ mealData, mealTime }) => {
  // 检查是否有数据
  if (!mealData || mealData.length === 0) {
    return (
      <div className="main-page">
        <div className="eat-time">用餐时间 {mealTime}</div>
        <p>暂无晚餐数据</p>
      </div>
    )
  }

  return (
    <div className="main-page">
      <div className="eat-time">用餐时间 {mealTime}</div>
      {/* 遍历每个档口 */}
      {mealData.map((section, index) => (
        <MealSection key={index} section={section} index={index} />
      ))}
    </div>
  )
}

export default Dinner
