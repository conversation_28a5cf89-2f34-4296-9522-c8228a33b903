/**
 * 验证待办项文本
 * @param {string} text - 待验证的文本
 * @returns {boolean} - 是否有效
 */
export function validateTodoText(text) {
  if (!text || typeof text !== "string") return false
  const trimmed = text.trim()
  return trimmed.length > 0 && trimmed.length <= 200
}

/**
 * 生成唯一的待办项ID
 * @returns {number} - 唯一ID
 */
export function generateTodoId() {
  return Date.now() + Math.random()
}

/**
 * 格式化待办项创建时间
 * @param {Date} date - 日期对象
 * @returns {string} - 格式化的时间字符串
 */
// export function formatTodoDate(date) {
//   if (!date) return ''
//   return new Intl.DateTimeFormat('zh-CN', {
//     year: 'numeric',
//     month: '2-digit',
//     day: '2-digit',
//     hour: '2-digit',
//     minute: '2-digit'
//   }).format(date)
// }
export function formatTodoDate(date) {
  if (!date) return ""
  const d = typeof date === "number" ? new Date(date) : date
  if (!(d instanceof Date) || isNaN(d.getTime())) return ""
  return new Intl.DateTimeFormat("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  }).format(d)
}
/**
 * 过滤待办项
 * @param {Array} todos - 待办项数组
 * @param {string} filter - 过滤条件 ('all', 'active', 'completed')
 * @returns {Array} - 过滤后的数组
 */
export function filterTodos(todos, filter = "all") {
  switch (filter) {
    case "active":
      return todos.filter((todo) => !todo.completed)
    case "completed":
      return todos.filter((todo) => todo.completed)
    default:
      return todos
  }
}

/**
 * 排序待办项
 * @param {Array} todos - 待办项数组
 * @param {string} sortBy - 排序方式 ('created', 'text', 'completed')
 * @returns {Array} - 排序后的数组
 */
export function sortTodos(todos, sortBy = "created") {
  const sorted = [...todos]

  switch (sortBy) {
    case "text":
      return sorted.sort((a, b) => a.text.localeCompare(b.text))
    case "completed":
      return sorted.sort((a, b) => {
        if (a.completed === b.completed) return 0
        return a.completed ? 1 : -1
      })
    case "created":
    default:
      return sorted.sort((a, b) => {
        const dateA = a.createdAt ? new Date(a.createdAt) : new Date(0)
        const dateB = b.createdAt ? new Date(b.createdAt) : new Date(0)
        return dateB - dateA
      })
  }
}

/**
 * 统计待办项信息
 * @param {Array} todos - 待办项数组
 * @returns {Object} - 统计信息
 */
export function getTodoStats(todos) {
  const total = todos.length
  const completed = todos.filter((todo) => todo.completed).length
  const active = total - completed
  const percentage = total > 0 ? Math.round((completed / total) * 100) : 0

  return {
    total,
    completed,
    active,
    percentage,
  }
}
