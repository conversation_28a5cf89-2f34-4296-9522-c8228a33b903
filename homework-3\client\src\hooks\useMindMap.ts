import {
  addChildNode as addChildNodeAction,
  addParentNode as addParentNodeAction,
  addSiblingNode as addSiblingNodeAction,
  createNodeDirectly as createNodeDirectlyAction,
  deleteNode as deleteNodeAction,
  deleteNodeWith<PERSON>hildren as deleteNodeWithChildrenAction,
  importMindMap as importMindMapAction,
  recalculateLayout as recalculateLayoutAction,
  resetMindMap as resetMindMapAction,
  setSelectedNode,
  toggleNodeCollapse as toggleNodeCollapseAction,
  updateNode as updateNodeAction
} from "../store"
import { useAppDispatch, useAppSelector } from "../store/hooks"
import type { NodeStyle } from "../types/mindmap"

export const useMindMap = () => {
  const dispatch = useAppDispatch()
  const mindMapNodes = useAppSelector((state) => state.mindMap.nodes)
  const selectedNodeId = useAppSelector((state) => state.mindMap.selectedNodeId)

  // 添加子节点
  const addChildNode = (parentId: string) => {
    const parent = mindMapNodes[parentId]
    if (!parent) return // 移除层级限制

    dispatch(addChildNodeAction(parentId))

    // 注意：Redux actions不能返回值，新节点ID会通过Redux状态更新
    // 调用方应该通过selectedNodeId来获取新添加的节点
    return undefined
  }

  // 添加同级节点
  const addSiblingNode = (nodeId: string) => {
    const node = mindMapNodes[nodeId]
    if (!node || !node.parentId) return // 根节点没有同级

    dispatch(addSiblingNodeAction(nodeId))
    return undefined
  }

  // 添加父节点
  const addParentNode = (nodeId: string) => {
    const node = mindMapNodes[nodeId]
    if (!node || node.id === "root") return // 根节点不能添加父节点

    dispatch(addParentNodeAction(nodeId))
    return undefined
  }

  // 删除节点
  const deleteNode = (nodeId: string) => {
    if (nodeId === "root") return // 不能删除根节点
    dispatch(deleteNodeAction(nodeId))
  }

  // 键盘删除节点（根据是否有子节点决定删除行为）
  const deleteNodeWithChildren = (nodeId: string) => {
    if (nodeId === "root") return // 不能删除根节点
    dispatch(deleteNodeWithChildrenAction(nodeId))
  }

  // 更新节点文本和样式
  const updateNode = (nodeId: string, text: string, style?: NodeStyle) => {
    dispatch(updateNodeAction({ nodeId, text, style }))
  }

  // 设置选中节点
  const setSelectedNodeId = (nodeId: string | null) => {
    dispatch(setSelectedNode(nodeId))
  }

  // 直接创建节点（用于AI生成）
  const createNodeDirectly = (
    id: string,
    text: string,
    level: number,
    parentId: string
  ) => {
    dispatch(createNodeDirectlyAction({ id, text, level, parentId }))
  }

  // 快速创建节点（用于AI流式生成，不立即重新计算布局）
  const createNodeFast = async (
    id: string,
    text: string,
    level: number,
    parentId: string
  ) => {
    // 动态导入 createNodeFast action
    const { createNodeFast: createNodeFastAction } = await import("../store/mindMapSlice")
    dispatch(createNodeFastAction({ id, text, level, parentId }))
  }

  // 重置思维导图
  const resetMindMap = () => {
    dispatch(resetMindMapAction())
  }

  // 导入思维导图

  const importMindMap = (importedNodes: Record<string, any>) => {
    // 验证导入的数据格式
    if (
      importedNodes &&
      typeof importedNodes === "object" &&
      importedNodes.root
    ) {
      dispatch(importMindMapAction(importedNodes))
      return true
    }
    return false
  }

  // 重新计算布局
  const recalculateLayout = () => {
    dispatch(recalculateLayoutAction())
  }

  // 切换节点折叠状态
  const toggleNodeCollapse = (nodeId: string) => {
    dispatch(toggleNodeCollapseAction(nodeId))
  }

  return {
    mindMapNodes,
    selectedNodeId,
    setSelectedNodeId,
    addChildNode,
    addSiblingNode,
    addParentNode,
    deleteNode,
    deleteNodeWithChildren,
    createNodeDirectly,
    createNodeFast,
    updateNode,
    recalculateLayout,
    resetMindMap,
    importMindMap,
    toggleNodeCollapse
  }
}
