import React, { useRef, useState } from 'react'
import { UpOutlined, DownOutlined } from '@ant-design/icons'
import './style.css'

export interface NumberPickerProps {
  value: number
  min?: number
  max?: number
  step?: number
  onChange: (value: number) => void
  width?: number | string
  formatter?: (value: number) => React.ReactNode
  upIcon?: React.ReactNode
  downIcon?: React.ReactNode
  disabled?: boolean
  placeholder?: string
  className?: string
  style?: React.CSSProperties
}

const NumberPicker: React.FC<NumberPickerProps> = ({
  value,
  min,
  max,
  step = 1,
  onChange,
  width = 60,
  formatter,
  upIcon,
  downIcon,
  disabled,
  placeholder,
  className = '',
  style,
}) => {
  const wrapperRef = useRef<HTMLDivElement>(null)
  const [hover, setHover] = useState(false)

  const handleStep = (up: boolean) => {
    if (disabled) return
    const v = typeof value === 'number' ? value : 0
    let next = up ? v + Number(step) : v - Number(step)
    if (typeof min === 'number') next = Math.max(next, min)
    if (typeof max === 'number') next = Math.min(next, max)
    onChange(next)
  }

  const handleInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const val = e.target.value
    if (/^-?\d*$/.test(val)) {
      let num = Number(val)
      if (val === '') num = 0
      if (typeof min === 'number') num = Math.max(num, min)
      if (typeof max === 'number') num = Math.min(num, max)
      onChange(num)
    }
  }

  return (
    <div
      ref={wrapperRef}
      className={`number-picker-wrapper${disabled ? ' disabled' : ''} ${className}`}
      style={{ width, ...style }}
      onMouseEnter={() => setHover(true)}
      onMouseLeave={() => setHover(false)}
    >
      {hover && !disabled && (
        <>
          <div
            className="number-picker-arrow up"
            onMouseDown={(e) => {
              e.preventDefault()
              handleStep(true)
            }}
          >
            {upIcon || <UpOutlined className="number-picker-icon" />}
          </div>
          <div
            className="number-picker-arrow down"
            onMouseDown={(e) => {
              e.preventDefault()
              handleStep(false)
            }}
          >
            {downIcon || <DownOutlined className="number-picker-icon" />}
          </div>
        </>
      )}
      <input
        type="text"
        className="number-picker-input"
        value={
          formatter ? String(formatter(value)) : String(value).padStart(2, '0')
        }
        onChange={handleInput}
        disabled={disabled}
        placeholder={placeholder}
        style={{
          zIndex: 1,
          position: 'relative',
          width: '100%',
          textAlign: 'center',
          fontSize: 24,
          color: '#888',
          background: 'transparent',
          border: 'none',
          outline: 'none',
        }}
      />
    </div>
  )
}

export default NumberPicker
