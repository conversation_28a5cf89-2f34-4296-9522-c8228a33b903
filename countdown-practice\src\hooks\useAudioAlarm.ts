import { useEffect, useRef } from 'react'

export function useAudioAlarm(src: string) {
  const audioRef = useRef<HTMLAudioElement>()

  // 只在首次挂载时创建 audio 元素
  useEffect(() => {
    audioRef.current = new window.Audio(src)
    return () => {
      if (audioRef.current) {
        audioRef.current.pause()
        audioRef.current.currentTime = 0
      }
    }
  }, [src])

  // 播放音频
  const play = () => {
    if (audioRef.current) {
      audioRef.current.currentTime = 0
      // 返回 promise 以便调试
      audioRef.current.play().catch((e) => {
        console.warn('音频播放被拦截', e)
      })
    }
  }

  // 暂停音频
  const pause = () => {
    if (audioRef.current) {
      audioRef.current.pause()
      audioRef.current.currentTime = 0
    }
  }

  // 设置静音
  const setMuted = (muted: boolean) => {
    if (audioRef.current) {
      audioRef.current.muted = muted
    }
  }

  return { play, pause, setMuted }
}