<template>
  <div v-if="visible" class="message-popup" :style="popupStyle">
    <div class="message-popup-content">
      <span>{{ message }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, computed, watch } from 'vue'

const props = defineProps({
  message: {
    type: String,
    required: true
  },
  duration: {
    type: Number,
    default: 1000
  },
  style: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['close'])

const visible = ref(false)
const popupStyle = computed(() => props.style)

// 控制是否自动关闭
const autoClose = computed(() => props.duration > 0)

const closePopup = () => {
  visible.value = false
  emit('close')
}

watch(
  () => props.message,
  (newVal) => {
    if (newVal) {
      visible.value = true
      if (autoClose.value) {
        setTimeout(() => {
          if (visible.value) {
            closePopup()
          }
        }, props.duration)
      }
    }
  },
  { immediate: true }
)
</script>

<style scoped>
.message-popup {
  position: fixed;
  bottom: 300px;
  left: 50%;
  transform: translateX(-50%);
  background: #333;
  color: #fff;
  padding: 15px 20px;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  z-index: 1000;
}

.message-popup-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}
</style>
