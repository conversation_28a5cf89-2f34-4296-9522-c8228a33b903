server {
    listen 80;
    listen  [::]:80;
    server_name *************; 


    root /usr/share/nginx/html;
    index index.html index.htm;

    # 开启gzip压缩
    gzip on;
    gzip_min_length 1k;
    gzip_comp_level 6;
    gzip_types text/plain text/css text/javascript application/json application/javascript application/x-javascript application/xml;
    gzip_vary on;

   # 主页面路由配置，支持React Router
    location / {
        root /usr/share/nginx/html;  # 假设你打包文件放在这个目录
        index index.html;
        try_files $uri $uri/ /index.html;
    }
   
   # 静态资源缓存配置
    location /assets {
        expires 1y;
        add_header Cache-Control "public, no-transform";
    }
    
    # 其他静态资源缓存配置
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, no-transform";
    }
    # 代理API请求到后端服务器
    location /api/ {
        rewrite ^/api/(.*)$ /$1 break;
        proxy_pass https://zhweb.kingsoft.com;
        proxy_set_header Host zhweb.kingsoft.com;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }


    # 错误页面配置
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }



}