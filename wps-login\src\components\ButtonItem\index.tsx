import type { ReactNode } from "react";
import "./index.css";
interface ButtonItemProps {
  icon?: ReactNode;
  title: string;
}
function ButtonItem(props: ButtonItemProps) {
  const { icon, title } = props;
  return (
    <div className="btn-item">
      <div className="btn-item-icon">
        {typeof icon === "string" ? (
          <span dangerouslySetInnerHTML={{ __html: icon }} />
        ) : (
          icon
        )}
      </div>
      <div className="btn-item-divider" />
      <div className="btn-item-title">{title}</div>
    </div>
  );
}
export default ButtonItem;
