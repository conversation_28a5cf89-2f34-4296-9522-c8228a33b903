import { useState, useEffect } from "react";
import { MOBILE_BREAKPOINT } from "../constants";

/**
 * 自定义hook：检测是否为移动端
 * @returns {boolean} isMobile - 是否为移动端
 */
export const useMobile = (): boolean => {
  const [isMobile, setIsMobile] = useState(() => {
    return (
      typeof window !== "undefined" && window.innerWidth < MOBILE_BREAKPOINT
    );
  });

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return isMobile;
};
