// 主组件
export { Header } from "./Header"

// 子组件
export { FileControls } from "./views/FileControls"
export { TabNavigation } from "./views/TabNavigation"
export { UserControls } from "./views/UserControls"

// 常量和工厂函数
export {
  createFileActions,
  createFileOperations,
  createLocationOperations,
  tabConfig
} from "./config/constants"

// 类型
export type {
  ActionItem,
  FileControlsProps,
  FileInfo,
  HeaderProps,
  IconActionItem,
  TabItem,
  TabNavigationProps
} from "./type/types"

// 工具函数
export { getIconComponent } from "./icon/iconUtils"
