import "./index.css"
interface AnswerItemProp {
  question: string
  answer: string
  userAnswer: string
}
function AnswerItem(prop: AnswerItemProp) {
  const { question, answer, userAnswer } = prop
  return (
    <div className="answer-item">
      <p className="answer-item-question">{question}</p>
      <div className="answer-item-content">
        <p className="user-answer">
          你的答案:
          <span>{userAnswer ? userAnswer : "未答"}</span>
        </p>
        <p className="user-answer-correct">
          正确答案:
          <span>{answer}</span>
        </p>
      </div>
    </div>
  )
}
export default AnswerItem
