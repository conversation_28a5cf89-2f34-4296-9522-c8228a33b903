{"name": "code_tools", "version": "1.0.0", "description": "代理", "main": "index.js", "scripts": {"start": "node index.js", "prettier": "prettier -c --write index.js", "test": "echo \"Error: no test specified\" && exit 1", "prepare": "husky install"}, "author": "", "license": "ISC", "dependencies": {"axios": "^1.10.0"}, "devDependencies": {"@commitlint/config-conventional": "^19.8.1", "@eslint/js": "^9.31.0", "commitlint": "^19.8.1", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "globals": "^16.3.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.6.2"}, "lint-staged": {"**/*.{js,ts,jsx,tsx}": ["prettier --write", "eslint --fix", "git add"]}}