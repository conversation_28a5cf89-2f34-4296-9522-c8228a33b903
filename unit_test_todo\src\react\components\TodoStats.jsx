import React from 'react'
import './TodoStats.css'

const TodoStats = ({ stats, onClearCompleted }) => {
  const defaultStats = {
    total: 0,
    active: 0,
    completed: 0,
    percentage: 0
  }

  const currentStats = stats || defaultStats

  return (
    <div className="todo-stats">
      <div className="stats-info">
        <span className="stat-item">
          总计: {currentStats.total}
        </span>
        <span className="stat-item">
          进行中: {currentStats.active}
        </span>
        <span className="stat-item">
          已完成: {currentStats.completed}
        </span>
        {currentStats.total > 0 && (
          <span className="stat-item">
            完成率: {currentStats.percentage}%
          </span>
        )}
      </div>
      
      <div className="stats-actions">
        <button 
          onClick={onClearCompleted}
          disabled={currentStats.completed === 0}
          className="clear-btn"
        >
          清除已完成
        </button>
      </div>
    </div>
  )
}

export default TodoStats 