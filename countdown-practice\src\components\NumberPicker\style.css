.number-picker-wrapper {
  position: relative;
  display: inline-block;
  padding-top: 24px;
  padding-bottom: 24px;
}

.number-picker-wrapper.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.number-picker-arrow {
  position: absolute;
  left: 0;
  width: 100%;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.95);
  z-index: 2;
  cursor: pointer;
  transition: background 0.2s;
  user-select: none;
}

.number-picker-arrow.up {
  top: 0;
  border-radius: 8px 8px 0 0;
}

.number-picker-arrow.down {
  bottom: 0;
  border-radius: 0 0 8px 8px;
}

.number-picker-icon {
  font-size: 16px;
  color: #888;
}

.number-picker-input {
  width: 100%;
  text-align: center;
  font-size: 24px;
  color: #888;
  background: transparent;
  z-index: 1;
  border: none;
  outline: none;
}
