import { useEffect, useRef, useState } from 'react'
import { useAudioAlarm } from './useAudioAlarm'


/**
 * 倒计时核心逻辑 Hook
 * @param alarmSrc 音频文件路径
 */
export function useCountdownTimer(alarmSrc: string) {
  const [showCountdown, setShowCountdown] = useState(false)
  const [leftSeconds, setLeftSeconds] = useState(0)
  const [ring, setRing] = useState(false)
  const [ended, setEnded] = useState(false)
  const timerRef = useRef<ReturnType<typeof setInterval> | null>(null)
  // 使用音频 Hook
  const audio = useAudioAlarm(alarmSrc)

  // 倒计时主循环
  useEffect(() => {
    if (showCountdown && leftSeconds > 0 && !ended) {
      timerRef.current = setInterval(() => {
        setLeftSeconds((prev) => {
          if (prev <= 1) {
            clearInterval(timerRef.current!)
            setEnded(true)
            return 0
          }
          return prev - 1
        })
      }, 1000)
    }
    return () => {
      if (timerRef.current) clearInterval(timerRef.current)
    }
  }, [showCountdown, ended, leftSeconds])

  // 处理倒计时归零时的结束状态
  useEffect(() => {
    if (leftSeconds === 0 && showCountdown && !ended) {
      setEnded(true)
    }
  }, [leftSeconds, showCountdown, ended])

  // 响铃控制
  useEffect(() => {
    if (ended && ring) {
      audio.play()
    } else if (!ring) {
      audio.pause()
    }
  }, [ended, ring])

  // 开始倒计时
  const start = (totalSeconds: number, shouldRing: boolean) => {
    setLeftSeconds(totalSeconds)
    setShowCountdown(true)
    setRing(shouldRing)
    setEnded(false)
  }

  // 关闭倒计时
  const close = () => {
    setShowCountdown(false)
    setLeftSeconds(0)
    setRing(false)
    audio.pause()
    if (timerRef.current) clearInterval(timerRef.current)
  }

  // 切换响铃
  const toggleRing = (val: boolean) => {
    setRing(val)
    audio.setMuted(!val)
    if (!val) {
      audio.pause()
    }
  }

  return {
    showCountdown,
    leftSeconds,
    ring,
    ended,
    start,
    close,
    toggleRing,
  }
}
