# CSS 学习笔记

## min-width|max-width

1. min-width：缩小到指定值就不在缩小了
2. max-width:可以扩展到多宽就不变大了

   应用场景：适合防止屏幕太小文字被压扁或太宽内容太散

## 怪异盒子模型（box-sizing: border-box）问题分析

### 问题描述

在 `border.html` 中，当 `.border2` 设置为怪异盒子模型后，内部的 `<p>` 元素会溢出容器。

### 问题原因

#### 1. 怪异盒子模型的特点

- `box-sizing: border-box` 使得 `width` 和 `height` 包含内容、padding 和 border 的总尺寸
- 与标准盒子模型不同，padding 和 border 不会增加元素的总尺寸

#### 2. 具体计算过程

```css
.border2 {
  box-sizing: border-box;
  height: 20px; /* 总高度 */
  padding: 10px; /* 上下各10px，共20px */
  border: solid 1px; /* 上下各1px，共2px */
}
```

**实际内容区域高度计算：**

- 总高度：20px
- Border 占用：2px（上下各 1px）
- Padding 占用：20px（上下各 10px）
- **剩余内容空间：20px - 2px - 20px = -2px**（负数！）

#### 3. p 元素的空间需求

- p 元素有默认的 margin（通常上下各 16px）
- 加上文字本身的行高
- 总共需要约 23px 的空间

#### 4. 溢出原因

由于容器实际内容区域是 **-2px**（负数），而 p 元素需要 **23px** 的空间，导致元素无法完全容纳在容器内，产生溢出。

### 解决方案

#### 方案 1：增加容器高度

```css
.border2 {
  height: 60px; /* 增加高度以容纳内容 */
}
```

#### 方案 2：重置 p 元素默认样式

```css
.border2 p {
  margin: 0; /* 移除默认margin */
}
```

#### 方案 3：减少 padding 值

```css
.border2 {
  padding: 5px; /* 减少padding */
}
```

### 提示

使用 `box-sizing: border-box` 时，必须确保：
**padding + border 的总和 ≤ 设定的 width/height 值**

否则会导致内容区域为负数，造成内容溢出。

## 浮动清除

## Flex布局——弹性布局

### 注意事项

1. 使用display:flex后，宽度由内容决定，就算是块级元素也不行
2. 使用flex容器必须设置父元素

### 基本属性

1. flex-direaction主轴方向

![1752825597679](image/readme/1752825597679.png)

2. justify-content主轴对齐方式

   主轴默认为为row（水平）

* flex-start
* flex-end
* center
* flex-between
* space-around
* space-evenly 所有间距相等

3. align-items交叉轴（默认一行）

   align-items: stretch | flex-start | flex-end | center | baseline;
   4. flex-wrap是否换行
   5. align-content：多行情况下交叉对齐方式

### 子元素属性

## picture、video元素

## 移动端响应式布局
