import React from "react"
import { startToolbarConfig } from "../config/startToolbarConfig"
import type { ToolbarSectionProps } from "../type/types"
import { ToolbarRenderer } from "../utils/ToolbarRenderer"

import "../../common/css/header.css"

export const StartToolbar: React.FC<ToolbarSectionProps> = (props) => {
  return (
    <ToolbarRenderer
      {...props}
      config={startToolbarConfig}
      ariaLabel="样式工具栏"
      separatorClassName="toolbar-separator-vertical"
    />
  )
}
