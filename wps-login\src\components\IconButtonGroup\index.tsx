import React from "react";
import { useMobile } from "../../hooks/useMobile";
import IconButton, { type IconButtonProps } from "../IconButton";
import "./index.css";

export interface IconButtonGroupProps {
  /** 按钮配置列表 */
  buttons: IconButtonProps[];
  /** 分隔符文本 */
  dividerText?: string;
  /** 自定义CSS类名 */
  className?: string;
  /** 移动端每行显示的按钮数量 */
  mobileColumns?: number;
  /** PC端每行显示的按钮数量 */
  desktopColumns?: number;
}

const IconButtonGroup: React.FC<IconButtonGroupProps> = ({
  buttons,
  dividerText = "或",
  className = "",
  mobileColumns = 5,
  desktopColumns = 6
}) => {
  const isMobile = useMobile();
  const columns = isMobile ? mobileColumns : desktopColumns;

  return (
    <div className={`icon-button-group ${className}`}>
      {dividerText && (
        <div className="icon-button-group__divider">{dividerText}</div>
      )}

      <div
        className="icon-button-group__container"
        style={{
          gridTemplateColumns: `repeat(${columns}, 1fr)`
        }}
      >
        {buttons.map((buttonProps) => (
          <IconButton
            key={buttonProps.id}
            {...buttonProps}
            className={`icon-button-group__item ${buttonProps.className || ""}`}
          />
        ))}
      </div>
    </div>
  );
};

export default IconButtonGroup;
