/* 样式已迁移到NumberPicker/style.css，仅保留兼容性 */

.custom-inputnumber-wrapper {
  position: relative;
  display: inline-block;
  padding-top: 24px;
  padding-bottom: 24px;
}

.custom-inputnumber-arrow {
  position: absolute;
  left: 0;
  width: 100%;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.95);
  z-index: 2;
  cursor: pointer;
  transition: background 0.2s;
  user-select: none;
}

.custom-inputnumber-arrow.up {
  top: 0;
  border-radius: 8px 8px 0 0;
}

.custom-inputnumber-arrow.down {
  bottom: 0;
  border-radius: 0 0 8px 8px;
}

.custom-inputnumber-icon {
  font-size: 16px;
  color: #888;
}

.custom-hide-handler .ant-input-number-handler-wrap {
  display: none !important;
}
