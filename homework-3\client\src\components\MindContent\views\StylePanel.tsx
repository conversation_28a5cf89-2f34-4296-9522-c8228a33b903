import {
  FontBoldIcon,
  FontItalicIcon,
  ListBulletIcon,
  StrikethroughIcon,
  TextIcon,
  UnderlineIcon
} from "@radix-ui/react-icons"
import * as Toggle from "@radix-ui/react-toggle"
import * as Toolbar from "@radix-ui/react-toolbar"
import type { NodeStyle } from "../../../types/mindmap"
import { ColorPicker } from "../../common/ColorPicker"
import { BackgroundIcon } from "../../icons/BackgroundIcon"
import { ColorIcon } from "../../icons/ColorIcon"

interface StylePanelProps {
  nodeId: string
  nodeX: number
  nodeY: number
  style: NodeStyle
  onStyleChange: (style: NodeStyle) => void
}

const StylePanelComponent = ({
  nodeId: _nodeId,
  nodeX,
  nodeY,
  style,
  onStyleChange
}: StylePanelProps) => {
  const updateStyle = (updates: Partial<NodeStyle>) => {
    const newStyle = { ...style, ...updates }
    onStyleChange(newStyle)
  }

  return (
    <div
      className="style-panel"
      style={{
        left: nodeX - 100,
        top: nodeY + 40,
        zIndex: 1001
      }}
      onClick={(e) => e.stopPropagation()}
    >
      <div className="style-panel-content">
        <Toolbar.Root className="style-toolbar">
          <Toggle.Root className="style-btn">
            <TextIcon />
          </Toggle.Root>

          {/* 粗体按钮 */}
          <Toggle.Root
            className={`style-btn ${
              style.fontWeight === "bold" ? "active" : ""
            }`}
            pressed={style.fontWeight === "bold"}
            onPressedChange={(pressed) => {
              const newWeight: "normal" | "bold" = pressed ? "bold" : "normal"

              updateStyle({ fontWeight: newWeight })
            }}
          >
            <FontBoldIcon />
          </Toggle.Root>

          {/* 斜体按钮 */}
          <Toggle.Root
            className={`style-btn ${
              style.fontStyle === "italic" ? "active" : ""
            }`}
            pressed={style.fontStyle === "italic"}
            onPressedChange={(pressed) => {
              updateStyle({
                fontStyle: pressed ? "italic" : "normal"
              })
            }}
          >
            <FontItalicIcon />
          </Toggle.Root>

          {/* 下划线按钮 */}
          <Toggle.Root
            className={`style-btn ${
              style.textDecoration === "underline" ? "active" : ""
            }`}
            pressed={style.textDecoration === "underline"}
            onPressedChange={(pressed) => {
              updateStyle({
                textDecoration: pressed ? "underline" : "none"
              })
            }}
          >
            <UnderlineIcon />
          </Toggle.Root>

          <Toggle.Root className="style-btn">
            <StrikethroughIcon />
          </Toggle.Root>

          {/* 文字颜色 */}
          <ColorPicker
            value={style.color}
            onChange={(color: string) => updateStyle({ color })}
            title="文字颜色"
            icon={<ColorIcon />}
          />

          {/* 背景颜色 */}
          <ColorPicker
            value={style.backgroundColor}
            onChange={(color: string) =>
              updateStyle({ backgroundColor: color })
            }
            title="背景颜色"
            icon={<BackgroundIcon />}
          />
          <Toggle.Root className="style-btn">
            <ListBulletIcon />
          </Toggle.Root>

          <Toggle.Root className="style-btn">
            <ListBulletIcon />
          </Toggle.Root>
        </Toolbar.Root>
      </div>
    </div>
  )
}

export const StylePanel = StylePanelComponent
