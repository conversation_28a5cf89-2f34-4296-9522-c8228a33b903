const crypto = require("crypto")
const axios = require("axios")
const aiConfig = require("../config/ai")
const { NON_STREAM_PROMPT } = require("../config/prompt")
/**
 * 生成唯一的Action ID
 */
function generateActionId() {
  return crypto.randomBytes(16).toString("hex")
}

/**
 * 构建非流式AI请求数据
 * @param {string} prompt - 用户输入的提示词
 * @returns {string} JSON格式的请求数据
 */
function buildNonStreamAIRequest(prompt) {
  console.log("🚀 ~~ buildNonStreamAIRequest ~~ prompt 🤖--EndLog--🤖", prompt)
  return JSON.stringify({
    messages: [
      {
        role: "system",
        content: NON_STREAM_PROMPT,
      },
      {
        role: "user",
        content: `请为以下主题生成思维导图：${prompt}`,
      },
    ],
    stream: false, // 非流式请求
    model: aiConfig.modle_name,
    max_tokens: 20, // 增加token限制以生成完整JSON
    temperature: 0.7, // 添加温度参数
  })
}

/**
 * 发送非流式AI请求
 * @param {string} prompt - 用户输入的提示词
 * @returns {Promise<Object>} 返回解析后的思维导图数据
 */
async function sendNonStreamAIRequest(prompt) {
  console.log(
    "🚀 ~~ sendNonStreamAIRequest ~~ prompt 🤖--EndLog--🤖",
    buildNonStreamAIRequest(prompt)
  )

  try {
    const response = await axios({
      method: "POST",
      url: `https://${aiConfig.AI_HOST_URL}/api/v3/chat/completions`,
      headers: {
        Authorization: "Bearer f7ef5aeb-dd13-4b57-875f-e827522fcbfc",
        "Content-Type": "application/json",
      },
      data: buildNonStreamAIRequest(prompt),
      timeout: 30000,
    })

    console.log("🚀 ~~ 非流式响应状态 🤖--EndLog--🤖", response.status)
    console.log("🚀 ~~ 非流式响应数据 🤖--EndLog--🤖", response.data)

    // 提取AI返回的内容
    const content = response.data.choices[0].message.content
    console.log("🚀 ~~ AI返回内容 🤖--EndLog--🤖", content)

    // 尝试解析JSON
    try {
      const mindMapData = JSON.parse(content)
      console.log("✅ JSON解析成功:", mindMapData)
      return {
        success: true,
        data: mindMapData,
        rawContent: content,
      }
    } catch (jsonError) {
      console.error("❌ JSON解析失败:", jsonError.message)
      console.log("📄 原始内容:", content)
      return {
        success: false,
        error: "JSON解析失败",
        rawContent: content,
      }
    }
  } catch (error) {
    console.error("❌ 非流式请求错误:", error.message)
    if (error.response) {
      console.error("错误状态码:", error.response.status)
      console.error("错误数据:", error.response.data)
    }
    return {
      success: false,
      error: "AI服务请求失败",
      details: error.message,
    }
  }
}

module.exports = {
  sendNonStreamAIRequest,
  buildNonStreamAIRequest,
  generateActionId,
}
