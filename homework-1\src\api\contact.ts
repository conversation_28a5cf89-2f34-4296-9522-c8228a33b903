import { pinyin } from "pinyin-pro";
import { Contact } from "../type";
import { fetchChatList } from "./router";

export interface MentionItem {
  id: number;
  value: string;
  avatars: string;
  dept?: string;
  pinyin?: string;
}

export async function getContactList(): Promise<Contact[]> {
  return await fetchChatList({
    count: 20,
    search_type: 104,
    relation_version: 1,
    search_scopes: [1, 2, 3]
  });
}

export function mapContactsToMentionList(contacts: Contact[]): MentionItem[] {
  return contacts.map((c) => {
    const name = c.name || c.remark_name;
    return {
      id: c.id,
      value: name,
      avatars: c.avatars[0],
      dept: c.department_show,
      pinyin: pinyin(name, { pattern: "first", type: "array" })
        .join("")
        .toLowerCase()
    };
  });
}
