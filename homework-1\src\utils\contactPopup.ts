import { MentionItem } from "../api/contact";

export function showContactPopup(contact: MentionItem, anchor: HTMLElement) {
  const old = document.getElementById("contact-popup");
  if (old) old.remove();

  // 创建弹窗
  const popup = document.createElement("div");
  popup.id = "contact-popup";
  popup.style.position = "absolute";
  popup.style.zIndex = "9999";
  popup.style.background = "#fff";
  popup.style.border = "1px solid #e0e0e0";
  popup.style.borderRadius = "8px";
  popup.style.boxShadow = "0 2px 8px rgba(0,0,0,0.15)";
  popup.style.padding = "16px";
  popup.style.minWidth = "220px";

  // 定位到联系人 mention 块下方
  const rect = anchor.getBoundingClientRect();
  popup.style.left = rect.left + window.scrollX + "px";
  popup.style.top = rect.bottom + window.scrollY + 6 + "px";

  // 填充内容
  popup.innerHTML = `
    <div style="display:flex;align-items:center;margin-bottom:12px;">
      <img src="${contact.avatars || ""}" style="width:40px;height:40px;border-radius:8px;margin-right:12px;background:#f37e9a;object-fit:cover;">
      <div>
        <div style="font-weight:bold;font-size:16px;">${contact.value}</div>
        <div style="color:#888;font-size:13px;">${contact.dept || ""}</div>
      </div>
    </div>
    <div style="color:#2563eb;font-size:14px;">ID: ${contact.id}</div>
    <div style="margin-top:8px;text-align:right;">
      <button id="close-contact-popup" style="padding:4px 12px;border:none;background:#2563eb;color:#fff;border-radius:4px;cursor:pointer;">关闭</button>
    </div>
  `;

  document.body.appendChild(popup);
  // 关闭按钮
  document
    .getElementById("close-contact-popup")
    ?.addEventListener("click", () => {
      popup.remove();
    });

  // 点击弹窗外关闭
  setTimeout(() => {
    const handler = (e: MouseEvent) => {
      if (popup && !popup.contains(e.target as Node)) {
        popup.remove();
        document.removeEventListener("click", handler, true);
      }
    };
    document.addEventListener("click", handler, true);
  }, 0);
}
