{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "module": "ESNext", "lib": ["ESNext", "DOM", "DOM.Iterable"], "skipLibCheck": true, "moduleResolution": "node", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "baseUrl": "./", "types": ["vite/client"], "composite": true, "allowSyntheticDefaultImports": true, "paths": {"@/*": ["src/*"]}, "typeRoots": ["node_modules/@types", "./src/types"]}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "src/**/*.json", "vite.config.ts", "src/types/**/*.d.ts", "src/react/_test_/TodoStats.test.jsx", "src/react/_test_/TodoItem.test.jsx", "src/react/_test_/TodoInput.test.jsx", "src/react/_test_/TodoApp.test.jsx", "src/react/_test_/useTodoStore.test.jsx"]}