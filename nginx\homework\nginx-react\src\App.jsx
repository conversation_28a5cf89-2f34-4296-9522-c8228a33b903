import React, { useState, useEffect } from "react"
import { <PERSON>rows<PERSON>Router as Router, Routes, Route, Link } from "react-router-dom"
import { DatePicker } from "antd"
import { DownOutlined } from "@ant-design/icons"
import { Image } from "antd"
import dayjs from "dayjs"
import weekday from "dayjs/plugin/weekday"
import isToday from "dayjs/plugin/isToday"
import { fetchMealMenu } from "./api/meal"
import { Navigate } from "react-router-dom"
import Breakfast from "./page/Breakfast"
import Lunch from "./page/Lunch"
import Dinner from "./page/Dinner"
import LogoImg from "./assets/pic1.png"
import "./App.css"

const { RangePicker } = DatePicker
dayjs.extend(weekday)
dayjs.extend(isToday)

const App = () => {
  // 初始化默认日期为当前日期
  const [selectedDate, setSelectedDate] = useState(dayjs())

  const handleChange = (date) => {
    if (date) setSelectedDate(date)
  }

  const formatDisplay = (value) => {
    if (!value) return ""
    if (value.isToday()) {
      return value.format("YYYY-MM-DD") + " 今天"
    } else {
      const weekdayText = [
        "星期日",
        "星期一",
        "星期二",
        "星期三",
        "星期四",
        "星期五",
        "星期六",
      ]
      const dayOfWeek = weekdayText[value.day()]
      return value.format("YYYY-MM-DD") + " " + dayOfWeek
    }
  }
  const [menuData, setMenuData] = useState({
    breakfast: [],
    lunch: [],
    dinner: [],
    breakfastTime: "",
    lunchTime: "",
    dinnerTime: "",
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // 当日期变化时获取菜单数据
  useEffect(() => {
    if (selectedDate) {
      const fetchMenu = async () => {
        try {
          setLoading(true)
          const formattedDate = selectedDate.format("YYYY-MM-DD")
          const data = await fetchMealMenu("WH_000", formattedDate)
          console.log("Fetched menu data:", data)
          setMenuData(data)
          setError(null)
        } catch (err) {
          setError(err.message)
        } finally {
          setLoading(false)
        }
      }

      fetchMenu()
    }
  }, [selectedDate])

  return (
    <Router>
      <div className="App">
        <div className="header">
          <div className="logo">
            <Image src={LogoImg} alt="logo" />
          </div>
          <div className="tabHeader">
            <div className="tableft">
              <Link to="/breakfast" className="nav-link">
                早餐
              </Link>
              <Link to="/lunch" className="nav-link">
                中餐
              </Link>
              <Link to="/dinner" className="nav-link">
                晚餐
              </Link>
            </div>
            <div className="tabright">
              <DatePicker
                value={selectedDate}
                onChange={handleChange}
                format={formatDisplay} // 使用函数动态格式化
                suffixIcon={
                  <DownOutlined style={{ fontSize: 12, marginLeft: 0 }} />
                }
                style={{ padding: "4px 8px", height: 32 }}
                className="custom-datepicker"
                popupStyle={{
                  position: "fixed",
                  top: "50%",
                  left: "50%",
                  transform: "translate(-50%, -50%)",
                  zIndex: 2000,
                }}
                bordered={false}
                allowClear={false}
              />
            </div>
          </div>
        </div>
        <main className="main-content">
          {loading ? (
            <div>加载中...</div>
          ) : error ? (
            <div>错误: {error}</div>
          ) : (
            <Routes>
              <Route
                path="/breakfast"
                element={
                  <Breakfast
                    mealData={menuData.breakfast}
                    mealTime={menuData.breakfastTime}
                  />
                }
              />
              <Route
                path="/lunch"
                element={
                  <Lunch
                    mealData={menuData.lunch}
                    mealTime={menuData.lunchTime}
                  />
                }
              />
              <Route
                path="/dinner"
                element={
                  <Dinner
                    mealData={menuData.dinner}
                    mealTime={menuData.dinnerTime}
                  />
                }
              />
              {/* 默认重定向到早餐 */}
              <Route path="/" element={<Navigate to="/breakfast" replace />} />
            </Routes>
          )}
        </main>
      </div>
    </Router>
  )
}

export default App
