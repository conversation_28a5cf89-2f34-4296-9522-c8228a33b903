const http = require("http")

// 获取端口号，默认为3001
const port = process.env.PORT || 3001
const serverId = process.env.SERVER_ID || "1"

// 创建HTTP服务器
const server = http.createServer((req, res) => {
  // 设置响应头
  res.setHeader("Content-Type", "application/json")
  res.setHeader("Access-Control-Allow-Origin", "*")

  // 返回固定响应
  const response = {
    code: 0,
    data: {
      srvId: serverId,
    },
  }

  // 发送响应
  res.end(JSON.stringify(response))
})

// 启动服务器
server.listen(port, () => {
  console.log(`Server with ID ${serverId} running on port ${port}`)
})
