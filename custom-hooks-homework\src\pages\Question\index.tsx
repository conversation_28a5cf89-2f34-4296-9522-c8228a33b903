import React, { useCallback, useEffect, useState } from "react"
import { useNavigate } from "react-router-dom"
import QuestionItem from "../../components/QuestionItem"
import type { questionType } from "../../type/question"
import "./index.css"

interface QuestionProp {
  questions: questionType[]
}

const Question: React.FC<QuestionProp> = ({ questions }) => {
  const navigate = useNavigate()
  const [progress, setProgress] = useState(0) // 用于存储进度

  // 获取所有答案
  const getAllAnswers = () => {
    const answers: {
      questionId: string
      questionTitle: string
      selectedOption: string | null
    }[] = []

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key?.startsWith("question-")) {
        const value = localStorage.getItem(key)
        if (value) {
          answers.push(JSON.parse(value))
        }
      }
    }
    return answers
  }

  // 计算进度并更新
  const calculateProgress = useCallback(() => {
    const answers = getAllAnswers()
    const totalAnswered = answers.filter(
      (item) => item.selectedOption !== null
    ).length
    const progress = (totalAnswered / questions.length) * 100
    setProgress(progress)
  }, [questions.length])

  // 提交按钮逻辑
  const handleSubmit = () => {
    const answers = getAllAnswers()
    navigate("/answer", { state: { answers } })
  }

  // useEffect 用于初始进度计算
  useEffect(() => {
    calculateProgress()
  }, [questions, calculateProgress])

  return (
    <div className="question">
      <h1>单选题作答</h1>
      <div className="progress-container">
        <div
          className="progress-bar"
          style={{ width: `${progress}%` }} // 动态更新宽度
        ></div>
        <span>{Math.round(progress)}% 完成</span>
      </div>
      <div className="question-container">
        {questions.map((question, index) => (
          <QuestionItem
            id={(index + 1).toString()}
            key={question.question}
            question={question.question}
            options={question.options}
            onOptionSelect={calculateProgress}
          />
        ))}
      </div>
      <button className="submit-button" onClick={handleSubmit}>
        提交答案
      </button>
    </div>
  )
}

export default Question
